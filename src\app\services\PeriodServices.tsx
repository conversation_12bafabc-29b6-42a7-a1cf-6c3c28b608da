import { PeriodSchema } from "../models/Period";
import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export interface Period {
  _id: string;
  period_number: number;
  start_time: string;
  end_time: string;
  school_id: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreatePeriodData {
  period_number: number;
  start_time: string;
  end_time: string;
}

export interface UpdatePeriodData {
  period_number?: number;
  start_time?: string;
  end_time?: string;
}

// Get all periods for a school
export async function getPeriods(schoolId: string): Promise<{
  periods: PeriodSchema[];
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching periods:", response.statusText);
      throw new Error("Failed to fetch periods");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch periods error:", error);
    throw new Error("Failed to fetch periods");
  }
}

// Get periods by school ID (alias for getPeriods)
export async function getPeriodsBySchool(schoolId: string): Promise<PeriodSchema[]> {
  const response = await getPeriods(schoolId);
  return response.periods;
}

// Create a new period
export async function createPeriod(schoolId: string, periodData: CreatePeriodData): Promise<{
  period: Period;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/school/${schoolId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(periodData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating period:", errorData);
      throw new Error(errorData.message || "Failed to create period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create period error:", error);
    throw error;
  }
}

// Update a period
export async function updatePeriod(schoolId: string, periodId: string, periodData: UpdatePeriodData): Promise<{
  period: Period;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/school/${schoolId}/${periodId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(periodData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating period:", errorData);
      throw new Error(errorData.message || "Failed to update period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update period error:", error);
    throw error;
  }
}

// Delete a period
export async function deletePeriod(schoolId: string, periodId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/school/${schoolId}/${periodId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting period:", errorData);
      throw new Error(errorData.message || "Failed to delete period");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete period error:", error);
    throw error;
  }
}

// Delete multiple periods (using legacy route)
export async function deleteMultiplePeriods(periodIds: string[]): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/delete-periods`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: periodIds }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting multiple periods:", errorData);
      throw new Error(errorData.message || "Failed to delete periods");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete multiple periods error:", error);
    throw error;
  }
}

// Delete all periods (using legacy route)
export async function deleteAllPeriods(): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/periods/delete-all-periods`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting all periods:", errorData);
      throw new Error(errorData.message || "Failed to delete all periods");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete all periods error:", error);
    throw error;
  }
}
