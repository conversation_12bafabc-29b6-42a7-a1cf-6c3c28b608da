"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import {
    getSystemSettings,
    SystemSettings,
} from "@/app/services/SystemSettingsService";
import MaintenanceMode from "./MaintenanceMode";
import CircularLoader from "./widgets/CircularLoader";

interface MaintenanceModeContextType {
    isMaintenanceMode: boolean;
    maintenanceMessage: string;
    platformName: string;
    refreshMaintenanceStatus: () => Promise<void>;
}

const MaintenanceModeContext = createContext<
    MaintenanceModeContextType | undefined
>(undefined);

export const useMaintenanceMode = () => {
    const context = useContext(MaintenanceModeContext);
    if (context === undefined) {
        throw new Error(
            "useMaintenanceMode must be used within a MaintenanceModeProvider"
        );
    }
    return context;
};

interface MaintenanceModeProviderProps {
    children: React.ReactNode;
    bypassForAdmins?: boolean; // Allow super admins to bypass maintenance mode
}

const MaintenanceModeProvider: React.FC<MaintenanceModeProviderProps> = ({
    children,
    bypassForAdmins = true,
}) => {
    const [isMaintenanceMode, setIsMaintenanceMode] = useState(false);
    const [maintenanceMessage, setMaintenanceMessage] = useState("");
    const [platformName, setPlatformName] = useState("Scholarify");
    const [supportEmail, setSupportEmail] = useState("<EMAIL>");
    const [loading, setLoading] = useState(true);
    const [isAdmin, setIsAdmin] = useState(false);

    const checkMaintenanceStatus = async () => {
        try {
            const settings = await getSystemSettings();
            setIsMaintenanceMode(settings.maintenanceMode);
            setMaintenanceMessage(
                settings.maintenanceMessage ||
                    "We are currently performing scheduled maintenance. Please check back soon."
            );
            setPlatformName(settings.platformName || "Scholarify");
            setSupportEmail(settings.supportEmail || "<EMAIL>");
        } catch (error) {
            console.error("Error checking maintenance status:", error);
            // In case of error, assume maintenance mode is off
            setIsMaintenanceMode(false);
        } finally {
            setLoading(false);
        }
    };

    const checkIfAdmin = () => {
        // Check if user is admin (you can implement your own logic here)
        const userRole = localStorage.getItem("userRole");
        const currentPath = window.location.pathname;
        setIsAdmin(
            userRole === "super-admin" || currentPath.includes("/super-admin")
        );
    };

    useEffect(() => {
        checkIfAdmin();
        checkMaintenanceStatus();

        // Check maintenance status every 5 minutes
        const interval = setInterval(checkMaintenanceStatus, 5 * 60 * 1000);

        return () => clearInterval(interval);
    }, []);

    const refreshMaintenanceStatus = async () => {
        await checkMaintenanceStatus();
    };

    if (loading) {
        return (
            <div className="min-h-screen flex items-center justify-center">
                <CircularLoader size={32} color="teal" />
            </div>
        );
    }

    // Show maintenance mode if enabled and user is not admin (or bypass is disabled)
    if (isMaintenanceMode && !(bypassForAdmins && isAdmin)) {
        return (
            <MaintenanceMode
                message={maintenanceMessage}
                platformName={platformName}
                supportEmail={supportEmail}
            />
        );
    }

    const contextValue: MaintenanceModeContextType = {
        isMaintenanceMode,
        maintenanceMessage,
        platformName,
        refreshMaintenanceStatus,
    };

    return (
        <MaintenanceModeContext.Provider value={contextValue}>
            {children}
        </MaintenanceModeContext.Provider>
    );
};

export default MaintenanceModeProvider;
