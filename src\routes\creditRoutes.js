const express = require('express');
const creditController = require('../controllers/creditController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// Test route (optional)
// router.get('/test', creditController.testCreditResponse);

// GET /credits - Fetch all credit records
router.get('/get-credits',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'parent', 'teacher']),
  creditController.getAllCredits
);

// GET /credit/:id - Fetch a specific credit record by ID
router.get('/get-credit/:id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'parent', 'teacher']),
  creditController.getCreditById
);

// POST /credit - Create a new credit record
router.post('/create-credit',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  creditController.createCredit
);

// PUT /credit/:id - Update a specific credit record by ID
router.put('/update-credit/:id',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  creditController.updateCreditById
);

// DELETE /credit/:id - Delete a specific credit record
router.delete('/delete-credit/:id',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  creditController.deleteCreditById
);

// DELETE /credits - Delete multiple credit records
router.delete('/delete-credits',
  authenticate,
  authorize(['admin', 'super', 'teacher']),
  creditController.deleteMultipleCredits
);

// GET /credits/school/:school_id - Get all credits for a specific school
router.get('/get-credits-by-school/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'parent', 'teacher']),
  creditController.getCreditsBySchoolId
);

router.get(
  '/total-amount-paid',
  authenticate,
  authorize(['super']), // adjust roles as necessary
  creditController.getTotalAmountPaid
);

// GET /credits/total-amount-change - Get total amount paid for current month with % change from previous month
router.get(
  '/total-amount-change',
  authenticate,
  authorize(['super']), // Adjust roles as needed
  creditController.getTotalAmountWithChange
);


module.exports = router;
