// EXEMPLE D'INTÉGRATION DU MULTIPLE DELETE POUR LES CLASS LEVELS
// <PERSON> fichier montre comment intégrer le multiple delete dans la page existante

import React from 'react';
import ClassLevelsTableWithBulkActions from '../components/ClassLevelsTableWithBulkActions';

// Dans votre page existante, au lieu d'utiliser DataTableFix pour les class levels :

/*
ANCIEN CODE (à remplacer) :
<DataTableFix
  columns={Level_columns}
  actions={Level_actions}
  data={filteredLevels}
  defaultItemsPerPage={5}
  loading={loadingLevels}
  onLoadingChange={setLoadingLevels}
  onSelectionChange={setSelectedLevels}
/>
*/

// NOUVEAU CODE (avec multiple delete) :
const ExampleClassLevelsWithBulkActions = ({
  filteredLevels,
  setClassLevels,
  Level_columns,
  Level_actions,
  loadingLevels,
  setLoadingLevels,
  setSelectedLevels,
  setNotificationMessage,
  setNotificationType,
  setIsNotificationCard
}) => {
  return (
    <ClassLevelsTableWithBulkActions
      classLevels={filteredLevels}
      setClassLevels={setClassLevels}
      columns={Level_columns}
      actions={Level_actions}
      loading={loadingLevels}
      onLoadingChange={setLoadingLevels}
      onSelectionChange={setSelectedLevels}
      onSuccess={(message) => {
        setNotificationMessage(message);
        setNotificationType("success");
        setIsNotificationCard(true);
      }}
      onError={(message) => {
        setNotificationMessage(message);
        setNotificationType("error");
        setIsNotificationCard(true);
      }}
    />
  );
};

export default ExampleClassLevelsWithBulkActions;

/*
INSTRUCTIONS D'INTÉGRATION :

1. Importer le composant dans votre page :
   import ClassLevelsTableWithBulkActions from '../components/ClassLevelsTableWithBulkActions';

2. Remplacer le DataTableFix existant par le nouveau composant

3. Ajouter les handlers de notification si ils n'existent pas déjà

4. Tester la fonctionnalité :
   - Sélectionner quelques class levels
   - Cliquer sur "Delete Selected"
   - Confirmer avec le mot de passe
   - Vérifier que les éléments sont supprimés

NOTES :
- Le composant utilise les mêmes props que DataTableFix
- Ajoute automatiquement les fonctionnalités de bulk delete
- Respecte les permissions existantes
- Utilise les services backend déjà créés
*/
