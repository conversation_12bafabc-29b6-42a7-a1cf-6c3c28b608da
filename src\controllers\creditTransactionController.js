const CreditTransaction = require('../models/CreditTransaction');
const School = require('../models/School');
const mongoose = require('mongoose');

// Test response
const testCreditTransaction = (req, res) => {
  res.status(200).json({ message: 'Hi, this is the credit transaction response' });
};

// Get all credit transactions (no populate)
const getAllCreditTransactions = async (req, res) => {
  try {
    const transactions = await CreditTransaction.find();
    res.status(200).json(transactions);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Create a new credit transaction
const createCreditTransaction = async (req, res) => {
  try {
    const { school_id, academicYear_id, payment_method, amountPaid, credit, paidAt } = req.body;

    if (!school_id || !academicYear_id || !payment_method || amountPaid === undefined || credit === undefined) {
      return res.status(400).json({ message: 'school_id, academicYear_id, payment_method, amountPaid, and credit are required.' });
    }

    if (!['manual', 'automatic', 'gift'].includes(payment_method)) {
      return res.status(400).json({ message: 'Invalid payment_method. Allowed values: manual, automatic.' });
    }
    if (payment_method === 'gift' && amountPaid !== 0) {
      return res.status(400).json({ message: 'For gift payments, amountPaid must be 0.' });
    }
    
    const newTransaction = new CreditTransaction({
      school_id,
      academicYear_id,
      payment_method,
      amountPaid,
      credit,
      paidAt: paidAt || Date.now()
    });

    await newTransaction.save();

    const school = await School.findById(school_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }
    school.credit = (school.credit || 0) + credit;
    await school.save();

    res.status(201).json(newTransaction);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get credit transaction by ID (no populate)
const getCreditTransactionById = async (req, res) => {
  try {
    const transaction = await CreditTransaction.findById(req.params.id);

    if (!transaction) {
      return res.status(404).json({ message: 'Credit transaction not found' });
    }

    res.status(200).json(transaction);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Update credit transaction by ID
const updateCreditTransactionById = async (req, res) => {
  try {
    const updateData = req.body;

    if (updateData.payment_method && !['manual', 'automatic'].includes(updateData.payment_method)) {
      return res.status(400).json({ message: 'Invalid payment_method. Allowed values: manual, automatic.' });
    }

    const updatedTransaction = await CreditTransaction.findByIdAndUpdate(req.params.id, updateData, { new: true });

    if (!updatedTransaction) {
      return res.status(404).json({ message: 'Credit transaction not found' });
    }

    res.status(200).json(updatedTransaction);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete credit transaction by ID
const deleteCreditTransactionById = async (req, res) => {
  try {
    const deleted = await CreditTransaction.findByIdAndDelete(req.params.id);

    if (!deleted) {
      return res.status(404).json({ message: 'Credit transaction not found' });
    }

    res.status(200).json({ message: 'Credit transaction deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete multiple credit transactions
const deleteMultipleCreditTransactions = async (req, res) => {
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    const result = await CreditTransaction.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No credit transactions found for the provided IDs' });
    }

    res.status(200).json({ message: `${result.deletedCount} credit transactions deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getCreditTransactionsBySchoolId = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const transactions = await CreditTransaction.find({ school_id });

    // ✅ Return 200 with an empty array instead of 404
    return res.status(200).json(transactions);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  testCreditTransaction,
  getAllCreditTransactions,
  createCreditTransaction,
  getCreditTransactionById,
  updateCreditTransactionById,
  deleteCreditTransactionById,
  deleteMultipleCreditTransactions,
  getCreditTransactionsBySchoolId,
};
