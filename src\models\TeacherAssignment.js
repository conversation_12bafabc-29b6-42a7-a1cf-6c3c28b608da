const mongoose = require('mongoose');

// Define the schema for Teacher Assignment
const teacherAssignmentSchema = new mongoose.Schema({
  assignment_id: {
    type: String,
    required: true,
    unique: true
  },
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  teacher_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  class_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Class',
    required: true
  },
  subjects: [{
    type: String, // Subject names as strings
    required: true
  }],
  academic_year: {
    type: String,
    required: true
  },
  is_active: {
    type: Boolean,
    default: true
  },
  assigned_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assigned_at: {
    type: Date,
    default: Date.now
  },
  last_modified_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  last_modified_at: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true
});

// Indexes for better performance
teacherAssignmentSchema.index({ school_id: 1, teacher_id: 1 });
teacherAssignmentSchema.index({ school_id: 1, class_id: 1 });
teacherAssignmentSchema.index({ school_id: 1, class_id: 1, subjects: 1 });

// Ensure only one teacher per subject per class
teacherAssignmentSchema.index(
  { school_id: 1, class_id: 1, subjects: 1 },
  { 
    unique: true,
    partialFilterExpression: { is_active: true }
  }
);

const TeacherAssignment = mongoose.model('TeacherAssignment', teacherAssignmentSchema);
module.exports = TeacherAssignment;
