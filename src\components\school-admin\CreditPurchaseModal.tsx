"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  CreditCard, 
  Calculator, 
  AlertCircle, 
  CheckCircle,
  Loader2,
  Plus,
  Minus
} from 'lucide-react';
import { 
  initiateCreditPurchase, 
  calculatePrice,
  formatCurrency 
} from '@/app/services/SubscriptionServices';

interface CreditPurchaseModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  schoolId: string;
  currentPlan: string;
}

export default function CreditPurchaseModal({
  isOpen,
  onClose,
  onSuccess,
  schoolId,
  currentPlan
}: CreditPurchaseModalProps) {
  const [creditsAmount, setCreditsAmount] = useState(10);
  const [calculation, setCalculation] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [step, setStep] = useState<'select' | 'payment' | 'processing' | 'success'>('select');
  const [billingInfo, setBillingInfo] = useState({
    name: '',
    email: '',
    phone: '',
    organization: ''
  });

  useEffect(() => {
    if (isOpen && creditsAmount > 0) {
      calculateCost();
    }
  }, [isOpen, creditsAmount, currentPlan]);

  const calculateCost = async () => {
    try {
      const response = await calculatePrice(currentPlan, creditsAmount);
      setCalculation(response.calculation);
    } catch (err) {
      console.error('Error calculating price:', err);
      setError('Erreur lors du calcul du prix');
    }
  };

  const handleCreditsChange = (newAmount: number) => {
    if (newAmount >= 1 && newAmount <= 1000) {
      setCreditsAmount(newAmount);
    }
  };

  const handlePurchase = async () => {
    try {
      setLoading(true);
      setError(null);
      setStep('processing');

      const purchaseData = {
        school_id: schoolId,
        credits_amount: creditsAmount,
        payment_method: 'fapshi',
        billing_info: billingInfo,
        redirect_url: `${window.location.origin}/school-admin/buy-credit/success`
      };

      const response = await initiateCreditPurchase(purchaseData);
      
      // Redirect to payment gateway
      if (response.payment_response?.paymentUrl) {
        window.location.href = response.payment_response.paymentUrl;
      } else {
        setStep('success');
        setTimeout(() => {
          onSuccess();
        }, 2000);
      }
    } catch (err: any) {
      setError(err.message || 'Erreur lors de l\'initiation du paiement');
      setStep('select');
    } finally {
      setLoading(false);
    }
  };

  const resetModal = () => {
    setStep('select');
    setError(null);
    setCreditsAmount(10);
    setBillingInfo({ name: '', email: '', phone: '', organization: '' });
  };

  const handleClose = () => {
    resetModal();
    onClose();
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-[9999] overflow-y-auto">
        <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-gray-500 dark:bg-gray-900 bg-opacity-75 dark:bg-opacity-80 transition-opacity"
            onClick={handleClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
          >
            {/* Header */}
            <div className="bg-white px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-medium text-gray-900 flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-blue-600" />
                  Acheter des crédits
                </h3>
                <button
                  onClick={handleClose}
                  className="text-gray-400 hover:text-gray-600"
                >
                  <X className="h-6 w-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="bg-white px-6 py-4">
              {step === 'select' && (
                <div className="space-y-6">
                  {/* Credits Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">
                      Nombre de crédits à acheter
                    </label>
                    
                    {/* Quick Select Buttons */}
                    <div className="grid grid-cols-4 gap-2 mb-4">
                      {[10, 25, 50, 100].map(amount => (
                        <button
                          key={amount}
                          onClick={() => setCreditsAmount(amount)}
                          className={`p-2 text-sm rounded-lg border ${
                            creditsAmount === amount
                              ? 'border-blue-500 bg-blue-50 text-blue-700'
                              : 'border-gray-300 hover:border-gray-400'
                          }`}
                        >
                          {amount}
                        </button>
                      ))}
                    </div>

                    {/* Custom Amount Input */}
                    <div className="flex items-center space-x-3">
                      <button
                        onClick={() => handleCreditsChange(creditsAmount - 1)}
                        className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                        disabled={creditsAmount <= 1}
                      >
                        <Minus className="h-4 w-4" />
                      </button>
                      
                      <input
                        type="number"
                        min="1"
                        max="1000"
                        value={creditsAmount}
                        onChange={(e) => handleCreditsChange(parseInt(e.target.value) || 1)}
                        className="flex-1 text-center text-xl font-bold border border-gray-300 rounded-lg py-2 px-4"
                      />
                      
                      <button
                        onClick={() => handleCreditsChange(creditsAmount + 1)}
                        className="p-2 border border-gray-300 rounded-lg hover:bg-gray-50"
                        disabled={creditsAmount >= 1000}
                      >
                        <Plus className="h-4 w-4" />
                      </button>
                    </div>
                  </div>

                  {/* Price Calculation */}
                  {calculation && (
                    <div className="bg-blue-50 rounded-lg p-4">
                      <div className="flex items-center mb-3">
                        <Calculator className="h-5 w-5 text-blue-600 mr-2" />
                        <span className="font-medium text-blue-900">Récapitulatif</span>
                      </div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-blue-700">Crédits:</span>
                          <span className="font-medium">{calculation.credits_amount}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-blue-700">Prix unitaire:</span>
                          <span className="font-medium">{formatCurrency(calculation.price_per_credit)}</span>
                        </div>
                        <div className="border-t border-blue-200 pt-2 flex justify-between">
                          <span className="font-medium text-blue-900">Total:</span>
                          <span className="font-bold text-blue-900 text-lg">
                            {formatCurrency(calculation.total)}
                          </span>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Billing Info */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-900">Informations de facturation</h4>
                    
                    <div className="grid grid-cols-1 gap-4">
                      <input
                        type="text"
                        placeholder="Nom complet"
                        value={billingInfo.name}
                        onChange={(e) => setBillingInfo({...billingInfo, name: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      
                      <input
                        type="email"
                        placeholder="Email"
                        value={billingInfo.email}
                        onChange={(e) => setBillingInfo({...billingInfo, email: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                      
                      <input
                        type="tel"
                        placeholder="Téléphone"
                        value={billingInfo.phone}
                        onChange={(e) => setBillingInfo({...billingInfo, phone: e.target.value})}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>

                  {/* Error Message */}
                  {error && (
                    <div className="flex items-center p-3 bg-red-50 border border-red-200 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                      <span className="text-red-700 text-sm">{error}</span>
                    </div>
                  )}
                </div>
              )}

              {step === 'processing' && (
                <div className="text-center py-8">
                  <Loader2 className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Traitement en cours...
                  </h4>
                  <p className="text-gray-600">
                    Redirection vers la page de paiement
                  </p>
                </div>
              )}

              {step === 'success' && (
                <div className="text-center py-8">
                  <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">
                    Paiement initié avec succès !
                  </h4>
                  <p className="text-gray-600">
                    Vous allez être redirigé vers la page de paiement
                  </p>
                </div>
              )}
            </div>

            {/* Footer */}
            {step === 'select' && (
              <div className="bg-gray-50 px-6 py-4 flex justify-end space-x-3">
                <button
                  onClick={handleClose}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
                >
                  Annuler
                </button>
                <button
                  onClick={handlePurchase}
                  disabled={loading || !calculation || !billingInfo.name || !billingInfo.email}
                  className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                >
                  {loading && <Loader2 className="h-4 w-4 animate-spin mr-2" />}
                  Procéder au paiement
                </button>
              </div>
            )}
          </motion.div>
        </div>
      </div>
    </AnimatePresence>
  );
}
