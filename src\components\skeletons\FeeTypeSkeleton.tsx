"use client";

import React from 'react';
import { 
  SkeletonCard, 
  SkeletonLine, 
  SkeletonButton, 
  SkeletonTableRow 
} from './SkeletonCard';

interface FeeTypeSkeletonProps {
  itemCount?: number;
}

const FeeTypeSkeleton: React.FC<FeeTypeSkeletonProps> = ({ 
  itemCount = 5 
}) => {
  return (
    <div className="container mx-auto p-4 space-y-6">
      {/* Header Skeleton */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div className="space-y-2">
          <SkeletonLine width="w-48" height="h-8" />
          <SkeletonLine width="w-64" height="h-4" />
        </div>
        <SkeletonButton width="w-32" height="h-10" />
      </div>

      {/* Table Header Skeleton */}
      <SkeletonCard>
        <div className="space-y-4">
          {/* Search and Actions Bar */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <SkeletonLine width="w-64" height="h-10" />
            <div className="flex space-x-2">
              <SkeletonButton width="w-32" height="h-8" />
              <SkeletonButton width="w-24" height="h-8" />
            </div>
          </div>

          {/* Table Headers */}
          <div className="grid grid-cols-4 gap-4 p-4 border-b border-stroke">
            <SkeletonLine width="w-20" height="h-4" />
            <SkeletonLine width="w-16" height="h-4" />
            <SkeletonLine width="w-24" height="h-4" />
            <SkeletonLine width="w-16" height="h-4" />
          </div>

          {/* Table Rows */}
          <div className="space-y-2">
            {Array.from({ length: itemCount }).map((_, index) => (
              <SkeletonTableRow key={index} columns={4} />
            ))}
          </div>

          {/* Pagination Skeleton */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0 pt-4 border-t border-stroke">
            <SkeletonLine width="w-32" height="h-4" />
            <div className="flex space-x-2">
              <SkeletonButton width="w-8" height="h-8" />
              <SkeletonButton width="w-8" height="h-8" />
              <SkeletonButton width="w-8" height="h-8" />
              <SkeletonButton width="w-8" height="h-8" />
            </div>
          </div>
        </div>
      </SkeletonCard>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        {Array.from({ length: 4 }).map((_, index) => (
          <SkeletonCard key={index}>
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <SkeletonLine width="w-16" height="h-4" />
                <SkeletonLine width="w-12" height="h-8" />
              </div>
              <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full animate-pulse" />
            </div>
          </SkeletonCard>
        ))}
      </div>
    </div>
  );
};

export default FeeTypeSkeleton;
