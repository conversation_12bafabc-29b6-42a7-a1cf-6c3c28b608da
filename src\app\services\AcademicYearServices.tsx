import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import { AcademicYearSchema } from "../models/AcademicYear";

// Get all academic years
export async function getAcademicYears(): Promise<AcademicYearSchema[]> {
    try {
        const token = getTokenFromCookie("idToken");
        
        // Check if we have a token
        if (!token) {
            console.warn("No authentication token found");
            return []; // Return empty array instead of throwing error
        }

        console.log("Fetching academic years from:", `${BASE_API_URL}/academic-years/get-academic-years`);
        
        const response = await fetch(`${BASE_API_URL}/academic-years/get-academic-years`, {
            method: "GET",
            headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
            },
        });

        if (!response.ok) {
            console.error("API Response Error:", {
                status: response.status,
                statusText: response.statusText,
                url: response.url
            });
            
            // If unauthorized, return empty array instead of throwing
            if (response.status === 401) {
                console.warn("Unauthorized access - user may need to log in");
                return [];
            }
            
            throw new Error(`Failed to fetch academic year data: ${response.status} ${response.statusText}`);
        }

        const academicYearsList = await response.json();

        if (!Array.isArray(academicYearsList)) {
            console.error("Expected array but got:", typeof academicYearsList);
            return [];
        }

        return academicYearsList.map((item: any) => ({
            _id: item._id,
            academic_year: item.academic_year,
            start_date: item.start_date,
            end_date: item.end_date,
        })) as AcademicYearSchema[];

    } catch (error) {
        console.error("Error fetching academic years:", error);
        
        // Check if it's a network error
        if (error instanceof TypeError && error.message.includes('fetch')) {
            console.error("Network error - API might be unreachable");
            return []; // Return empty array for network errors
        }
        
        // For other errors, still return empty array but log the error
        console.error("Academic year fetch error:", error);
        return [];
    }
}
