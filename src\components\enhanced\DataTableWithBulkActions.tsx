import React from 'react';
import DataTableFix from '@/components/utils/TableFix';
import BulkDeleteModal from '@/components/modals/BulkDeleteModal';
import { useMultipleDelete } from '@/app/hooks/useMultipleDelete';
import { useTableSelection } from '@/app/hooks/useTableSelection';

interface DataTableWithBulkActionsProps<T> {
  // All existing DataTableFix props
  columns: any[];
  data: T[];
  actions?: any[];
  defaultItemsPerPage?: number;
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: T[]) => void;
  idAccessor?: keyof T;
  hasSearch?: boolean;
  showCheckbox?: boolean;
  
  // Bulk delete specific props
  enableBulkActions?: boolean;
  deleteMultipleService: (ids: string[]) => Promise<any>;
  deleteAllService: () => Promise<any>;
  itemName: string;
  setItems: React.Dispatch<React.SetStateAction<T[]>>;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

function DataTableWithBulkActions<T>({
  columns,
  data,
  actions = [],
  defaultItemsPerPage = 5,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  idAccessor = '_id' as keyof T,
  hasSearch = true,
  showCheckbox = true,
  enableBulkActions = false,
  deleteMultipleService,
  deleteAllService,
  itemName,
  setItems,
  onSuccess,
  onError,
  ...otherProps
}: DataTableWithBulkActionsProps<T>) {

  // Use table selection hook
  const tableSelection = useTableSelection<T>({
    onSelectionChange
  });

  const multipleDelete = useMultipleDelete({
    items: data,
    setItems,
    deleteMultipleService,
    deleteAllService,
    idAccessor,
    itemName,
    onSuccess,
    onError,
    clearTableSelection: tableSelection.clearTableSelection
  });

  return (
    <>
      <DataTableFix
        columns={columns}
        data={data}
        actions={actions}
        defaultItemsPerPage={defaultItemsPerPage}
        loading={loading}
        onLoadingChange={onLoadingChange}
        onSelectionChange={tableSelection.handleSelectionChange}
        idAccessor={idAccessor}
        hasSearch={hasSearch}
        showCheckbox={showCheckbox && enableBulkActions}
        enableBulkActions={enableBulkActions}
        handleDeleteMultiple={multipleDelete.handleDeleteMultiple}
        handleDeleteAll={multipleDelete.handleDeleteAll}
        clearSelection={tableSelection.clearSelection}
        onSelectionCleared={tableSelection.onSelectionCleared}
        {...otherProps}
      />

      {/* Bulk Delete Modal */}
      {enableBulkActions && multipleDelete.isBulkDeleteModalOpen && (
        <BulkDeleteModal {...multipleDelete.modalProps} />
      )}
    </>
  );
}

export default DataTableWithBulkActions;
