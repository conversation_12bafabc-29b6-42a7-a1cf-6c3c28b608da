"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rash<PERSON>, <PERSON>, <PERSON>, BookO<PERSON>, Calendar } from "lucide-react";
import { motion } from "framer-motion";
import { ScheduleEntry } from "@/app/services/TimetableServices";

interface ScheduleDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  scheduleEntry: ScheduleEntry | null;
  isExamMode: boolean;
  loading?: boolean;
}

export default function ScheduleDeleteModal({
  isOpen,
  onClose,
  onConfirm,
  scheduleEntry,
  isExamMode,
  loading = false
}: ScheduleDeleteModalProps) {
  if (!isOpen || !scheduleEntry) return null;

  const formatTime = (time: string) => {
    try {
      const timeParts = time.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = timeParts[1];
      
      if (hours === 0) return `12:${minutes} AM`;
      if (hours < 12) return `${hours}:${minutes} AM`;
      if (hours === 12) return `12:${minutes} PM`;
      return `${hours - 12}:${minutes} PM`;
    } catch {
      return time;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-widget rounded-lg shadow-xl w-full max-w-lg mx-4 p-6 relative border border-stroke max-h-[90vh] overflow-y-auto"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">
                Delete {isExamMode ? 'Exam' : 'Schedule'} Entry
              </h2>
              <p className="text-sm text-foreground/60">This action cannot be undone</p>
            </div>
          </div>
          <button
            onClick={onClose}
            disabled={loading}
            className="text-foreground/60 hover:text-foreground transition-colors disabled:opacity-50"
          >
            <X size={20} />
          </button>
        </div>

        {/* Schedule Details */}
        <div className="mb-6">
          <h3 className="font-medium text-foreground mb-3">
            {isExamMode ? 'Exam' : 'Schedule'} Details:
          </h3>
          
          <div className="space-y-3">
            {/* Subject */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <BookOpen className="h-4 w-4 text-foreground/60" />
              <div>
                <p className="text-sm font-medium text-foreground">Subject</p>
                <p className="text-sm text-foreground/70">{scheduleEntry.subject_name}</p>
              </div>
            </div>

            {/* Class */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <Users className="h-4 w-4 text-foreground/60" />
              <div>
                <p className="text-sm font-medium text-foreground">Class</p>
                <p className="text-sm text-foreground/70">{scheduleEntry.class_name}</p>
              </div>
            </div>

            {/* Time & Day */}
            <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <Clock className="h-4 w-4 text-foreground/60" />
              <div>
                <p className="text-sm font-medium text-foreground">Time & Day</p>
                <p className="text-sm text-foreground/70">
                  {scheduleEntry.day_of_week} • {formatTime(scheduleEntry.start_time)} - {formatTime(scheduleEntry.end_time)}
                </p>
              </div>
            </div>

            {/* Teacher */}
            {scheduleEntry.teacher_name && (
              <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <Users className="h-4 w-4 text-foreground/60" />
                <div>
                  <p className="text-sm font-medium text-foreground">
                    {isExamMode ? 'Supervisor' : 'Teacher'}
                  </p>
                  <p className="text-sm text-foreground/70">{scheduleEntry.teacher_name}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Impact Warning */}
        <div className="mb-6 p-4 bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-orange-600 dark:text-orange-400 mt-0.5 flex-shrink-0" />
            <div>
              <h4 className="font-medium text-orange-800 dark:text-orange-200 mb-2">
                What will happen when you delete this entry:
              </h4>
              <ul className="text-sm text-orange-700 dark:text-orange-300 space-y-1">
                <li>• The {isExamMode ? 'exam' : 'class'} will be removed from the timetable</li>
                <li>• The time slot will become available for other assignments</li>
                {scheduleEntry.teacher_name ? (
                  <li>• The {isExamMode ? 'supervisor' : 'teacher'} assignment for <strong>{scheduleEntry.teacher_name}</strong> will be removed</li>
                ) : (
                  <li>• No {isExamMode ? 'supervisor' : 'teacher'} assignment will be affected (none was set)</li>
                )}
                <li>• Changes will be reflected immediately in the timetable</li>
              </ul>
              
              <div className="mt-3 p-2 bg-orange-100 dark:bg-orange-900/30 rounded border border-orange-300 dark:border-orange-700">
                <p className="text-xs text-orange-800 dark:text-orange-200">
                  <strong>Note:</strong> If this {isExamMode ? 'exam' : 'class'} has related assignments in the Teacher Assignment section, 
                  those may need to be updated separately.
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onClose}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-foreground/70 bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {loading ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Deleting...</span>
              </>
            ) : (
              <>
                <Trash2 className="h-4 w-4" />
                <span>Delete {isExamMode ? 'Exam' : 'Schedule'}</span>
              </>
            )}
          </button>
        </div>
      </motion.div>
    </div>
  );
}
