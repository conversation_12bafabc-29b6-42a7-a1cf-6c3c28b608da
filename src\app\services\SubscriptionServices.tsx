import {
  SubscriptionPlanSchema,
  SchoolSubscriptionSchema,
  CreditPurchaseSchema,
  CreditUsageSchema,
  CreditPurchaseFormSchema
} from '@/app/models/SchoolSubscriptionModel';
import { getTokenFromCookie } from './UserServices';
import { BASE_API_URL } from './AuthContext';

// Utility function for API calls
async function apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const token = getTokenFromCookie('idToken');

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`${BASE_API_URL}${endpoint}`, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Subscription Plans Services
export const getSubscriptionPlans = async (): Promise<{ plans: SubscriptionPlanSchema[] }> => {
  return apiCall('/subscription-plans');
};

export const getPlanByName = async (planName: string): Promise<{ plan: SubscriptionPlanSchema }> => {
  return apiCall(`/subscription-plans/${planName}`);
};

export const calculatePrice = async (planName: string, creditsAmount: number): Promise<{
  calculation: {
    plan_name: string;
    credits_amount: number;
    price_per_credit: number;
    subtotal: number;
    discount: number;
    total: number;
    currency: string;
  }
}> => {
  return apiCall(`/subscription-plans/pricing/calculate?plan_name=${planName}&credits_amount=${creditsAmount}`);
};

export const comparePlans = async (): Promise<{ plans: SubscriptionPlanSchema[] }> => {
  return apiCall('/subscription-plans/compare/all');
};

// School Subscription Services
export const getSchoolSubscription = async (schoolId: string): Promise<{ subscription: SchoolSubscriptionSchema }> => {
  return apiCall(`/school-subscription/${schoolId}`);
};

export const updateSchoolSubscription = async (
  schoolId: string, 
  updates: Partial<SchoolSubscriptionSchema>
): Promise<{ subscription: SchoolSubscriptionSchema }> => {
  return apiCall(`/school-subscription/${schoolId}`, {
    method: 'PUT',
    body: JSON.stringify(updates),
  });
};

export const getSubscriptionStats = async (schoolId: string, period: string = 'month'): Promise<{
  subscription_overview: any;
  usage_stats: any[];
  recent_purchases: CreditPurchaseSchema[];
  daily_usage: any[];
  efficiency_metrics: any;
  total_purchased: any;
}> => {
  return apiCall(`/school-subscription/${schoolId}/stats?period=${period}`);
};

export const checkCreditsAvailability = async (schoolId: string, creditsNeeded: number = 1): Promise<{
  has_credits: boolean;
  credits_balance: number;
  credits_needed: number;
  is_low_balance: boolean;
  low_credit_threshold: number;
}> => {
  return apiCall(`/school-subscription/${schoolId}/credits/check?credits_needed=${creditsNeeded}`);
};

export const deductCredits = async (schoolId: string, deductionData: {
  credits_amount: number;
  usage_type: string;
  reference_id?: string;
  reference_type?: string;
  description: string;
  details?: any;
  session_info?: any;
}): Promise<{
  success: boolean;
  credits_deducted: number;
  new_balance: number;
}> => {
  return apiCall(`/school-subscription/${schoolId}/credits/deduct`, {
    method: 'POST',
    body: JSON.stringify(deductionData),
  });
};

export const getCreditUsageHistory = async (
  schoolId: string, 
  options: {
    limit?: number;
    skip?: number;
    usage_type?: string;
    start_date?: string;
    end_date?: string;
    used_by?: string;
  } = {}
): Promise<{
  usage_history: CreditUsageSchema[];
  pagination: {
    total: number;
    limit: number;
    skip: number;
    has_more: boolean;
  };
}> => {
  const queryParams = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  
  return apiCall(`/school-subscription/${schoolId}/usage?${queryParams.toString()}`);
};

// Credit Purchase Services
export const initiateCreditPurchase = async (purchaseData: {
  school_id: string;
  credits_amount: number;
  payment_method?: string;
  billing_info?: any;
  promotion_code?: string;
  redirect_url?: string;
}): Promise<{
  purchase: {
    purchase_id: string;
    transaction_id: string;
    credits_purchased: number;
    total_amount: number;
    payment_status: string;
  };
  payment_response: any;
}> => {
  return apiCall('/credit-purchase/initiate', {
    method: 'POST',
    body: JSON.stringify(purchaseData),
  });
};

export const confirmCreditPurchase = async (confirmationData: {
  transaction_id: string;
  payment_status: string;
  gateway_response?: any;
}): Promise<{ purchase: CreditPurchaseSchema }> => {
  return apiCall('/credit-purchase/confirm', {
    method: 'POST',
    body: JSON.stringify(confirmationData),
  });
};

export const getPurchaseHistory = async (
  schoolId: string, 
  options: { limit?: number; skip?: number } = {}
): Promise<{
  purchases: CreditPurchaseSchema[];
  pagination: {
    total: number;
    limit: number;
    skip: number;
    has_more: boolean;
  };
}> => {
  const queryParams = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  
  return apiCall(`/credit-purchase/school/${schoolId}/history?${queryParams.toString()}`);
};

export const getPurchaseDetails = async (purchaseId: string): Promise<{ purchase: CreditPurchaseSchema }> => {
  return apiCall(`/credit-purchase/${purchaseId}`);
};

export const checkPaymentStatus = async (transactionId: string): Promise<{
  transaction_id: string;
  purchase_id: string;
  payment_status: string;
  credits_purchased: number;
  total_amount: number;
  purchase_date: string;
  payment_completed_date?: string;
}> => {
  return apiCall(`/credit-purchase/payment/${transactionId}/status`);
};

// Plan Management Services
export const changePlan = async (schoolId: string, newPlanName: string): Promise<{
  subscription: {
    school_id: string;
    old_plan: string;
    new_plan: string;
    features: string[];
    credits_balance: number;
  };
}> => {
  return apiCall(`/subscription-plans/school/${schoolId}/change`, {
    method: 'PUT',
    body: JSON.stringify({ new_plan_name: newPlanName }),
  });
};

export const getPlanRecommendations = async (schoolId: string): Promise<{
  current_plan: string;
  usage_analysis: {
    monthly_credits_used: number;
    chatbot_messages: number;
    efficiency_score: number;
  };
  recommendations: Array<{
    plan: string;
    reason: string;
    potential_savings: number | string;
    features_gained: string[];
  }>;
}> => {
  return apiCall(`/subscription-plans/school/${schoolId}/recommendations`);
};

// Utility Services
export const formatCurrency = (amount: number, currency: string = 'XAF'): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatCredits = (credits: number): string => {
  return `${credits.toLocaleString()} crédit${credits > 1 ? 's' : ''}`;
};

export const calculateSavings = (monthlyAmount: number, yearlyAmount: number): {
  savings: number;
  percentage: number;
} => {
  const annualWithoutDiscount = monthlyAmount * 12;
  const savings = annualWithoutDiscount - yearlyAmount;
  const percentage = (savings / annualWithoutDiscount) * 100;
  
  return { savings, percentage };
};

export const getPlanIcon = (planName: string): string => {
  switch (planName) {
    case 'basic': return '🌱';
    case 'standard': return '⭐';
    case 'custom': return '👑';
    default: return '📋';
  }
};

export const getPlanColor = (planName: string): string => {
  switch (planName) {
    case 'basic': return '#10B981'; // green
    case 'standard': return '#3B82F6'; // blue
    case 'custom': return '#8B5CF6'; // purple
    default: return '#6B7280'; // gray
  }
};
