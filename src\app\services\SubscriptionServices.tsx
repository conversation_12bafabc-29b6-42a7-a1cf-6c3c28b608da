import {
  SubscriptionPlanSchema,
  SchoolSubscriptionSchema,
  CreditPurchaseSchema,
  CreditUsageSchema,
  CreditPurchaseFormSchema
} from '@/app/models/SchoolSubscriptionModel';
import { getTokenFromCookie } from './UserServices';
import { BASE_API_URL } from './AuthContext';

// Données mock pour les plans de souscription
const MOCK_PLANS: SubscriptionPlanSchema[] = [
  {
    _id: 'plan_basic_001',
    plan_name: 'basic',
    display_name: 'Plan Basic',
    description: 'Parfait pour débuter avec la gestion scolaire',
    price_per_credit: 3000,
    currency: 'FCFA',
    chatbot_enabled: false,
    features: [
      {
        name: 'student_management',
        display_name: 'Gestion des étudiants',
        description: 'Inscription, profils et suivi des étudiants',
        included: true
      },
      {
        name: 'class_management',
        display_name: 'Gestion des classes',
        description: 'Organisation des classes et groupes',
        included: true
      },
      {
        name: 'attendance_tracking',
        display_name: 'Suivi des présences',
        description: 'Pointage et rapports de présence',
        included: true
      },
      {
        name: 'grade_management',
        display_name: 'Gestion des notes',
        description: 'Saisie et calcul des notes',
        included: true
      },
      {
        name: 'timetable_management',
        display_name: 'Emplois du temps',
        description: 'Création et gestion des emplois du temps',
        included: true
      }
    ],
    benefits: [
      'Gestion complète des étudiants',
      'Suivi des présences et notes',
      'Emplois du temps personnalisés',
      'Rapports de base',
      'Support par email'
    ],
    limitations: [
      'Pas de chatbot IA',
      'Rapports limités',
      'Support standard'
    ],
    recommended_for: 'Petites écoles (moins de 100 étudiants)',
    max_students: 100,
    is_active: true,
    sort_order: 1,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: 'plan_standard_002',
    plan_name: 'standard',
    display_name: 'Plan Standard',
    description: 'Le choix idéal pour la plupart des écoles',
    price_per_credit: 3000,
    currency: 'FCFA',
    chatbot_enabled: true,
    chatbot_credits_per_message: 1,
    features: [
      {
        name: 'student_management',
        display_name: 'Gestion des étudiants',
        description: 'Inscription, profils et suivi des étudiants',
        included: true
      },
      {
        name: 'chatbot_access',
        display_name: 'Chatbot IA',
        description: 'Assistant intelligent pour répondre aux questions',
        included: true
      },
      {
        name: 'advanced_reports',
        display_name: 'Rapports avancés',
        description: 'Analytics détaillées et tableaux de bord',
        included: true
      }
    ],
    benefits: [
      'Toutes les fonctionnalités Basic',
      'Chatbot IA intelligent (1 crédit/message)',
      'Rapports avancés et analytics',
      'Support prioritaire',
      'Accès aux nouvelles fonctionnalités'
    ],
    limitations: [
      'Coût par message chatbot'
    ],
    recommended_for: 'Écoles moyennes (100-500 étudiants)',
    max_students: 500,
    is_active: true,
    sort_order: 2,
    is_popular: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  },
  {
    _id: 'plan_custom_003',
    plan_name: 'custom',
    display_name: 'Plan Custom',
    description: 'Solution sur mesure pour les grandes institutions',
    price_per_credit: 0, // Prix négocié
    currency: 'FCFA',
    chatbot_enabled: true,
    features: [
      {
        name: 'everything_standard',
        display_name: 'Toutes les fonctionnalités Standard',
        description: 'Accès complet à toutes les fonctionnalités',
        included: true
      },
      {
        name: 'custom_development',
        display_name: 'Développement sur mesure',
        description: 'Fonctionnalités personnalisées selon vos besoins',
        included: true
      },
      {
        name: 'dedicated_support',
        display_name: 'Support dédié 24/7',
        description: 'Équipe dédiée disponible 24h/24',
        included: true
      }
    ],
    benefits: [
      'Toutes les fonctionnalités Standard',
      'Développement sur mesure',
      'Intégrations personnalisées',
      'Support dédié 24/7',
      'Formation personnalisée'
    ],
    limitations: [],
    recommended_for: 'Grandes institutions (500+ étudiants)',
    is_active: true,
    sort_order: 3,
    contact_required: true,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
];

// Utility function for API calls
async function apiCall<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const token = getTokenFromCookie('idToken');

  const defaultHeaders: HeadersInit = {
    'Content-Type': 'application/json',
  };

  if (token) {
    defaultHeaders.Authorization = `Bearer ${token}`;
  }

  const response = await fetch(`${BASE_API_URL}${endpoint}`, {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ message: 'Network error' }));
    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
  }

  return response.json();
}

// Subscription Plans Services
export const getSubscriptionPlans = async (): Promise<{ plans: SubscriptionPlanSchema[] }> => {
  return apiCall('/subscription-plans');
};

export const getPlanByName = async (planName: string): Promise<{ plan: SubscriptionPlanSchema }> => {
  return apiCall(`/subscription-plans/${planName}`);
};

export const calculatePrice = async (planName: string, creditsAmount: number): Promise<{
  calculation: {
    plan_name: string;
    credits_amount: number;
    price_per_credit: number;
    subtotal: number;
    discount: number;
    total: number;
    currency: string;
  }
}> => {
  return apiCall(`/subscription-plans/pricing/calculate?plan_name=${planName}&credits_amount=${creditsAmount}`);
};

export const comparePlans = async (): Promise<{ plans: SubscriptionPlanSchema[] }> => {
  return apiCall('/subscription-plans/compare/all');
};

// School Subscription Services
export const getSchoolSubscription = async (schoolId: string): Promise<{ subscription: SchoolSubscriptionSchema }> => {
  return apiCall(`/school-subscription/${schoolId}`);
};

export const updateSchoolSubscription = async (
  schoolId: string, 
  updates: Partial<SchoolSubscriptionSchema>
): Promise<{ subscription: SchoolSubscriptionSchema }> => {
  return apiCall(`/school-subscription/${schoolId}`, {
    method: 'PUT',
    body: JSON.stringify(updates),
  });
};

export const getSubscriptionStats = async (schoolId: string, period: string = 'month'): Promise<{
  subscription_overview: any;
  usage_stats: any[];
  recent_purchases: CreditPurchaseSchema[];
  daily_usage: any[];
  efficiency_metrics: any;
  total_purchased: any;
}> => {
  return apiCall(`/school-subscription/${schoolId}/stats?period=${period}`);
};

export const checkCreditsAvailability = async (schoolId: string, creditsNeeded: number = 1): Promise<{
  has_credits: boolean;
  credits_balance: number;
  credits_needed: number;
  is_low_balance: boolean;
  low_credit_threshold: number;
}> => {
  return apiCall(`/school-subscription/${schoolId}/credits/check?credits_needed=${creditsNeeded}`);
};

export const deductCredits = async (schoolId: string, deductionData: {
  credits_amount: number;
  usage_type: string;
  reference_id?: string;
  reference_type?: string;
  description: string;
  details?: any;
  session_info?: any;
}): Promise<{
  success: boolean;
  credits_deducted: number;
  new_balance: number;
}> => {
  return apiCall(`/school-subscription/${schoolId}/credits/deduct`, {
    method: 'POST',
    body: JSON.stringify(deductionData),
  });
};

export const getCreditUsageHistory = async (
  schoolId: string, 
  options: {
    limit?: number;
    skip?: number;
    usage_type?: string;
    start_date?: string;
    end_date?: string;
    used_by?: string;
  } = {}
): Promise<{
  usage_history: CreditUsageSchema[];
  pagination: {
    total: number;
    limit: number;
    skip: number;
    has_more: boolean;
  };
}> => {
  const queryParams = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  
  return apiCall(`/school-subscription/${schoolId}/usage?${queryParams.toString()}`);
};

// Credit Purchase Services
export const initiateCreditPurchase = async (purchaseData: {
  school_id: string;
  credits_amount: number;
  payment_method?: string;
  billing_info?: any;
  promotion_code?: string;
  redirect_url?: string;
}): Promise<{
  purchase: {
    purchase_id: string;
    transaction_id: string;
    credits_purchased: number;
    total_amount: number;
    payment_status: string;
  };
  payment_response: any;
}> => {
  return apiCall('/credit-purchase/initiate', {
    method: 'POST',
    body: JSON.stringify(purchaseData),
  });
};

export const confirmCreditPurchase = async (confirmationData: {
  transaction_id: string;
  payment_status: string;
  gateway_response?: any;
}): Promise<{ purchase: CreditPurchaseSchema }> => {
  return apiCall('/credit-purchase/confirm', {
    method: 'POST',
    body: JSON.stringify(confirmationData),
  });
};

export const getPurchaseHistory = async (
  schoolId: string, 
  options: { limit?: number; skip?: number } = {}
): Promise<{
  purchases: CreditPurchaseSchema[];
  pagination: {
    total: number;
    limit: number;
    skip: number;
    has_more: boolean;
  };
}> => {
  const queryParams = new URLSearchParams();
  Object.entries(options).forEach(([key, value]) => {
    if (value !== undefined) {
      queryParams.append(key, value.toString());
    }
  });
  
  return apiCall(`/credit-purchase/school/${schoolId}/history?${queryParams.toString()}`);
};

export const getPurchaseDetails = async (purchaseId: string): Promise<{ purchase: CreditPurchaseSchema }> => {
  return apiCall(`/credit-purchase/${purchaseId}`);
};

export const checkPaymentStatus = async (transactionId: string): Promise<{
  transaction_id: string;
  purchase_id: string;
  payment_status: string;
  credits_purchased: number;
  total_amount: number;
  purchase_date: string;
  payment_completed_date?: string;
}> => {
  return apiCall(`/credit-purchase/payment/${transactionId}/status`);
};

// Plan Management Services
export const changePlan = async (schoolId: string, newPlanName: string): Promise<{
  subscription: {
    school_id: string;
    old_plan: string;
    new_plan: string;
    features: string[];
    credits_balance: number;
  };
}> => {
  return apiCall(`/subscription-plans/school/${schoolId}/change`, {
    method: 'PUT',
    body: JSON.stringify({ new_plan_name: newPlanName }),
  });
};

export const getPlanRecommendations = async (schoolId: string): Promise<{
  current_plan: string;
  usage_analysis: {
    monthly_credits_used: number;
    chatbot_messages: number;
    efficiency_score: number;
  };
  recommendations: Array<{
    plan: string;
    reason: string;
    potential_savings: number | string;
    features_gained: string[];
  }>;
}> => {
  return apiCall(`/subscription-plans/school/${schoolId}/recommendations`);
};

// Utility Services
export const formatCurrency = (amount: number, currency: string = 'XAF'): string => {
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

export const formatCredits = (credits: number): string => {
  return `${credits.toLocaleString()} crédit${credits > 1 ? 's' : ''}`;
};

export const calculateSavings = (monthlyAmount: number, yearlyAmount: number): {
  savings: number;
  percentage: number;
} => {
  const annualWithoutDiscount = monthlyAmount * 12;
  const savings = annualWithoutDiscount - yearlyAmount;
  const percentage = (savings / annualWithoutDiscount) * 100;
  
  return { savings, percentage };
};

export const getPlanIcon = (planName: string): string => {
  switch (planName) {
    case 'basic': return '🌱';
    case 'standard': return '⭐';
    case 'custom': return '👑';
    default: return '📋';
  }
};

export const getPlanColor = (planName: string): string => {
  switch (planName) {
    case 'basic': return '#10B981'; // green
    case 'standard': return '#3B82F6'; // blue
    case 'custom': return '#8B5CF6'; // purple
    default: return '#6B7280'; // gray
  }
};
