'use client';

import React from 'react';
import { X, Code, User, Crown, School, GraduationCap, Users } from 'lucide-react';

interface ChatHeaderProps {
  mode: 'technical' | 'user';
  onModeChange: (mode: 'technical' | 'user') => void;
  onClose: () => void;
  dashboardType?: string;
  userName?: string;
}

export default function ChatHeader({
  mode,
  onModeChange,
  onClose,
  dashboardType = 'super-admin',
  userName = 'User'
}: ChatHeaderProps) {

  const getDashboardIcon = (type: string) => {
    switch (type) {
      case 'super-admin': return Crown;
      case 'school-admin': return School;
      case 'teacher': return GraduationCap;
      case 'counselor': return Users;
      case 'parent': return Users;
      default: return Crown;
    }
  };

  const getDashboardTitle = (type: string) => {
    switch (type) {
      case 'super-admin': return 'Super Admin Assistant';
      case 'school-admin': return 'School Admin Assistant';
      case 'teacher': return 'Teacher Assistant';
      case 'counselor': return 'Counselor Assistant';
      case 'parent': return 'Parent Assistant';
      default: return 'Scholarify Assistant';
    }
  };

  const getDashboardColor = (type: string) => {
    switch (type) {
      case 'super-admin': return 'text-purple-600 bg-purple-100 dark:text-purple-400 dark:bg-purple-900';
      case 'school-admin': return 'text-blue-600 bg-blue-100 dark:text-blue-400 dark:bg-blue-900';
      case 'teacher': return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
      case 'counselor': return 'text-orange-600 bg-orange-100 dark:text-orange-400 dark:bg-orange-900';
      case 'parent': return 'text-pink-600 bg-pink-100 dark:text-pink-400 dark:bg-pink-900';
      default: return 'text-teal-600 bg-teal-100 dark:text-teal-400 dark:bg-teal-900';
    }
  };

  const DashboardIcon = getDashboardIcon(dashboardType);

  return (
    <div className="p-3 sm:p-4 border-b dark:border-gray-700 flex items-center justify-between bg-teal-50 dark:bg-teal-900/10 rounded-t-lg">
      <div className="flex items-center space-x-2">
        <div className={`p-1.5 rounded-full ${getDashboardColor(dashboardType)}`}>
          <DashboardIcon size={16} />
        </div>
        <div>
          <h3 className="font-semibold text-sm text-gray-900 dark:text-gray-100">
            {getDashboardTitle(dashboardType)}
          </h3>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            Hello, {userName}
          </p>
        </div>
        <div className="flex items-center bg-white dark:bg-gray-700 rounded-full p-0.5 sm:p-1 shadow-sm">
          <button
            onClick={() => onModeChange('user')}
            className={`p-1 sm:p-1.5 rounded-full transition-colors ${
              mode === 'user'
                ? 'bg-teal text-white'
                : 'hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            title="User mode"
          >
            <User className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </button>
          <button
            onClick={() => onModeChange('technical')}
            className={`p-1 sm:p-1.5 rounded-full transition-colors ${
              mode === 'technical'
                ? 'bg-teal text-white'
                : 'hover:bg-gray-100 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300'
            }`}
            title="Technical mode"
          >
            <Code className="w-3.5 h-3.5 sm:w-4 sm:h-4" />
          </button>
        </div>
      </div>
      
      <button
        onClick={onClose}
        className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors p-1 sm:p-0"
      >
        <X className="w-4 h-4 sm:w-5 sm:h-5" />
      </button>
    </div>
  );
} 