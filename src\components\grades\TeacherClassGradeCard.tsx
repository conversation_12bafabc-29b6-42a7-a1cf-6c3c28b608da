"use client";

import React, { useState, useEffect } from "react";
import { Users, BookOpen, TrendingUp, ArrowR<PERSON>, Percent } from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getGradeStats } from "@/app/services/GradeServices";

interface TeacherClassGradeCardProps {
  classId: string;
  className: string;
  subjectId: string;
  subjectName: string;
  schoolId: string;
  currentTerm?: any;
}

interface GradeStats {
  totalGrades: number;
  averageScore: number;
  highestScore: number;
  lowestScore: number;
  passRate: number;
}

export default function TeacherClassGradeCard({
  classId,
  className,
  subjectId,
  subjectName,
  schoolId,
  currentTerm
}: TeacherClassGradeCardProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [studentCount, setStudentCount] = useState(0);
  const [stats, setStats] = useState<GradeStats | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!schoolId || !classId || !subjectId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Fetch students count
        const studentsResponse = await getStudentsByClassAndSchool(classId, schoolId);
        setStudentCount(studentsResponse.length);

        // Fetch grade stats for this class and subject
        try {
          const filters = {
            class_id: classId,
            subject_id: subjectId,
            ...(currentTerm && { term_id: currentTerm._id })
          };
          
          const gradeStats = await getGradeStats(schoolId, filters);
          setStats(gradeStats);
        } catch (statsError) {
          console.warn("Could not fetch grade stats:", statsError);
          // Don't set error for stats, just leave stats as null
        }

      } catch (err) {
        console.error("Error fetching class data:", err);
        setError(err instanceof Error ? err.message : "Failed to load class data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [schoolId, classId, subjectId, currentTerm]);

  const handleCardClick = () => {
    // Navigate to the detailed grades page for this class and subject
    router.push(`/teacher-dashboard/grades/class/${classId}/subject/${subjectId}`);
  };

  const getGradeColor = (average: number) => {
    if (average >= 16) return "text-green-600";
    if (average >= 12) return "text-blue-600";
    if (average >= 10) return "text-yellow-600";
    return "text-red-600";
  };

  const getPassRateColor = (passRate: number) => {
    if (passRate >= 80) return "text-green-600";
    if (passRate >= 60) return "text-blue-600";
    if (passRate >= 40) return "text-yellow-600";
    return "text-red-600";
  };

  if (loading) {
    return (
      <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            <div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24 mb-2"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
            </div>
          </div>
        </div>
        <div className="space-y-3">
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-widget rounded-lg border border-red-200 p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
            <BookOpen className="h-5 w-5 text-red-600" />
          </div>
          <div>
            <h3 className="font-medium text-foreground">{className}</h3>
            <p className="text-sm text-foreground/60">{subjectName}</p>
          </div>
        </div>
        <p className="text-sm text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      onClick={handleCardClick}
      className="bg-widget rounded-lg border border-stroke p-6 cursor-pointer hover:shadow-lg transition-all duration-200 hover:border-teal"
    >
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-teal/10 rounded-lg flex items-center justify-center">
            <BookOpen className="h-5 w-5 text-teal" />
          </div>
          <div>
            <h3 className="font-medium text-foreground">{className}</h3>
            <p className="text-sm text-foreground/60">{subjectName}</p>
          </div>
        </div>
        <ArrowRight className="h-4 w-4 text-foreground/40" />
      </div>

      {/* Stats */}
      <div className="space-y-3">
        {/* Student Count */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Users className="h-4 w-4 text-foreground/60" />
            <span className="text-sm text-foreground/60">Students</span>
          </div>
          <span className="font-medium text-foreground">{studentCount}</span>
        </div>

        {/* Grade Stats */}
        {stats ? (
          <>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <TrendingUp className="h-4 w-4 text-foreground/60" />
                <span className="text-sm text-foreground/60">Average</span>
              </div>
              <span className={`font-medium ${getGradeColor(stats.averageScore)}`}>
                {stats.averageScore.toFixed(1)}/20
              </span>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Percent className="h-4 w-4 text-foreground/60" />
                <span className="text-sm text-foreground/60">Pass Rate</span>
              </div>
              <span className={`font-medium ${getPassRateColor(stats.passRate)}`}>
                {stats.passRate.toFixed(0)}%
              </span>
            </div>

            <div className="flex items-center justify-between">
              <span className="text-sm text-foreground/60">Total Grades</span>
              <span className="font-medium text-foreground">{stats.totalGrades}</span>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-between">
            <span className="text-sm text-foreground/60">Grades</span>
            <span className="text-sm text-foreground/40">No data</span>
          </div>
        )}
      </div>

      {/* Current Term Indicator */}
      {currentTerm && (
        <div className="mt-4 pt-3 border-t border-stroke">
          <div className="flex items-center justify-between">
            <span className="text-xs text-foreground/50">Current Term</span>
            <span className="text-xs text-teal font-medium">
              {currentTerm.name}
            </span>
          </div>
        </div>
      )}

      {/* Quick Actions Hint */}
      <div className="mt-4 pt-3 border-t border-stroke">
        <p className="text-xs text-foreground/40 text-center">
          Click to manage grades for this subject
        </p>
      </div>
    </motion.div>
  );
}
