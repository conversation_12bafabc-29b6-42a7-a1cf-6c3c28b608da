"use client";

import React from "react";

export default function TeacherAttendanceSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      {/* Header Skeleton */}
      <div className="bg-widget rounded-lg border border-stroke p-6">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
            <div>
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-48 mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
            </div>
          </div>
          
          <div className="flex items-center space-x-4">
            <div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-12 mb-1"></div>
              <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
            </div>
            <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-36"></div>
          </div>
        </div>

        {/* Stats Skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, index) => (
            <div key={index} className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-12 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16 mx-auto"></div>
            </div>
          ))}
        </div>
      </div>

      {/* Attendance List Skeleton */}
      <div className="bg-widget rounded-lg border border-stroke p-6">
        <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-40 mb-4"></div>
        
        <div className="space-y-3">
          {[...Array(8)].map((_, index) => (
            <div
              key={index}
              className="flex items-center justify-between p-4 border border-stroke rounded-lg"
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                <div>
                  <div className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-32 mb-1"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Status Buttons Skeleton */}
                <div className="flex space-x-2">
                  {[...Array(3)].map((_, btnIndex) => (
                    <div
                      key={btnIndex}
                      className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-16"
                    ></div>
                  ))}
                </div>

                {/* Remarks Input Skeleton */}
                <div className="w-32 h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions Skeleton */}
      <div className="flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0">
        <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
        
        <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-3 w-full sm:w-auto">
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full sm:w-32"></div>
          <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full sm:w-36"></div>
        </div>
      </div>
    </div>
  );
}
