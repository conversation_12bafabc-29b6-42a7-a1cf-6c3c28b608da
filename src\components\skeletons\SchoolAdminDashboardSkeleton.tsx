import React from 'react';

// Skeleton for stats card
export const DashboardStatsCardSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
        <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded w-1/3"></div>
      </div>
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-lg"></div>
    </div>
  </div>
);

// Skeleton for chart widget
export const DashboardChartSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-6"></div>
    <div className="h-64 bg-gray-300 dark:bg-gray-600 rounded"></div>
  </div>
);

// Skeleton for recent activities
export const DashboardActivitiesSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-3">
      {[...Array(5)].map((_, index) => (
        <div key={index} className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
          <div className="flex-1">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full mb-1"></div>
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
          </div>
          <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-12"></div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for quick actions
export const DashboardQuickActionsSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="grid grid-cols-2 gap-4">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Skeleton for announcements
export const DashboardAnnouncementsSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-4"></div>
    <div className="space-y-4">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
          <div className="flex items-start space-x-3">
            <div className="w-6 h-6 bg-gray-300 dark:bg-gray-600 rounded"></div>
            <div className="flex-1">
              <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-full mb-1"></div>
              <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
            </div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// Main skeleton component for dashboard page
export const SchoolAdminDashboardSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
      <div className="flex items-center space-x-3 mb-4">
        <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
        <div className="flex-1">
          <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
        </div>
      </div>
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
      {[...Array(4)].map((_, index) => (
        <DashboardStatsCardSkeleton key={index} />
      ))}
    </div>

    {/* Main content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Chart - takes 2 columns */}
      <div className="lg:col-span-2">
        <DashboardChartSkeleton />
      </div>

      {/* Quick actions - takes 1 column */}
      <div className="lg:col-span-1">
        <DashboardQuickActionsSkeleton />
      </div>
    </div>

    {/* Bottom content grid */}
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <DashboardActivitiesSkeleton />
      <DashboardAnnouncementsSkeleton />
    </div>
  </div>
);

export default SchoolAdminDashboardSkeleton;
