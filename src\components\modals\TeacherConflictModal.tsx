"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, Book<PERSON><PERSON>, Calendar } from "lucide-react";
import { motion } from "framer-motion";

interface ConflictDetails {
  teacher_name: string;
  class_name: string;
  subject_name: string;
  period_info: {
    period_number: number;
    start_time: string;
    end_time: string;
  } | null;
  day_of_week: string;
}

interface TeacherConflictModalProps {
  isOpen: boolean;
  onClose: () => void;
  conflictDetails: ConflictDetails;
  message: string;
}

export default function TeacherConflictModal({
  isOpen,
  onClose,
  conflictDetails,
  message
}: TeacherConflictModalProps) {
  if (!isOpen) return null;

  const formatTime = (time: string) => {
    try {
      // Handle both HH:mm and HH:mm:ss formats
      const timeParts = time.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = timeParts[1];
      
      if (hours === 0) return `12:${minutes} AM`;
      if (hours < 12) return `${hours}:${minutes} AM`;
      if (hours === 12) return `12:${minutes} PM`;
      return `${hours - 12}:${minutes} PM`;
    } catch {
      return time; // Return original if parsing fails
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        transition={{ duration: 0.2 }}
        className="bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 p-6 relative border border-stroke"
      >
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <h2 className="text-lg font-semibold text-foreground">Teacher Conflict</h2>
              <p className="text-sm text-foreground/60">Schedule conflict detected</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-foreground/60 hover:text-foreground transition-colors"
          >
            <X size={20} />
          </button>
        </div>

        {/* Alert Message */}
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
          <div className="flex items-start gap-3">
            <AlertTriangle className="h-5 w-5 text-red-600 dark:text-red-400 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="font-medium text-red-800 dark:text-red-200 mb-1">
                Scheduling Conflict
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300">
                {message}
              </p>
            </div>
          </div>
        </div>

        {/* Conflict Details */}
        <div className="space-y-4 mb-6">
          <h3 className="font-medium text-foreground mb-3">Conflict Details:</h3>
          
          {/* Teacher */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <Users className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Teacher</p>
              <p className="text-sm text-foreground/70">{conflictDetails.teacher_name}</p>
            </div>
          </div>

          {/* Current Assignment */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <BookOpen className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Currently Teaching</p>
              <p className="text-sm text-foreground/70">
                {conflictDetails.subject_name} - {conflictDetails.class_name}
              </p>
            </div>
          </div>

          {/* Time & Day */}
          <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <Clock className="h-4 w-4 text-foreground/60" />
            <div>
              <p className="text-sm font-medium text-foreground">Time Conflict</p>
              <p className="text-sm text-foreground/70">
                {conflictDetails.day_of_week}
                {conflictDetails.period_info && (
                  <span>
                    {" "}• Period {conflictDetails.period_info.period_number} 
                    ({formatTime(conflictDetails.period_info.start_time)} - {formatTime(conflictDetails.period_info.end_time)})
                  </span>
                )}
              </p>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex justify-end gap-3">
          <motion.button
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={onClose}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors font-medium"
          >
            Understood
          </motion.button>
        </div>
      </motion.div>
    </div>
  );
}
