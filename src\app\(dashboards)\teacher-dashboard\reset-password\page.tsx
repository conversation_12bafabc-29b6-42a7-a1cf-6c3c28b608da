"use client";

import { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Eye, EyeOff, Lock, CheckCircle, AlertCircle } from "lucide-react";
import Logo from "@/components/widgets/Logo";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";
const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

export default function TeacherResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  const [formData, setFormData] = useState({
    password: "",
    confirmPassword: ""
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [tokenValid, setTokenValid] = useState<boolean | null>(null);

  useEffect(() => {
    if (!token) {
      setError("Invalid or missing reset token");
      setTokenValid(false);
      return;
    }

    // Verify token validity (you can add an API call here)
    setTokenValid(true);
  }, [token]);

  const validatePassword = (password: string) => {
    const minLength = 8;
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

    return {
      minLength: password.length >= minLength,
      hasUpperCase,
      hasLowerCase,
      hasNumbers,
      hasSpecialChar,
      isValid: password.length >= minLength && hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChar
    };
  };

  const passwordValidation = validatePassword(formData.password);
  const passwordsMatch = formData.password === formData.confirmPassword;

  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    setError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!passwordValidation.isValid) {
      setError("Password does not meet requirements");
      return;
    }

    if (!passwordsMatch) {
      setError("Passwords do not match");
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      // API call to reset password
      const response = await fetch(`${BASE_API_URL}/auth/reset-password-token`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          token,
          password: formData.password,
          userType: "teacher"
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to reset password");
      }

      setSuccess(true);
      
      // Redirect to teacher dashboard after 3 seconds
      setTimeout(() => {
        router.push("/teacher-dashboard");
      }, 3000);

    } catch (error) {
      console.error("Error resetting password:", error);
      setError(error instanceof Error ? error.message : "Failed to reset password");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (tokenValid === false) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <Logo />
            <h1 className="text-2xl font-bold text-foreground mt-4 mb-2">Invalid Reset Link</h1>
          </div>
          
          <div className="bg-widget rounded-lg border border-stroke p-6 shadow-lg">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
              <p className="text-foreground/70 mb-4">
                {error || "The password reset link is invalid or has expired."}
              </p>
              <button
                onClick={() => router.push("/teacher-dashboard")}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
              >
                Go to Login
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center p-4">
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <Logo />
            <h1 className="text-2xl font-bold text-foreground mt-4 mb-2">Password Reset Successful</h1>
          </div>
          
          <div className="bg-widget rounded-lg border border-stroke p-6 shadow-lg">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <p className="text-foreground/70 mb-4">
                Your password has been successfully reset. You can now log in with your new password.
              </p>
              <p className="text-sm text-foreground/50">
                Redirecting to login page...
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Header */}
        <div className="text-center mb-8">
          <Logo />
          <h1 className="text-2xl font-bold text-foreground mt-4 mb-2">Set Your Password</h1>
          <p className="text-foreground/60">
            Create a secure password for your teacher account
          </p>
        </div>

        {/* Form */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-widget rounded-lg border border-stroke p-6 shadow-lg"
        >
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-lg">
              <p className="text-sm text-red-600 dark:text-red-300">{error}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* Password Input */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                  className="w-full px-3 py-2 pr-10 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  placeholder="Enter your new password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 hover:text-foreground/60"
                >
                  {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
            </div>

            {/* Password Requirements */}
            {formData.password && (
              <div className="space-y-2">
                <p className="text-sm font-medium text-foreground">Password Requirements:</p>
                <div className="space-y-1">
                  {[
                    { key: 'minLength', text: 'At least 8 characters' },
                    { key: 'hasUpperCase', text: 'One uppercase letter' },
                    { key: 'hasLowerCase', text: 'One lowercase letter' },
                    { key: 'hasNumbers', text: 'One number' },
                    { key: 'hasSpecialChar', text: 'One special character' }
                  ].map(({ key, text }) => (
                    <div key={key} className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${
                        passwordValidation[key as keyof typeof passwordValidation] 
                          ? 'bg-green-500' 
                          : 'bg-gray-300'
                      }`} />
                      <span className={`text-xs ${
                        passwordValidation[key as keyof typeof passwordValidation]
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-foreground/60'
                      }`}>
                        {text}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Confirm Password Input */}
            <div>
              <label className="block text-sm font-medium text-foreground mb-2">
                Confirm Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                  className="w-full px-3 py-2 pr-10 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
                  placeholder="Confirm your new password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/40 hover:text-foreground/60"
                >
                  {showConfirmPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                </button>
              </div>
              
              {formData.confirmPassword && (
                <div className="mt-1">
                  <div className="flex items-center space-x-2">
                    <div className={`w-2 h-2 rounded-full ${
                      passwordsMatch ? 'bg-green-500' : 'bg-red-500'
                    }`} />
                    <span className={`text-xs ${
                      passwordsMatch 
                        ? 'text-green-600 dark:text-green-400'
                        : 'text-red-600 dark:text-red-400'
                    }`}>
                      {passwordsMatch ? 'Passwords match' : 'Passwords do not match'}
                    </span>
                  </div>
                </div>
              )}
            </div>

            {/* Submit Button */}
            <motion.button
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              type="submit"
              disabled={isSubmitting || !passwordValidation.isValid || !passwordsMatch}
              className="w-full px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2 transition-colors"
            >
              {isSubmitting && <CircularLoader size={16} color="white" />}
              <Lock size={16} />
              <span>{isSubmitting ? "Setting Password..." : "Set Password"}</span>
            </motion.button>
          </form>

          {/* Footer */}
          <div className="mt-6 pt-4 border-t border-stroke text-center">
            <p className="text-sm text-foreground/60">
              After setting your password, you'll be redirected to the teacher dashboard.
            </p>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
