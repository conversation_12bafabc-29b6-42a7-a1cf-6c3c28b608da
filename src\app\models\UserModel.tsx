// user.model.ts (Frontend TypeScript Interfaces for User Management)

export interface UserSchema extends Record<string, unknown> {
    _id:string;
    user_id: string;               // Unique ID for the user (auto-generated by backend)
    firebaseUid?: string;           // Firebase UID (auto-generated by Firebase)
    name: string;                 // Name of the user (optional)
    email: string;                // Optional email of the user
    phone?: string;                // Optional phone number
    role: "super" | "admin" | "teacher" | "parent" | "school_admin" | "bursar" | "dean_of_studies"; // Role of the user (required)
    password?: string;             // Optional password (for non-'parent' roles)
    avatar?: string;               // Optional avatar URL
    address?: string;              // Optional address
    school_ids?: string[];         // Optional array of school IDs the user is associated with
    student_ids?: string[];        // Optional array of student IDs associated with the user (for 'teacher' or 'parent' roles)
    isVerified?: boolean;           // Verification status (true/false)
    verificationCode?: string;     // Optional verification code (used for email/phone verification)
    verificationCodeExpires?: string; // Optional expiration timestamp for verification code
    lastLogin?: string;           // Timestamp of last activity (optional)
    createdAt?: string;             // Timestamp of creation (auto-generated)
    updatedAt?: string;             // Timestamp of last update (auto-generated)
}

export interface UserCreateSchema extends Record<string, unknown> {
    user_id?: string;  
    name: string;                  // Name of the user (required)
    email: string;                // Optional email of the user (either email or phone must be provided)
    phone?: string;                // Optional phone number (either phone or email must be provided)
    role: "super" | "admin" | "teacher" | "parent" | "school_admin" | "bursar" | "dean_of_studies"; // Role of the user (required)
    password: string;             // Optional password (required for non-'parent' roles)
    avatar?: string;               // Optional avatar URL
    address: string;              // Optional address
    school_ids: string[];         // Optional array of school IDs the user is associated with
    isVerified?: boolean;          // Optional, default could be false if not provided
}


export interface UserUpdateSchema extends Record<string, unknown> {
    user_id: string;               // Required to identify which user to update
    firebaseUid?: string;          // Optional firebaseUid (auto-generated, no need to update)
    name?: string;                 // Optional name to update
    email?: string;                // Optional email to update (either email or phone must be provided)
    phone?: string;                // Optional phone number to update (either phone or email must be provided)
    role: "super" | "admin" | "teacher" | "parent" | "school_admin" | "bursar" | "dean_of_studies"; // Role of the user (required)
    password?: string;             // Optional password to update (required for non-'parent' roles)
    avatar?: string;               // Optional avatar URL to update
    address?: string;              // Optional address to update
    school_ids?: string[];         // Optional school IDs to update
    isVerified?: boolean;          // Optional verification status to update
    verificationCode?: string;     // Optional verification code to update
    verificationCodeExpires?: string; // Optional expiration timestamp to update
    lastLogin?: string;           // Optional last active timestamp to update
}
export interface UserDeleteSchema extends Record<string, unknown> {
    _id?:string;
    user_id: string; // Required: Unique ID of the user to delete
    name: string; 
}