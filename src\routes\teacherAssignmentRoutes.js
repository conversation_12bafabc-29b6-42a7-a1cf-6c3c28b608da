const express = require('express');
const { authenticate, authorize } = require('../middleware/middleware');
const { checkTeacherSchoolAccess } = require('../middleware/teacherMiddleware');
const User = require('../models/User');
const StaffPermission = require('../models/StaffPermission');
const Student = require('../models/Student');
const {
  getTeacherAssignmentsFromSchedule,
  getTeacherStudentsFromSchedule,
  syncTeacherAssignments
} = require('../utils/teacherAssignmentSync');
const { testTeacherAssignments } = require('../utils/testTeacherAssignments');
const ClassSchedule = require('../models/ClassSchedule');

// Import new teacher assignment controller
const {
  createTeacherAssignment,
  getTeacherAssignmentsBySchool,
  updateTeacherAssignment,
  deleteTeacherAssignment
} = require('../controllers/teacherAssignmentController');

const router = express.Router();

// ===== NEW TEACHER ASSIGNMENT ROUTES (Proper Logic) =====

// Create teacher assignment (school admin only)
router.post('/school/:school_id/assignments',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  createTeacherAssignment
);

// Get all teacher assignments for a school
router.get('/school/:school_id/assignments',
  authenticate,
  authorize(['school_admin', 'admin', 'super', 'teacher']),
  getTeacherAssignmentsBySchool
);

// Update teacher assignment
router.put('/assignments/:id',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  updateTeacherAssignment
);

// Delete teacher assignment
router.delete('/assignments/:id',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  deleteTeacherAssignment
);

// ===== LEGACY ROUTES (Based on ClassSchedule) =====

// Get teacher's assignments (classes and subjects) for a specific school
router.get('/assignments/:school_id',
  authenticate,
  authorize(['teacher']),
  async (req, res) => {
    try {
      const { school_id } = req.params;
      const teacher_id = req.user.id;

      console.log(`🎯 Teacher assignments request: teacher_id=${teacher_id}, school_id=${school_id}`);

      // Use the utility function to get assignments from ClassSchedule
      const assignments = await getTeacherAssignmentsFromSchedule(teacher_id, school_id);

      console.log(`✅ Returning assignments:`, {
        assigned_classes: assignments.assigned_classes?.length || 0,
        assigned_subjects: assignments.assigned_subjects?.length || 0,
        role_template: assignments.role_template
      });

      res.json(assignments);
    } catch (error) {
      console.error('❌ Error fetching teacher assignments:', error);
      res.status(500).json({ message: 'Internal server error', error: error.message });
    }
  }
);

// Get students in teacher's assigned classes
router.get('/students/:school_id',
  authenticate,
  authorize(['teacher']),
  async (req, res) => {
    try {
      const { school_id } = req.params;
      const teacher_id = req.user.id;

      // Use the utility function to get students from ClassSchedule
      const students = await getTeacherStudentsFromSchedule(teacher_id, school_id);

      res.json({ students });
    } catch (error) {
      console.error('Error fetching teacher students:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Get teacher's schedule based on ClassSchedule
router.get('/schedule/:school_id',
  authenticate,
  authorize(['teacher']),
  async (req, res) => {
    try {
      const { school_id } = req.params;
      const teacher_id = req.user.id;

      // Get teacher's schedule from ClassSchedule
      const ClassSchedule = require('../models/ClassSchedule');

      const scheduleEntries = await ClassSchedule.find({
        school_id,
        teacher_id
      })
      .populate('class_id', 'name')
      .populate('subject_id', 'name')
      .populate('period_id', 'period_number start_time end_time')
      .sort({ day_of_week: 1, 'period_id.period_number': 1 });

      // Format schedule for frontend
      const schedule = scheduleEntries.map(entry => ({
        day: entry.day_of_week,
        time: `${entry.period_id.start_time.slice(0, 5)}-${entry.period_id.end_time.slice(0, 5)}`,
        period: entry.period_id.period_number.toString(),
        period_id: entry.period_id._id,
        subject: entry.subject_id.name,
        subject_id: entry.subject_id._id,
        class_id: entry.class_id._id,
        class_name: entry.class_id.name,
        schedule_type: entry.schedule_type
      }));

      res.json({ schedule });
    } catch (error) {
      console.error('Error fetching teacher schedule:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Get teacher assigned to a specific class and subject
router.get('/class/:school_id/:class_id/subject/:subject_id',
  authenticate,
  authorize(['school_admin', 'admin', 'super', 'teacher']),
  async (req, res) => {
    try {
      const { school_id, class_id, subject_id } = req.params;
      const ClassSchedule = require('../models/ClassSchedule');

      // Find the teacher assigned to this specific class and subject
      // Since only one teacher can be assigned to a subject in a class, we get the first one
      const scheduleEntry = await ClassSchedule.findOne({
        school_id,
        class_id,
        subject_id,
        teacher_id: { $exists: true, $ne: null }
      })
      .populate('teacher_id', 'first_name last_name email name');

      if (!scheduleEntry || !scheduleEntry.teacher_id) {
        return res.json({
          teacher: null,
          teacher_name: 'Non assigné',
          message: 'No teacher assigned to this class and subject'
        });
      }

      // Format response
      const teacher = {
        teacher_id: scheduleEntry.teacher_id._id,
        teacher_name: scheduleEntry.teacher_id.first_name && scheduleEntry.teacher_id.last_name ?
            `${scheduleEntry.teacher_id.first_name} ${scheduleEntry.teacher_id.last_name}`
            :  `${entry.teacher_id.name}`,
        teacher_email: scheduleEntry.teacher_id.email
      };

      res.json({
        teacher,
        teacher_name: teacher.teacher_name,
        message: 'Teacher retrieved successfully'
      });
    } catch (error) {
      console.error('Error fetching teacher for class and subject:', error);
      res.status(500).json({ message: 'Internal server error', error:error });
    }
  }
);

// Get all teacher assignments for a class (all subjects)
router.get('/class/:school_id/:class_id',
  authenticate,
  authorize(['school_admin', 'admin', 'super', 'teacher']),
  async (req, res) => {
    try {
      const { school_id, class_id } = req.params;
      const ClassSchedule = require('../models/ClassSchedule');

      // Find unique teacher-subject combinations for this class
      // Since only one teacher can be assigned to a subject in a class, we get distinct combinations
      const scheduleEntries = await ClassSchedule.find({
        school_id,
        class_id,
        teacher_id: { $exists: true, $ne: null }
      })
      .populate('teacher_id', 'first_name last_name email name')
      .populate('subject_id', 'name')
      .select('teacher_id subject_id');

      // Group by subject and get the first (and should be only) teacher for each subject
      const subjectTeachers = {};

      scheduleEntries.forEach(entry => {
        if (entry.teacher_id && entry.subject_id) {
          const subjectId = entry.subject_id._id.toString();

          // Only add if we haven't seen this subject before (first teacher wins)
          if (!subjectTeachers[subjectId]) {
            const subjectName = entry.subject_id.name;
            const teacherName = entry.teacher_id.first_name && entry.teacher_id.last_name ?
              `${entry.teacher_id.first_name} ${entry.teacher_id.last_name}`
              : `${entry.teacher_id.name}`;

            subjectTeachers[subjectId] = {
              subject_id: subjectId,
              subject_name: subjectName,
              primary_teacher: teacherName,
              teacher_id: entry.teacher_id._id,
              teacher_email: entry.teacher_id.email,
            };
          }
        }
      });

      // Convert to array format
      const assignments = Object.values(subjectTeachers);

      res.json({
        assignments,
        message: 'Class teacher assignments retrieved successfully'
      });
    } catch (error) {
      console.error('Error fetching class teacher assignments:', error);
      res.status(500).json({ message: 'Internal server error', error:error });
    }
  }
);

// Sync teacher assignments from ClassSchedule to StaffPermission
router.post('/sync/:school_id/:teacher_id',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  async (req, res) => {
    try {
      const { school_id, teacher_id } = req.params;

      const result = await syncTeacherAssignments(teacher_id, school_id);

      if (result.success) {
        res.json({
          message: result.message,
          assigned_classes: result.assigned_classes
        });
      } else {
        res.status(500).json({
          message: 'Failed to sync teacher assignments',
          error: result.error
        });
      }
    } catch (error) {
      console.error('Error syncing teacher assignments:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  }
);

// Debug endpoint for testing teacher assignments (temporary)
router.get('/debug-assignments/:school_id',
  authenticate,
  authorize(['teacher']),
  async (req, res) => {
    try {
      const { school_id } = req.params;
      const teacher_id = req.user.id;

      console.log(`🔍 Debug endpoint called: teacher_id=${teacher_id}, school_id=${school_id}`);

      // Get raw schedule data
      const scheduleEntries = await ClassSchedule.find({
        school_id: school_id,
        teacher_id: teacher_id
      })
      .populate('class_id', 'name class_level')
      .populate('subject_id', 'name')
      .populate('period_id', 'period_number start_time end_time');

      // Test the utility function
      const assignments = await testTeacherAssignments(teacher_id, school_id);

      // Return detailed debug info
      res.json({
        debug_info: {
          teacher_id,
          school_id,
          raw_schedule_count: scheduleEntries.length,
          raw_schedule: scheduleEntries.map(entry => ({
            class_name: entry.class_id?.name,
            class_id: entry.class_id?._id,
            subject_name: entry.subject_id?.name,
            subject_id: entry.subject_id?._id,
            day_of_week: entry.day_of_week,
            period_number: entry.period_id?.period_number
          })),
          processed_assignments: assignments,
          analysis: {
            unique_classes: [...new Set(scheduleEntries.map(e => e.class_id?._id?.toString()).filter(Boolean))],
            unique_subjects_per_class: scheduleEntries.reduce((acc, entry) => {
              if (entry.class_id && entry.subject_id) {
                const key = `${entry.class_id._id}_${entry.subject_id._id}`;
                acc[key] = {
                  class_name: entry.class_id.name,
                  subject_name: entry.subject_id.name,
                  class_id: entry.class_id._id,
                  subject_id: entry.subject_id._id
                };
              }
              return acc;
            }, {})
          }
        }
      });

    } catch (error) {
      console.error('❌ Error in debug endpoint:', error);
      res.status(500).json({
        message: 'Internal server error',
        error: error.message,
        stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
      });
    }
  }
);

module.exports = router;
