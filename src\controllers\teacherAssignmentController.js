const TeacherAssignment = require('../models/TeacherAssignment');
const StaffPermission = require('../models/StaffPermission');
const User = require('../models/User');
const Class = require('../models/Class');
const Subject = require('../models/Subject');
const { ensureUniqueId } = require('../utils/generateId');

// Create a new teacher assignment
const createTeacherAssignment = async (req, res) => {
  try {
    const { school_id, teacher_id, class_id, subjects, academic_year } = req.body;

    // Validate required fields
    if (!school_id || !teacher_id || !class_id || !subjects || !academic_year) {
      return res.status(400).json({ 
        message: 'Missing required fields: school_id, teacher_id, class_id, subjects, academic_year' 
      });
    }

    // Validate that teacher exists and has teacher role
    const teacher = await User.findById(teacher_id);
    if (!teacher || teacher.role !== 'teacher') {
      return res.status(404).json({ message: 'Teacher not found or invalid role' });
    }

    // Validate that class exists
    const classDoc = await Class.findById(class_id);
    if (!classDoc) {
      return res.status(404).json({ message: 'Class not found' });
    }

    // Check for existing assignments for the same class and subjects
    const existingAssignments = await TeacherAssignment.find({
      school_id,
      class_id,
      subjects: { $in: subjects },
      is_active: true
    }).populate('teacher_id', 'first_name last_name name');

    if (existingAssignments.length > 0) {
      const conflicts = existingAssignments.map(assignment => ({
        subject: assignment.subjects.find(s => subjects.includes(s)),
        teacher: assignment.teacher_id.first_name && assignment.teacher_id.last_name 
          ? `${assignment.teacher_id.first_name} ${assignment.teacher_id.last_name}`
          : assignment.teacher_id.name
      }));
      
      return res.status(409).json({ 
        message: 'Some subjects are already assigned to other teachers',
        conflicts
      });
    }

    // Generate unique assignment ID
    const assignmentId = await ensureUniqueId(TeacherAssignment, 'assignment_id', 'TA');

    // Create the assignment
    const assignment = new TeacherAssignment({
      assignment_id: assignmentId,
      school_id,
      teacher_id,
      class_id,
      subjects,
      academic_year,
      assigned_by: req.user._id
    });

    await assignment.save();

    // Sync with StaffPermission
    await syncAssignmentToStaffPermission(teacher_id, school_id);

    // Populate the response
    const populatedAssignment = await TeacherAssignment.findById(assignment._id)
      .populate('teacher_id', 'first_name last_name name email')
      .populate('class_id', 'name class_code level')
      .populate('assigned_by', 'first_name last_name name');

    res.status(201).json({
      message: 'Teacher assignment created successfully',
      assignment: populatedAssignment
    });

  } catch (error) {
    console.error('Error creating teacher assignment:', error);
    res.status(500).json({ message: 'Internal server error', error: error.message });
  }
};

// Get all teacher assignments for a school
const getTeacherAssignmentsBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { teacher_id, class_id, academic_year } = req.query;

    // Build filter
    const filter = { school_id, is_active: true };
    if (teacher_id) filter.teacher_id = teacher_id;
    if (class_id) filter.class_id = class_id;
    if (academic_year) filter.academic_year = academic_year;

    const assignments = await TeacherAssignment.find(filter)
      .populate('teacher_id', 'first_name last_name name email')
      .populate('class_id', 'name class_code level')
      .populate('assigned_by', 'first_name last_name name')
      .sort({ 'class_id.name': 1, subjects: 1 });

    res.json({
      assignments,
      total: assignments.length
    });

  } catch (error) {
    console.error('Error fetching teacher assignments:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update teacher assignment
const updateTeacherAssignment = async (req, res) => {
  try {
    const { id } = req.params;
    const { subjects, academic_year } = req.body;

    const assignment = await TeacherAssignment.findById(id);
    if (!assignment) {
      return res.status(404).json({ message: 'Assignment not found' });
    }

    // Check for conflicts if subjects are being changed
    if (subjects && subjects.length > 0) {
      const existingAssignments = await TeacherAssignment.find({
        _id: { $ne: id },
        school_id: assignment.school_id,
        class_id: assignment.class_id,
        subjects: { $in: subjects },
        is_active: true
      }).populate('teacher_id', 'first_name last_name name');

      if (existingAssignments.length > 0) {
        const conflicts = existingAssignments.map(existing => ({
          subject: existing.subjects.find(s => subjects.includes(s)),
          teacher: existing.teacher_id.first_name && existing.teacher_id.last_name 
            ? `${existing.teacher_id.first_name} ${existing.teacher_id.last_name}`
            : existing.teacher_id.name
        }));
        
        return res.status(409).json({ 
          message: 'Some subjects are already assigned to other teachers',
          conflicts
        });
      }
    }

    // Update assignment
    if (subjects) assignment.subjects = subjects;
    if (academic_year) assignment.academic_year = academic_year;
    assignment.last_modified_by = req.user._id;
    assignment.last_modified_at = new Date();

    await assignment.save();

    // Sync with StaffPermission
    await syncAssignmentToStaffPermission(assignment.teacher_id, assignment.school_id);

    const updatedAssignment = await TeacherAssignment.findById(assignment._id)
      .populate('teacher_id', 'first_name last_name name email')
      .populate('class_id', 'name class_code level')
      .populate('assigned_by', 'first_name last_name name');

    res.json({
      message: 'Assignment updated successfully',
      assignment: updatedAssignment
    });

  } catch (error) {
    console.error('Error updating teacher assignment:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete teacher assignment
const deleteTeacherAssignment = async (req, res) => {
  try {
    const { id } = req.params;

    const assignment = await TeacherAssignment.findById(id);
    if (!assignment) {
      return res.status(404).json({ message: 'Assignment not found' });
    }

    // Soft delete
    assignment.is_active = false;
    assignment.last_modified_by = req.user._id;
    assignment.last_modified_at = new Date();
    await assignment.save();

    // Sync with StaffPermission
    await syncAssignmentToStaffPermission(assignment.teacher_id, assignment.school_id);

    res.json({ message: 'Assignment deleted successfully' });

  } catch (error) {
    console.error('Error deleting teacher assignment:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Helper function to sync assignments to StaffPermission
async function syncAssignmentToStaffPermission(teacherId, schoolId) {
  try {
    // Get all active assignments for this teacher in this school
    const assignments = await TeacherAssignment.find({
      teacher_id: teacherId,
      school_id: schoolId,
      is_active: true
    }).populate('class_id');

    // Group assignments by class
    const assignedClasses = assignments.reduce((acc, assignment) => {
      const classId = assignment.class_id._id.toString();
      
      if (!acc[classId]) {
        acc[classId] = {
          class_id: assignment.class_id._id,
          subjects: [],
          periods: [] // Empty for now, will be filled by timetable
        };
      }
      
      // Add subjects (avoid duplicates)
      assignment.subjects.forEach(subject => {
        if (!acc[classId].subjects.includes(subject)) {
          acc[classId].subjects.push(subject);
        }
      });
      
      return acc;
    }, {});

    // Update or create StaffPermission
    let staffPermission = await StaffPermission.findOne({
      user_id: teacherId,
      school_id: schoolId
    });

    if (staffPermission) {
      staffPermission.assigned_classes = Object.values(assignedClasses);
      staffPermission.last_modified_at = new Date();
      await staffPermission.save();
    }
    // If no StaffPermission exists, it will be created when the teacher is assigned permissions

    return { success: true };
  } catch (error) {
    console.error('Error syncing assignment to StaffPermission:', error);
    return { success: false, error: error.message };
  }
}

module.exports = {
  createTeacherAssignment,
  getTeacherAssignmentsBySchool,
  updateTeacherAssignment,
  deleteTeacherAssignment,
  syncAssignmentToStaffPermission
};
