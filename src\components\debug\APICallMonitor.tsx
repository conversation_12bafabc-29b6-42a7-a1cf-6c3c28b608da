"use client";

import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Activity, AlertTriangle } from 'lucide-react';

interface APICall {
  id: string;
  url: string;
  method: string;
  timestamp: number;
  status?: number;
}

interface APICallMonitorProps {
  enabled?: boolean;
  maxCalls?: number;
}

const APICallMonitor: React.FC<APICallMonitorProps> = ({ 
  enabled = process.env.NODE_ENV === 'development',
  maxCalls = 50 
}) => {
  const [calls, setCalls] = useState<APICall[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [duplicateWarnings, setDuplicateWarnings] = useState<string[]>([]);

  useEffect(() => {
    if (!enabled) return;

    // Intercepter les appels fetch
    const originalFetch = window.fetch;
    
    window.fetch = async (...args) => {
      const [url, options] = args;
      const method = options?.method || 'GET';
      const fullUrl = typeof url === 'string' ? url : url.toString();
      
      const callId = `${Date.now()}-${Math.random()}`;
      const timestamp = Date.now();
      
      // Ajouter l'appel à la liste
      setCalls(prev => {
        const newCall: APICall = {
          id: callId,
          url: fullUrl,
          method,
          timestamp
        };
        
        // Vérifier les doublons récents (dans les 5 dernières secondes)
        const recentCalls = prev.filter(call => timestamp - call.timestamp < 5000);
        const duplicates = recentCalls.filter(call => 
          call.url === fullUrl && call.method === method
        );
        
        if (duplicates.length >= 3) {
          setDuplicateWarnings(prevWarnings => {
            const warningKey = `${method} ${fullUrl}`;
            if (!prevWarnings.includes(warningKey)) {
              return [...prevWarnings, warningKey];
            }
            return prevWarnings;
          });
        }
        
        const updated = [newCall, ...prev].slice(0, maxCalls);
        return updated;
      });

      try {
        const response = await originalFetch(...args);
        
        // Mettre à jour avec le statut
        setCalls(prev => 
          prev.map(call => 
            call.id === callId 
              ? { ...call, status: response.status }
              : call
          )
        );
        
        return response;
      } catch (error) {
        // Mettre à jour avec erreur
        setCalls(prev => 
          prev.map(call => 
            call.id === callId 
              ? { ...call, status: 0 }
              : call
          )
        );
        throw error;
      }
    };

    // Restaurer fetch original au démontage
    return () => {
      window.fetch = originalFetch;
    };
  }, [enabled, maxCalls]);

  if (!enabled) return null;

  const getStatusColor = (status?: number) => {
    if (!status) return 'text-red-500';
    if (status >= 200 && status < 300) return 'text-green-500';
    if (status >= 400) return 'text-red-500';
    return 'text-yellow-500';
  };

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const userEmailCalls = calls.filter(call => call.url.includes('/user/get-user-email/'));
  const hasInfiniteLoop = userEmailCalls.length > 10;

  return (
    <>
      {/* Bouton flottant */}
      <motion.button
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        onClick={() => setIsVisible(!isVisible)}
        className={`fixed bottom-4 right-4 z-50 p-3 rounded-full shadow-lg ${
          hasInfiniteLoop ? 'bg-red-500 animate-pulse' : 'bg-blue-500'
        } text-white hover:scale-110 transition-transform`}
        title={`API Calls: ${calls.length}${hasInfiniteLoop ? ' - INFINITE LOOP DETECTED!' : ''}`}
      >
        <Activity className="h-5 w-5" />
        {calls.length > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-6 w-6 flex items-center justify-center">
            {calls.length > 99 ? '99+' : calls.length}
          </span>
        )}
      </motion.button>

      {/* Panel de monitoring */}
      <AnimatePresence>
        {isVisible && (
          <motion.div
            initial={{ opacity: 0, x: 400 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 400 }}
            className="fixed top-0 right-0 h-full w-96 bg-white dark:bg-gray-800 shadow-2xl z-40 overflow-hidden flex flex-col"
          >
            {/* Header */}
            <div className="p-4 border-b border-gray-200 dark:border-gray-700 flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <h3 className="font-semibold">API Calls Monitor</h3>
                {hasInfiniteLoop && (
                  <AlertTriangle className="h-5 w-5 text-red-500" />
                )}
              </div>
              <button
                onClick={() => setIsVisible(false)}
                className="p-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded"
              >
                <X className="h-4 w-4" />
              </button>
            </div>

            {/* Warnings */}
            {duplicateWarnings.length > 0 && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border-b border-red-200 dark:border-red-800">
                <div className="flex items-center space-x-2 mb-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  <span className="text-sm font-medium text-red-700 dark:text-red-300">
                    Infinite Loop Detected!
                  </span>
                </div>
                {duplicateWarnings.map((warning, index) => (
                  <div key={index} className="text-xs text-red-600 dark:text-red-400">
                    {warning}
                  </div>
                ))}
                <button
                  onClick={() => setDuplicateWarnings([])}
                  className="text-xs text-red-500 hover:text-red-700 mt-2"
                >
                  Clear warnings
                </button>
              </div>
            )}

            {/* Stats */}
            <div className="p-4 bg-gray-50 dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-gray-500">Total Calls:</span>
                  <span className="ml-2 font-semibold">{calls.length}</span>
                </div>
                <div>
                  <span className="text-gray-500">User Email:</span>
                  <span className={`ml-2 font-semibold ${userEmailCalls.length > 5 ? 'text-red-500' : ''}`}>
                    {userEmailCalls.length}
                  </span>
                </div>
              </div>
              <button
                onClick={() => setCalls([])}
                className="mt-2 text-xs text-blue-500 hover:text-blue-700"
              >
                Clear all
              </button>
            </div>

            {/* Liste des appels */}
            <div className="flex-1 overflow-y-auto">
              {calls.map((call) => (
                <div
                  key={call.id}
                  className="p-3 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-xs font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                      {call.method}
                    </span>
                    <span className={`text-xs ${getStatusColor(call.status)}`}>
                      {call.status || 'pending'}
                    </span>
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400 break-all">
                    {call.url}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {formatTime(call.timestamp)}
                  </div>
                </div>
              ))}
              
              {calls.length === 0 && (
                <div className="p-8 text-center text-gray-500">
                  No API calls yet
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  );
};

export default APICallMonitor;
