import React from 'react';
import CircularLoader from './CircularLoader';

interface LoadingSpinnerProps {
  size?: 'small' | 'medium' | 'large';
  color?: string;
  className?: string;
  fullScreen?: boolean;
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'medium',
  color = 'teal',
  className = '',
  fullScreen = false
}) => {
  // Convertir la taille en pixels
  const sizeMap = {
    small: 20,
    medium: 32,
    large: 48
  };

  const spinnerSize = sizeMap[size];

  if (fullScreen) {
    return (
      <div className="fixed inset-0 flex items-center justify-center z-[100] bg-black/50">
        <div className="bg-white rounded-lg p-6 shadow-lg">
          <CircularLoader size={spinnerSize} color={color} />
        </div>
      </div>
    );
  }

  return (
    <div className={`flex items-center justify-center ${className}`}>
      <CircularLoader size={spinnerSize} color={color} />
    </div>
  );
};

export default LoadingSpinner;
