import React from 'react';
import DataTableWithBulkActions from '@/components/enhanced/DataTableWithBulkActions';
import { UserSchema } from '@/app/models/UserModel';
import { deleteMultipleUsers, deleteAllUsers } from '@/app/services/UserServices';

interface UsersTableWithBulkActionsProps {
  users: UserSchema[];
  setUsers: React.Dispatch<React.SetStateAction<UserSchema[]>>;
  columns: any[];
  actions: any[];
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: UserSchema[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const UsersTableWithBulkActions: React.FC<UsersTableWithBulkActionsProps> = ({
  users,
  setUsers,
  columns,
  actions,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  onSuccess,
  onError
}) => {
  return (
    <DataTableWithBulkActions
      columns={columns}
      data={users}
      actions={actions}
      defaultItemsPerPage={5}
      loading={loading}
      onLoadingChange={onLoadingChange}
      onSelectionChange={onSelectionChange}
      idAccessor="_id"
      enableBulkActions={true}
      deleteMultipleService={deleteMultipleUsers}
      deleteAllService={deleteAllUsers}
      itemName="user"
      setItems={setUsers}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
};

export default UsersTableWithBulkActions;
