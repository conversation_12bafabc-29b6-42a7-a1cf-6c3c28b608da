const StaffPermission = require('../models/StaffPermission');

/**
 * Middleware to check if a teacher has access to specific school data
 * Adds teacher information to req.teacher for use in controllers
 */
const checkTeacherSchoolAccess = async (req, res, next) => {
  try {
    // Only apply this middleware for teachers
    if (req.user.role !== 'teacher') {
      return next();
    }

    const schoolId = req.params.school_id || req.body.school_id;

    if (!schoolId) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // req.user is already the User object from MongoDB (set by authorize middleware)
    const user = req.user;
    if (!user || !user._id) {
      return res.status(403).json({ message: 'User not found' });
    }

    // Check if teacher has access to this school
    const hasSchoolAccess = user.school_ids.includes(schoolId) ||
                           user.access_codes.some(code =>
                             code.school_id.toString() === schoolId && code.is_active
                           );

    if (!hasSchoolAccess) {
      return res.status(403).json({
        message: 'Teacher not authorized to access this school'
      });
    }

    // Get teacher's permissions and assignments for this school
    const staffPermission = await StaffPermission.findOne({
      user_id: user._id,
      school_id: schoolId,
      is_active: true
    }).populate('assigned_classes.class_id');

    if (!staffPermission) {
      return res.status(403).json({
        message: 'No permissions found for this teacher in this school'
      });
    }

    // Add teacher info to request for use in controllers
    req.teacher = {
      user_id: user._id,
      school_id: schoolId,
      permissions: staffPermission.permissions,
      assigned_classes: staffPermission.assigned_classes,
      assigned_class_ids: staffPermission.assigned_classes.map(cls =>
        cls.class_id._id ? cls.class_id._id.toString() : cls.class_id.toString()
      ),
      assigned_subjects: staffPermission.assigned_classes.flatMap(cls => cls.subjects || []),
      role_template: staffPermission.role_template
    };

    next();
  } catch (error) {
    console.error('Error in teacher school access middleware:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Middleware to check if a teacher can access specific class/subject data
 */
const checkTeacherClassSubjectAccess = (req, res, next) => {
  try {
    // Only apply this middleware for teachers
    if (req.user.role !== 'teacher' || !req.teacher) {
      return next();
    }

    const classId = req.body.class_id || req.query.class_id;
    const subjectName = req.body.subject || req.query.subject;

    // Check class access
    if (classId && !req.teacher.assigned_class_ids.includes(classId)) {
      return res.status(403).json({
        message: 'You are not authorized to access this class'
      });
    }

    // Check subject access (subjects are stored as strings in assigned_classes)
    if (subjectName && !req.teacher.assigned_subjects.includes(subjectName)) {
      return res.status(403).json({
        message: 'You are not authorized to access this subject'
      });
    }

    next();
  } catch (error) {
    console.error('Error in teacher class/subject access middleware:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

/**
 * Helper function to apply teacher filters to a MongoDB query filter
 * Note: For Grade model, we can't filter by class_id directly since grades don't have class_id field.
 * The class filtering should be done in the aggregation pipeline after joining with students.
 */
const applyTeacherFilters = (filter, req) => {
  if (req.user.role === 'teacher' && req.teacher) {
    // For Grade model, we can't filter by class_id directly
    // The class filtering will be handled in the aggregation pipeline
    // We can only filter by subjects here if needed

    // Store teacher info for use in aggregation pipeline
    req.teacherClassIds = req.teacher.assigned_class_ids;
    req.teacherSubjects = req.teacher.assigned_subjects;

    // If teacher has no assigned classes, return impossible filter
    if (req.teacher.assigned_class_ids.length === 0) {
      filter._id = { $in: [] }; // This will return no results
    }
  }

  return filter;
};

/**
 * Helper function to check if teacher has specific permission
 */
const hasPermission = (req, module, permission) => {
  if (req.user.role !== 'teacher' || !req.teacher) {
    return false;
  }

  return req.teacher.permissions[module] && req.teacher.permissions[module][permission];
};

module.exports = {
  checkTeacherSchoolAccess,
  checkTeacherClassSubjectAccess,
  applyTeacherFilters,
  hasPermission
};
