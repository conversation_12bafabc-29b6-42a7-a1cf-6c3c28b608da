"use client";

import React, { useEffect, useState } from "react";
import { X } from "lucide-react";
import { ClassLevelSchema } from "@/app/models/ClassLevel";
import CustomInput from "@/components/inputs/CustomInput";
import { ClassSchema } from "@/app/models/ClassModel";
import { motion } from "framer-motion";
import CircularLoader from "@/components/widgets/CircularLoader";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import ActionButton from "@/components/ActionButton";

interface UpdateClassModalProps {
  onClose: () => void;
  onSave: (data: ClassSchema) => Promise<void>;
  initialData?: ClassSchema;
  classLevels: ClassLevelSchema[];  // Full list of class levels passed as prop
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const UpdateClassModal: React.FC<UpdateClassModalProps> = ({
  onClose,
  onSave,
  initialData,
  classLevels,
  submitStatus,
  isSubmitting,
}) => {
  const [formData, setFormData] = useState<ClassSchema>({
    _id: "",
    class_id: "",
    name: "",
    class_code: "",
    class_level: "",
    school_id: "",
  });

  useEffect(() => {
    if (initialData) {
      setFormData({
        _id: initialData._id,
        class_id: initialData.class_id,
        name: initialData.name,
        class_code: initialData.class_code,
        class_level: initialData.class_level,  // Set initial class level from initialData
        school_id: initialData.school_id,
      });
    }
  }, [initialData]);


  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);  // Save the form data
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4 sm:mx-6 md:mx-0 p-6 relative">
        {/* Header */}
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Update Class</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>
        {submitStatus ? (
          <SubmissionFeedback status={submitStatus}
            message={
              submitStatus === "success"
                ? "Class has been sent Successfully!"
                : "There was an error updating this class. Try again and if this persist contact support!"
            } />
        ) : (<>
          <form onSubmit={handleSubmit}>
            {/* Class Name */}
            <CustomInput
              label="Class Name"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleChange}
              required
            />

            {/* Class Code */}
            <CustomInput
              label="Class Code"
              id="class_code"
              name="class_code"
              value={formData.class_code}
              onChange={handleChange}
              required
            />

            {/* Class Level Dropdown */}
            <div className="mb-4">
              <label htmlFor="class_level" className="block text-sm mb-1">
                Class Level
              </label>
              <select
                name="class_level"
                id="class_level"
                value={formData.class_level}
                onChange={handleChange}
                className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-teal"
                required
              >
                <option value="">Select level</option>
                {classLevels.map((level) => (
                  <option key={level._id} value={level._id}>
                    {level.name}
                  </option>
                ))}
              </select>
            </div>

            {/* Actions */}
            <div className="flex justify-end space-x-2 mt-6">
              <ActionButton
                action="cancel"
                label="Cancel"
                onClick={onClose}
                type="button"
              />
              <ActionButton
                action="save"
                type="submit"
                isLoading={isSubmitting}
              />

            </div>
          </form>
        </>)}

      </div>
    </div>
  );
};

export default UpdateClassModal;
