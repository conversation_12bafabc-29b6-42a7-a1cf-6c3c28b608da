# Test Plan: Bulk Delete Features

## Testing Checklist

### 🏫 Schools Section Tests

#### DataTable Behavior
- [ ] **No Selection State**
  - No bulk action bar visible
  - Only individual action buttons (View, Delete) visible
  - Checkboxes available for selection

- [ ] **Partial Selection State**
  - Bulk action bar appears with animation
  - Shows "X of Y selected" counter
  - "Select All (Y)" button visible
  - "Deselect All" button visible  
  - "Delete Selected (X)" button visible
  - No "Delete All" button

- [ ] **Full Selection State**
  - Bulk action bar shows "X of Y selected"
  - "Deselect All" button visible
  - "Delete All (X)" button visible (replaces Delete Selected)
  - No "Select All" button

#### Modal Functionality
- [ ] **Delete Selected Modal**
  - Opens when "Delete Selected" clicked
  - Shows correct title: "Delete Selected Schools"
  - Shows correct count and warning level
  - Two-step confirmation process works
  - Password validation works
  - Success/failure feedback works
  - <PERSON>dal closes after successful operation

- [ ] **Delete All Modal**
  - Opens when "Delete All" clicked
  - Shows correct title: "Delete All Schools"
  - Shows extreme warning (red theme)
  - Emphasizes destructive nature
  - Password validation works
  - Success/failure feedback works

#### API Integration
- [ ] **Delete Selected**
  - Calls `deleteMultipleSchools` with correct IDs
  - Handles API errors gracefully
  - Refreshes data after success
  - Clears selection after success

- [ ] **Delete All**
  - Calls `deleteAllSchools` endpoint
  - Handles API errors gracefully
  - Refreshes data after success
  - Clears selection after success

### 💳 Subscription Section Tests

#### DataTable Behavior
- [ ] **Clean Interface**
  - No checkboxes visible
  - No bulk action bar ever appears
  - Only individual action buttons (Edit, Delete) visible
  - Selection functionality disabled

- [ ] **Individual Operations Only**
  - Edit button works for individual subscriptions
  - Delete button works for individual subscriptions
  - No way to select multiple items
  - No bulk operation buttons

#### Modal Functionality
- [ ] **Individual Delete Modal**
  - Uses existing `DeleteUserModal`
  - Shows subscription/parent name
  - Password confirmation works
  - Success/failure feedback works

## Manual Testing Steps

### Schools Section

1. **Navigate to Schools Page**
   ```
   /super-admin/schools
   ```

2. **Test Selection States**
   - Start with no selection
   - Select 1 item → verify partial selection UI
   - Select all items → verify full selection UI
   - Deselect all → verify return to no selection

3. **Test Delete Selected**
   - Select 2-3 schools
   - Click "Delete Selected"
   - Verify modal opens with correct info
   - Test password validation (wrong password)
   - Test successful deletion (correct password)

4. **Test Delete All**
   - Select all schools
   - Click "Delete All"
   - Verify extreme warning modal
   - Test password validation
   - Test successful deletion

### Subscription Section

1. **Navigate to Subscription Page**
   ```
   /super-admin/subscription
   ```

2. **Verify Clean Interface**
   - Confirm no checkboxes visible
   - Confirm no bulk action buttons
   - Confirm only individual actions available

3. **Test Individual Delete**
   - Click delete on one subscription
   - Verify modal opens
   - Test password validation
   - Test successful deletion

## Automated Testing

### Component Tests
```typescript
// BulkDeleteModal.test.tsx
describe('BulkDeleteModal', () => {
  it('shows correct warning level for different item counts', () => {
    // Test low (≤5), medium (6-10), high (>10), extreme (delete all)
  });
  
  it('handles two-step confirmation flow', () => {
    // Test first confirmation → password step → final confirmation
  });
  
  it('validates password correctly', () => {
    // Test password validation and error handling
  });
});
```

### Integration Tests
```typescript
// Schools.test.tsx
describe('Schools Page', () => {
  it('shows bulk actions when items selected', () => {
    // Test DataTable bulk action visibility
  });
  
  it('calls correct API endpoints for bulk operations', () => {
    // Mock API calls and verify correct endpoints called
  });
});

// Subscription.test.tsx  
describe('Subscription Page', () => {
  it('hides bulk actions and checkboxes', () => {
    // Verify bulk actions are disabled
  });
  
  it('only allows individual operations', () => {
    // Test that only individual actions work
  });
});
```

## Performance Testing

### Large Dataset Tests
- [ ] Test with 100+ schools
- [ ] Verify bulk operations performance
- [ ] Check UI responsiveness during operations
- [ ] Validate memory usage

### Network Tests
- [ ] Test with slow network
- [ ] Test with network failures
- [ ] Verify error handling
- [ ] Check loading states

## Security Testing

### Authentication Tests
- [ ] Test with invalid JWT token
- [ ] Test with expired token
- [ ] Verify proper error handling

### Authorization Tests
- [ ] Test with non-super user role
- [ ] Verify access restrictions
- [ ] Test permission boundaries

### Password Validation Tests
- [ ] Test with incorrect password
- [ ] Test with empty password
- [ ] Verify password security

## Browser Compatibility

### Desktop Browsers
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)

### Mobile Browsers
- [ ] Mobile Chrome
- [ ] Mobile Safari
- [ ] Mobile Firefox

### Responsive Design
- [ ] Test on different screen sizes
- [ ] Verify modal responsiveness
- [ ] Check button layouts

## Accessibility Testing

### Keyboard Navigation
- [ ] Tab through all interactive elements
- [ ] Test modal keyboard navigation
- [ ] Verify escape key closes modals

### Screen Reader
- [ ] Test with screen reader
- [ ] Verify proper ARIA labels
- [ ] Check focus management

### Color Contrast
- [ ] Verify warning color contrasts
- [ ] Check button visibility
- [ ] Test in high contrast mode

## Error Scenarios

### API Errors
- [ ] Network timeout
- [ ] Server error (500)
- [ ] Authentication failure (401)
- [ ] Authorization failure (403)
- [ ] Not found (404)

### User Errors
- [ ] Invalid password
- [ ] Empty selection
- [ ] Cancelled operations
- [ ] Browser refresh during operation

## Success Criteria

✅ **All tests pass**
✅ **No console errors**
✅ **Smooth user experience**
✅ **Proper error handling**
✅ **Security requirements met**
✅ **Performance acceptable**
✅ **Accessibility compliant**
