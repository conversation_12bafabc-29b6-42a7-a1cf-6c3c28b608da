// Types pour le système de souscription par crédits

export type PlanType = 'basic' | 'standard' | 'custom';
export type SubscriptionStatus = 'active' | 'expired' | 'suspended' | 'trial';
export type PaymentStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled' | 'refunded';
export type UsageType = 'student_creation' | 'chatbot_message' | 'manual_deduction' | 'refund' | 'bonus';

// Interface pour la souscription d'école
export interface SchoolSubscriptionSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  plan_type: PlanType;
  status: SubscriptionStatus;
  credits_balance: number;
  credits_purchased: number;
  credits_used: number;
  subscription_start: string;
  subscription_end?: string;
  monthly_chatbot_limit: number;
  chatbot_usage_current_month: number;
  chatbot_usage_reset_date: string;
  features: string[];
  credit_purchases: string[];
  low_credit_threshold: number;
  notifications_enabled: boolean;
  last_credit_purchase?: string;
  last_credit_usage?: string;
  admin_notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

// Interface pour les plans de souscription
export interface SubscriptionPlanSchema extends Record<string, unknown> {
  _id: string;
  plan_name: PlanType;
  display_name: string;
  description: string;
  price_per_credit: number;
  minimum_purchase: number;
  chatbot_enabled: boolean;
  chatbot_cost_per_message: number;
  chatbot_monthly_limit: number;
  features: FeatureSchema[];
  benefits: string[];
  limitations: string[];
  is_active: boolean;
  is_popular: boolean;
  display_order: number;
  color_theme: string;
  contact_info?: ContactInfoSchema;
  createdAt?: string;
  updatedAt?: string;
}

export interface FeatureSchema {
  name: string;
  description: string;
  enabled: boolean;
}

export interface ContactInfoSchema {
  email?: string;
  phone?: string;
  message?: string;
}

// Interface pour l'achat de crédits
export interface CreditPurchaseSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  subscription_id: string;
  transaction_id: string;
  purchase_id: string;
  credits_purchased: number;
  price_per_credit: number;
  total_amount: number;
  currency: string;
  payment_method: string;
  payment_status: PaymentStatus;
  payment_gateway_response: Record<string, any>;
  purchase_date: string;
  payment_completed_date?: string;
  purchased_by: string;
  purchaser_email: string;
  billing_info?: BillingInfoSchema;
  promotion_code?: string;
  discount_amount: number;
  discount_percentage: number;
  notes?: string;
  admin_notes?: string;
  refund_info?: RefundInfoSchema;
  is_processed: boolean;
  processed_date?: string;
  processed_by?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface BillingInfoSchema {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
  organization?: string;
}

export interface RefundInfoSchema {
  refund_date?: string;
  refund_amount?: number;
  refund_reason?: string;
  refunded_by?: string;
}

// Interface pour l'utilisation des crédits
export interface CreditUsageSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  subscription_id: string;
  usage_type: UsageType;
  credits_used: number;
  reference_id?: string;
  reference_type?: string;
  used_by: string;
  usage_date: string;
  description: string;
  details: UsageDetailsSchema;
  balance_before: number;
  balance_after: number;
  session_info: SessionInfoSchema;
  status: 'completed' | 'failed' | 'reversed';
  reversal_info?: ReversalInfoSchema;
  admin_notes?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface UsageDetailsSchema {
  student_name?: string;
  student_id?: string;
  class_name?: string;
  message_content?: string;
  response_length?: number;
  conversation_id?: string;
  admin_reason?: string;
  admin_notes?: string;
  metadata?: Record<string, any>;
}

export interface SessionInfoSchema {
  ip_address?: string;
  user_agent?: string;
  device_type?: string;
}

export interface ReversalInfoSchema {
  reversed_date?: string;
  reversed_by?: string;
  reversal_reason?: string;
}

// Interface pour l'utilisation du chatbot
export interface ChatbotUsageSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;
  user_id: string;
  subscription_id: string;
  conversation_id: string;
  user_message: string;
  bot_response: string;
  message_metadata: MessageMetadataSchema;
  credits_charged: number;
  usage_date: string;
  session_info: SessionInfoSchema;
  conversation_context: ConversationContextSchema;
  feedback?: FeedbackSchema;
  status: 'completed' | 'failed' | 'timeout' | 'cancelled';
  error_info?: ErrorInfoSchema;
  billing_period: BillingPeriodSchema;
  quality_metrics?: QualityMetricsSchema;
  createdAt?: string;
  updatedAt?: string;
}

export interface MessageMetadataSchema {
  message_length?: number;
  response_length?: number;
  processing_time?: number;
  model_used?: string;
  tokens_used?: number;
  confidence_score?: number;
}

export interface ConversationContextSchema {
  previous_messages_count?: number;
  conversation_duration?: number;
  topic_category?: string;
  intent_detected?: string;
}

export interface FeedbackSchema {
  rating?: number;
  helpful?: boolean;
  feedback_text?: string;
  feedback_date?: string;
}

export interface ErrorInfoSchema {
  error_code?: string;
  error_message?: string;
  error_details?: Record<string, any>;
}

export interface BillingPeriodSchema {
  year: number;
  month: number;
}

export interface QualityMetricsSchema {
  response_relevance?: number;
  user_satisfaction?: number;
  resolution_achieved?: boolean;
}

// Interfaces pour les statistiques et rapports
export interface SubscriptionStatsSchema {
  total_schools: number;
  active_subscriptions: number;
  total_credits_sold: number;
  total_revenue: number;
  plan_distribution: PlanDistributionSchema[];
  monthly_growth: number;
  churn_rate: number;
}

export interface PlanDistributionSchema {
  plan_type: PlanType;
  count: number;
  percentage: number;
  revenue: number;
}

export interface CreditUsageStatsSchema {
  total_usage: number;
  usage_by_type: UsageByTypeSchema[];
  daily_usage: DailyUsageSchema[];
  top_users: TopUserSchema[];
  efficiency_metrics: EfficiencyMetricsSchema;
}

export interface UsageByTypeSchema {
  usage_type: UsageType;
  total_credits: number;
  count: number;
  percentage: number;
}

export interface DailyUsageSchema {
  date: string;
  total_credits: number;
  count: number;
}

export interface TopUserSchema {
  user_id: string;
  user_name: string;
  total_credits: number;
  usage_count: number;
}

export interface EfficiencyMetricsSchema {
  avg_credits_per_student: number;
  avg_chatbot_messages_per_user: number;
  peak_usage_hours: number[];
  cost_per_active_user: number;
}

// Interfaces pour les formulaires
export interface CreditPurchaseFormSchema {
  credits_amount: number;
  payment_method: string;
  billing_info: BillingInfoSchema;
  promotion_code?: string;
}

export interface SubscriptionUpdateFormSchema {
  plan_type: PlanType;
  features: string[];
  low_credit_threshold: number;
  notifications_enabled: boolean;
  admin_notes?: string;
}
