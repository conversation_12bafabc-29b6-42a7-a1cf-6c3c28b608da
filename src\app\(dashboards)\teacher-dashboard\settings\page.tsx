"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Settings, User, Bell, Shield, Palette, Globe, Save, Eye, EyeOff } from "lucide-react";
import { motion } from "framer-motion";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { useToast, ToastContainer } from "@/components/ui/Toast";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface UserSettings {
  profile: {
    first_name: string;
    last_name: string;
    email: string;
    phone: string;
    bio: string;
    avatar: string;
  };
  notifications: {
    email_announcements: boolean;
    email_grades: boolean;
    email_attendance: boolean;
    push_notifications: boolean;
  };
  privacy: {
    profile_visibility: 'public' | 'school' | 'private';
    show_email: boolean;
    show_phone: boolean;
  };
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
  };
}

const navigation = {
  icon: Settings,
  baseHref: "/teacher-dashboard/settings",
  title: "Settings"
};

export default function TeacherSettingsPage() {
  const { logout, user } = useAuth();
  const router = useRouter();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loadingSchool, setLoadingSchool] = useState(true);
  const [saving, setSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');
  const [showPassword, setShowPassword] = useState(false);

  // Settings state
  const [settings, setSettings] = useState<UserSettings>({
    profile: {
      first_name: user?.first_name || '',
      last_name: user?.last_name || '',
      email: user?.email || '',
      phone: user?.phone || '',
      bio: user?.bio || '',
      avatar: user?.avatar || ''
    },
    notifications: {
      email_announcements: true,
      email_grades: true,
      email_attendance: true,
      push_notifications: true
    },
    privacy: {
      profile_visibility: 'school',
      show_email: false,
      show_phone: false
    },
    preferences: {
      theme: 'system',
      language: 'en',
      timezone: 'UTC'
    }
  });

  // Password change state
  const [passwordData, setPasswordData] = useState({
    current_password: '',
    new_password: '',
    confirm_password: ''
  });

  // Load selected school from localStorage
  useEffect(() => {
    const savedSchool = localStorage.getItem('teacher_selected_school');
    if (savedSchool) {
      setSelectedSchool(JSON.parse(savedSchool));
    }
    setLoadingSchool(false);
  }, []);

  // Handle school change
  const handleSchoolChange = () => {
    router.push('/teacher-dashboard');
  };

  // Handle settings update
  const handleSettingsUpdate = (section: keyof UserSettings, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  // Handle save settings
  const handleSaveSettings = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess("Settings Saved", "Your settings have been updated successfully.");
    } catch (error) {
      showError("Error", "Failed to save settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  // Handle password change
  const handlePasswordChange = async () => {
    if (passwordData.new_password !== passwordData.confirm_password) {
      showError("Error", "New passwords do not match.");
      return;
    }

    if (passwordData.new_password.length < 8) {
      showError("Error", "Password must be at least 8 characters long.");
      return;
    }

    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      showSuccess("Password Changed", "Your password has been updated successfully.");
      setPasswordData({
        current_password: '',
        new_password: '',
        confirm_password: ''
      });
    } catch (error) {
      showError("Error", "Failed to change password. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const tabs = [
    { id: 'profile', label: 'Profile', icon: User },
    { id: 'notifications', label: 'Notifications', icon: Bell },
    { id: 'privacy', label: 'Privacy', icon: Shield },
    { id: 'preferences', label: 'Preferences', icon: Palette }
  ];

  if (loadingSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <CircularLoader />
      </div>
    );
  }

  if (!selectedSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">No School Selected</h2>
          <button
            onClick={() => router.push('/teacher-dashboard')}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
          >
            Select School
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={{
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        }}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-gray-500 rounded-full flex items-center justify-center">
                  <Settings className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">Account Settings</h1>
                  <p className="text-foreground/60">
                    Manage your account preferences and privacy settings
                  </p>
                </div>
              </div>

              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleSaveSettings}
                disabled={saving}
                className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50"
              >
                <Save size={16} />
                <span>{saving ? 'Saving...' : 'Save Changes'}</span>
              </motion.button>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar Tabs */}
            <div className="lg:col-span-1">
              <div className="bg-widget rounded-lg border border-stroke p-4">
                <nav className="space-y-2">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center space-x-3 px-3 py-2 rounded-md text-left transition-colors ${
                        activeTab === tab.id
                          ? 'bg-teal text-white'
                          : 'text-foreground hover:bg-gray-100 dark:hover:bg-gray-700'
                      }`}
                    >
                      <tab.icon className="h-5 w-5" />
                      <span>{tab.label}</span>
                    </button>
                  ))}
                </nav>
              </div>
            </div>

            {/* Content Area */}
            <div className="lg:col-span-3">
              <div className="bg-widget rounded-lg border border-stroke p-6">
                {/* Profile Tab */}
                {activeTab === 'profile' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-foreground">Profile Information</h3>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          First Name
                        </label>
                        <input
                          type="text"
                          value={settings.profile.first_name}
                          onChange={(e) => handleSettingsUpdate('profile', 'first_name', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Last Name
                        </label>
                        <input
                          type="text"
                          value={settings.profile.last_name}
                          onChange={(e) => handleSettingsUpdate('profile', 'last_name', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={settings.profile.email}
                        onChange={(e) => handleSettingsUpdate('profile', 'email', e.target.value)}
                        className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={settings.profile.phone}
                        onChange={(e) => handleSettingsUpdate('profile', 'phone', e.target.value)}
                        className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-foreground mb-2">
                        Bio
                      </label>
                      <textarea
                        value={settings.profile.bio}
                        onChange={(e) => handleSettingsUpdate('profile', 'bio', e.target.value)}
                        rows={4}
                        className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        placeholder="Tell us about yourself..."
                      />
                    </div>

                    {/* Password Change Section */}
                    <div className="border-t border-stroke pt-6">
                      <h4 className="text-md font-semibold text-foreground mb-4">Change Password</h4>

                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Current Password
                          </label>
                          <div className="relative">
                            <input
                              type={showPassword ? "text" : "password"}
                              value={passwordData.current_password}
                              onChange={(e) => setPasswordData(prev => ({...prev, current_password: e.target.value}))}
                              className="w-full px-3 py-2 pr-10 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                            />
                            <button
                              type="button"
                              onClick={() => setShowPassword(!showPassword)}
                              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-foreground/60"
                            >
                              {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                            </button>
                          </div>
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            New Password
                          </label>
                          <input
                            type={showPassword ? "text" : "password"}
                            value={passwordData.new_password}
                            onChange={(e) => setPasswordData(prev => ({...prev, new_password: e.target.value}))}
                            className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-foreground mb-2">
                            Confirm New Password
                          </label>
                          <input
                            type={showPassword ? "text" : "password"}
                            value={passwordData.confirm_password}
                            onChange={(e) => setPasswordData(prev => ({...prev, confirm_password: e.target.value}))}
                            className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                          />
                        </div>

                        <button
                          onClick={handlePasswordChange}
                          disabled={saving || !passwordData.current_password || !passwordData.new_password || !passwordData.confirm_password}
                          className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50"
                        >
                          {saving ? 'Changing...' : 'Change Password'}
                        </button>
                      </div>
                    </div>
                  </div>
                )}

                {/* Notifications Tab */}
                {activeTab === 'notifications' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-foreground">Notification Preferences</h3>

                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Email Announcements</h4>
                          <p className="text-sm text-foreground/60">Receive school announcements via email</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.notifications.email_announcements}
                            onChange={(e) => handleSettingsUpdate('notifications', 'email_announcements', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Grade Updates</h4>
                          <p className="text-sm text-foreground/60">Get notified when grades are updated</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.notifications.email_grades}
                            onChange={(e) => handleSettingsUpdate('notifications', 'email_grades', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Attendance Alerts</h4>
                          <p className="text-sm text-foreground/60">Receive attendance-related notifications</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.notifications.email_attendance}
                            onChange={(e) => handleSettingsUpdate('notifications', 'email_attendance', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Push Notifications</h4>
                          <p className="text-sm text-foreground/60">Receive push notifications on your device</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.notifications.push_notifications}
                            onChange={(e) => handleSettingsUpdate('notifications', 'push_notifications', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Privacy Tab */}
                {activeTab === 'privacy' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-foreground">Privacy Settings</h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Profile Visibility
                        </label>
                        <select
                          value={settings.privacy.profile_visibility}
                          onChange={(e) => handleSettingsUpdate('privacy', 'profile_visibility', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        >
                          <option value="public">Public - Visible to everyone</option>
                          <option value="school">School Only - Visible to school members</option>
                          <option value="private">Private - Only visible to you</option>
                        </select>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Show Email Address</h4>
                          <p className="text-sm text-foreground/60">Allow others to see your email address</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.privacy.show_email}
                            onChange={(e) => handleSettingsUpdate('privacy', 'show_email', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="font-medium text-foreground">Show Phone Number</h4>
                          <p className="text-sm text-foreground/60">Allow others to see your phone number</p>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.privacy.show_phone}
                            onChange={(e) => handleSettingsUpdate('privacy', 'show_phone', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-teal-300 dark:peer-focus:ring-teal-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-teal-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                )}

                {/* Preferences Tab */}
                {activeTab === 'preferences' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold text-foreground">Application Preferences</h3>

                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Theme
                        </label>
                        <select
                          value={settings.preferences.theme}
                          onChange={(e) => handleSettingsUpdate('preferences', 'theme', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        >
                          <option value="light">Light</option>
                          <option value="dark">Dark</option>
                          <option value="system">System Default</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Language
                        </label>
                        <select
                          value={settings.preferences.language}
                          onChange={(e) => handleSettingsUpdate('preferences', 'language', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        >
                          <option value="en">English</option>
                          <option value="fr">French</option>
                          <option value="es">Spanish</option>
                          <option value="de">German</option>
                        </select>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-foreground mb-2">
                          Timezone
                        </label>
                        <select
                          value={settings.preferences.timezone}
                          onChange={(e) => handleSettingsUpdate('preferences', 'timezone', e.target.value)}
                          className="w-full px-3 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal"
                        >
                          <option value="UTC">UTC</option>
                          <option value="America/New_York">Eastern Time</option>
                          <option value="America/Chicago">Central Time</option>
                          <option value="America/Denver">Mountain Time</option>
                          <option value="America/Los_Angeles">Pacific Time</option>
                          <option value="Europe/London">London</option>
                          <option value="Europe/Paris">Paris</option>
                          <option value="Asia/Tokyo">Tokyo</option>
                        </select>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </TeacherLayout>
    </ProtectedRoute>
  );
}