// --- TypeScript Interfaces for Grade Model ---

/**
 * Interface representing the full structure of a Grade document as stored in MongoDB.
 * Aligns with the Mongoose GradeSchema, including optionality and ObjectId references.
 */
export interface GradeSchema extends Record<string, unknown> {
  _id: string; // MongoDB ObjectId as string
  school_id?: string; // Optional MongoDB ObjectId as string (reference to School)
  subject_id: string; // Required (reference to Subject)
  student_id: string; // Required (reference to Student)
  exam_type?: string; // Made optional to align with Mongoose schema's `required: false`
  term_id: string; // Required reference to the Term model (ObjectId as string)

  // Legacy fields for backward compatibility, now optional
  term?: 'First Term' | 'Second Term' | 'Third Term' | string; // Dont use this because we have term_id
  academic_year?: string; // Optional legacy academic year field

  grade?: string; // Optional grade (e.g., "A", "B+", etc.)
  score: number; // Required numerical score
  comments?: string; // Optional teacher comments

  createdAt?: string; // Auto-generated timestamp
  updatedAt?: string; // Auto-generated timestamp
}

/**
 * Interface for creating a new Grade document.
 * Fields like _id, createdAt, updatedAt are omitted as they are auto-generated by MongoDB/Mongoose.
 */
export interface GradeCreateSchema extends Record<string, unknown> {
  subject_id: string; // Required
  student_id: string; // Required
  exam_type?: string; // Optional, as per Mongoose schema
  term_id: string; // Required, as per Mongoose schema

  // Legacy fields for backward compatibility, now optional for creation
  term?: string; // Optional legacy term field
  academic_year?: string; // Optional legacy academic year field

  score: number; // Required
  grade?: string; // Optional
  school_id?: string; // Optional
  comments?: string; // Optional
}

/**
 * Interface for updating an existing Grade document.
 * All fields are optional as updates may only modify specific attributes.
 * _id is required to identify the document to be updated.
 */
export interface GradeUpdateSchema extends Record<string, unknown> {
  _id: string; // Required to identify grade record for update
  subject_id?: string;
  student_id?: string;
  exam_type?: string;
  term_id?: string; // Optional, but will be a string (ObjectId) if provided

  // Legacy fields for backward compatibility, now optional for updates
  term?: string; // Optional legacy term field
  academic_year?: string; // Optional legacy academic year field

  score?: number;
  grade?: string;
  school_id?: string;
  comments?: string;
}

/**
 * Interface for deleting a Grade document.
 * Only the MongoDB ID is required for deletion.
 */
export interface GradeDeleteSchema extends Record<string, unknown> {
  _id: string; // Required MongoDB ID to delete
}
