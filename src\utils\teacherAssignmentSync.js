const ClassSchedule = require('../models/ClassSchedule');
const StaffPermission = require('../models/StaffPermission');
const Class = require('../models/Class');
const Subject = require('../models/Subject');

/**
 * Synchronize teacher assignments from ClassSchedule to StaffPermission
 * This ensures that both systems have consistent data
 */
async function syncTeacherAssignments(teacherId, schoolId) {
  try {
    // Get all schedule entries for this teacher in this school
    const scheduleEntries = await ClassSchedule.find({
      school_id: schoolId,
      teacher_id: teacherId
    })
    .populate('class_id', 'name class_level')
    .populate('subject_id', 'name')
    .populate('period_id', 'period_number start_time end_time');

    // Group by classes to build assigned_classes array
    const classMap = new Map();

    scheduleEntries.forEach(entry => {
      const classId = entry.class_id._id.toString();
      
      if (!classMap.has(classId)) {
        classMap.set(classId, {
          class_id: entry.class_id._id,
          subjects: [],
          periods: []
        });
      }

      const classData = classMap.get(classId);
      
      // Add subject if not already present
      if (!classData.subjects.includes(entry.subject_id.name)) {
        classData.subjects.push(entry.subject_id.name);
      }

      // Add period if not already present
      const periodInfo = `Period ${entry.period_id.period_number} (${entry.period_id.start_time.slice(0, 5)}-${entry.period_id.end_time.slice(0, 5)})`;
      if (!classData.periods.includes(periodInfo)) {
        classData.periods.push(periodInfo);
      }
    });

    // Convert map to array
    const assigned_classes = Array.from(classMap.values());

    // Find or create StaffPermission record
    let staffPermission = await StaffPermission.findOne({
      user_id: teacherId,
      school_id: schoolId
    });

    if (staffPermission) {
      // Update existing record
      staffPermission.assigned_classes = assigned_classes;
      staffPermission.last_modified_at = new Date();
      await staffPermission.save();
    } else {
      // Create new record with default teacher permissions
      const { ensureUniqueId } = require('./idGenerator');
      const permissionId = await ensureUniqueId(StaffPermission, 'permission_id', 'PRM');
      
      staffPermission = new StaffPermission({
        permission_id: permissionId,
        user_id: teacherId,
        school_id: schoolId,
        role_template: 'teacher',
        permissions: StaffPermission.getDefaultPermissions('teacher'),
        assigned_classes: assigned_classes,
        granted_by: teacherId // Self-assigned from schedule
      });
      
      await staffPermission.save();
    }

    return {
      success: true,
      assigned_classes: assigned_classes.length,
      message: `Synchronized ${assigned_classes.length} class assignments for teacher`
    };

  } catch (error) {
    console.error('Error syncing teacher assignments:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Get teacher assignments from ClassSchedule (primary source)
 * This is the main function that teacher dashboard should use
 */
async function getTeacherAssignmentsFromSchedule(teacherId, schoolId) {
  try {
    console.log(`🔍 Getting teacher assignments for teacher: ${teacherId}, school: ${schoolId}`);

    // Get all schedule entries for this teacher
    const scheduleEntries = await ClassSchedule.find({
      school_id: schoolId,
      teacher_id: teacherId
    })
    .populate('class_id', 'name class_level')
    .populate('subject_id', 'name')
    .populate('period_id', 'period_number start_time end_time');

    console.log(`📅 Found ${scheduleEntries.length} schedule entries for teacher`);
    scheduleEntries.forEach((entry, index) => {
      console.log(`  Entry ${index + 1}: ${entry.class_id?.name} - ${entry.subject_id?.name} - ${entry.day_of_week} - Period ${entry.period_id?.period_number}`);
    });

    // Group by classes to get unique classes and their subjects
    const classMap = new Map();
    const subjectMap = new Map();

    scheduleEntries.forEach(entry => {
      const classId = entry.class_id._id.toString();
      const subjectId = entry.subject_id._id.toString();

      // Add class to map
      if (!classMap.has(classId)) {
        classMap.set(classId, {
          _id: classId,
          name: entry.class_id.name,
          level: entry.class_id.class_level || 'Unknown'
        });
      }

      // Add subject to map with class association
      // Use a unique key combining subject and class to handle same subject in multiple classes
      const subjectClassKey = `${subjectId}_${classId}`;
      if (!subjectMap.has(subjectClassKey)) {
        subjectMap.set(subjectClassKey, {
          _id: subjectId,
          name: entry.subject_id.name,
          class_id: classId,
          class_name: entry.class_id.name
        });
      }
    });

    // Convert maps to arrays
    const assigned_classes = Array.from(classMap.values());
    const assigned_subjects = Array.from(subjectMap.values());

    console.log(`📚 Processed assignments:`);
    console.log(`  Classes (${assigned_classes.length}):`, assigned_classes.map(c => `${c.name} (${c._id})`));
    console.log(`  Subjects (${assigned_subjects.length}):`, assigned_subjects.map(s => `${s.name} in ${s.class_name} (${s.class_id})`));

    // Get permissions from StaffPermission
    let staffPermission = await StaffPermission.findOne({
      user_id: teacherId,
      school_id: schoolId
    });

    let permissions;
    if (staffPermission) {
      permissions = staffPermission.permissions;
    } else {
      // Create default teacher permissions if not found
      permissions = StaffPermission.getDefaultPermissions('teacher');
      
      // Optionally sync the assignments
      await syncTeacherAssignments(teacherId, schoolId);
    }

    return {
      _id: teacherId,
      teacher_id: teacherId,
      school_id: schoolId,
      assigned_classes,
      assigned_subjects,
      permissions,
      role_template: staffPermission?.role_template || 'teacher'
    };

  } catch (error) {
    console.error('Error getting teacher assignments from schedule:', error);
    throw error;
  }
}

/**
 * Get students in teacher's assigned classes from ClassSchedule
 */
async function getTeacherStudentsFromSchedule(teacherId, schoolId) {
  try {
    const Student = require('../models/Student');
    
    // Get teacher's assigned classes from ClassSchedule
    const scheduleEntries = await ClassSchedule.find({
      school_id: schoolId,
      teacher_id: teacherId
    }).populate('class_id', 'name');

    // Get unique class IDs
    const assignedClassIds = [...new Set(scheduleEntries.map(entry => entry.class_id._id.toString()))];

    if (assignedClassIds.length === 0) {
      return [];
    }

    // Find students in teacher's assigned classes
    const students = await Student.find({
      school_id: schoolId,
      class_id: { $in: assignedClassIds }
    }).populate('class_id', 'name');

    return students.map(student => ({
      _id: student._id,
      first_name: student.first_name,
      last_name: student.last_name,
      email: student.email,
      class_id: student.class_id._id,
      class_name: student.class_id.name
    }));

  } catch (error) {
    console.error('Error getting teacher students from schedule:', error);
    throw error;
  }
}

module.exports = {
  syncTeacherAssignments,
  getTeacherAssignmentsFromSchedule,
  getTeacherStudentsFromSchedule
};
