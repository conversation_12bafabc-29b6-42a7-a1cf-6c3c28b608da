"use client";

import React from 'react';
import { motion } from 'framer-motion';
import {
  SkeletonCard,
  SkeletonLine,
  SkeletonCircle,
  SkeletonButton,
  SkeletonBadge,
  SkeletonIcon,
  SkeletonHeader,
  SkeletonStatsGrid
} from './SkeletonCard';

interface TeacherResourcesSkeletonProps {
  itemCount?: number;
}

const TeacherResourcesSkeleton: React.FC<TeacherResourcesSkeletonProps> = ({
  itemCount = 6
}) => {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <SkeletonCard>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <SkeletonCircle size="w-12 h-12" />
            <div>
              <SkeletonLine width="w-48" height="h-8" className="mb-2" />
              <SkeletonLine width="w-64" height="h-4" />
            </div>
          </div>
          <SkeletonButton width="w-32" />
        </div>
      </SkeletonCard>

      {/* Stats Cards Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <SkeletonCard>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <SkeletonLine width="w-16" height="h-4" className="mb-2" />
                  <SkeletonLine width="w-12" height="h-8" className="mb-1" />
                  <SkeletonLine width="w-20" height="h-3" />
                </div>
                <SkeletonCircle size="w-10 h-10" />
              </div>
            </SkeletonCard>
          </motion.div>
        ))}
      </div>

      {/* Filters Skeleton */}
      <SkeletonCard>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <SkeletonLine width="w-64" height="h-10" />
            </div>
            <SkeletonButton width="w-32" />
            <SkeletonButton width="w-28" />
          </div>
          <div className="flex space-x-2">
            <SkeletonButton width="w-24" />
            <SkeletonButton width="w-20" />
          </div>
        </div>
      </SkeletonCard>

      {/* Resources Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {Array.from({ length: itemCount }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 + index * 0.1 }}
          >
            <SkeletonCard>
              {/* File Icon Skeleton */}
              <div className="flex items-center justify-center h-16 mb-4">
                <SkeletonCircle size="w-12 h-12" />
              </div>

              {/* Resource Info Skeleton */}
              <div className="space-y-3">
                <SkeletonLine width="w-full" height="h-5" />
                <SkeletonLine width="w-3/4" height="h-4" />
                
                {/* Badges Skeleton */}
                <div className="flex flex-wrap gap-2">
                  <SkeletonBadge />
                  <SkeletonBadge />
                </div>

                {/* Meta Info Skeleton */}
                <div className="flex items-center justify-between text-sm pt-3 border-t border-stroke">
                  <div className="flex items-center space-x-2">
                    <SkeletonIcon size="w-4 h-4" />
                    <SkeletonLine width="w-16" height="h-3" />
                  </div>
                  <SkeletonLine width="w-12" height="h-3" />
                </div>

                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center space-x-2">
                    <SkeletonIcon size="w-4 h-4" />
                    <SkeletonLine width="w-20" height="h-3" />
                  </div>
                  <div className="flex items-center space-x-2">
                    <SkeletonIcon size="w-4 h-4" />
                    <SkeletonLine width="w-8" height="h-3" />
                  </div>
                </div>
              </div>

              {/* Action Buttons Skeleton */}
              <div className="flex space-x-2 mt-4 pt-4 border-t border-stroke">
                <SkeletonButton className="flex-1" />
                <SkeletonButton width="w-10" height="h-8" />
                <SkeletonButton width="w-10" height="h-8" />
              </div>
            </SkeletonCard>
          </motion.div>
        ))}
      </div>

      {/* Pagination Skeleton */}
      <div className="flex items-center justify-between">
        <SkeletonLine width="w-32" height="h-4" />
        <div className="flex space-x-2">
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
          <SkeletonButton width="w-8" height="h-8" />
        </div>
      </div>
    </div>
  );
};

export default TeacherResourcesSkeleton;
