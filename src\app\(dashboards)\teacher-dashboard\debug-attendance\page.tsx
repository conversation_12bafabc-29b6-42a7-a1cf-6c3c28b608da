"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import AttendanceAPITester from "@/components/debug/AttendanceAPITester";
import { getTeacherNavigationItems, debugTeacherAssignments } from "@/app/services/TeacherPermissionServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

export default function DebugAttendancePage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [navigation, setNavigation] = useState<any[]>([]);
  const [debugResult, setDebugResult] = useState<any>(null);
  const [isDebugging, setIsDebugging] = useState(false);

  useEffect(() => {
    if (user) {
      // Get selected school from localStorage
      const storedSchool = localStorage.getItem("teacher_selected_school");
      if (storedSchool) {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
      } else {
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const handleDebugAssignments = async () => {
    if (!selectedSchool) {
      alert("No school selected");
      return;
    }

    setIsDebugging(true);
    setDebugResult(null);

    try {
      console.log("🔍 Starting debug for school:", selectedSchool.school_id);
      const result = await debugTeacherAssignments(selectedSchool.school_id);
      setDebugResult(result);
      console.log("🔍 Debug completed:", result);
    } catch (error) {
      console.error("❌ Debug failed:", error);
      alert(`Debug failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsDebugging(false);
    }
  };

  // Set basic navigation for debug page
  useEffect(() => {
    const debugNavigation = [
      {
        title: "Debug Tools",
        icon: "Bug",
        items: [
          { icon: "TestTube", name: "API Tester", href: "/teacher-dashboard/debug-attendance" },
          { icon: "ClipboardList", name: "Back to Attendance", href: "/teacher-dashboard/attendance" },
          { icon: "Home", name: "Dashboard", href: "/teacher-dashboard/dashboard" }
        ]
      }
    ];
    setNavigation(debugNavigation);
  }, []);

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Debug Attendance</h1>
              <p className="text-foreground/60">
                Test and debug attendance APIs for troubleshooting
              </p>
            </div>
            <button
              onClick={() => router.push("/teacher-dashboard/attendance")}
              className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
            >
              Back to Attendance
            </button>
          </div>

          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3 flex-1">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Debug Mode
                </h3>
                <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>
                    This page is for debugging attendance issues. Use the API tester below to check if the backend APIs are working correctly.
                    {selectedSchool && (
                      <span className="block mt-1">
                        <strong>Current School:</strong> {selectedSchool.school_name} ({selectedSchool.school_id})
                      </span>
                    )}
                  </p>
                </div>
                <div className="mt-3">
                  <button
                    onClick={handleDebugAssignments}
                    disabled={isDebugging || !selectedSchool}
                    className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {isDebugging ? (
                      <>
                        <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                        </svg>
                        <span>Debugging...</span>
                      </>
                    ) : (
                      <span>🔍 Debug Teacher Assignments</span>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>

          <AttendanceAPITester />

          {/* Debug Results */}
          {debugResult && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
              <h2 className="text-lg font-semibold text-foreground mb-4">🔍 Teacher Assignments Debug Results</h2>

              <div className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-800 dark:text-blue-200">Raw Schedule</h3>
                    <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                      {debugResult.debug_info?.raw_schedule_count || 0}
                    </p>
                    <p className="text-sm text-blue-600 dark:text-blue-400">entries found</p>
                  </div>

                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-green-800 dark:text-green-200">Assigned Classes</h3>
                    <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {debugResult.debug_info?.processed_assignments?.assigned_classes?.length || 0}
                    </p>
                    <p className="text-sm text-green-600 dark:text-green-400">classes</p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-purple-800 dark:text-purple-200">Assigned Subjects</h3>
                    <p className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                      {debugResult.debug_info?.processed_assignments?.assigned_subjects?.length || 0}
                    </p>
                    <p className="text-sm text-purple-600 dark:text-purple-400">subject-class combinations</p>
                  </div>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h3 className="font-medium text-foreground mb-2">📚 Assigned Classes</h3>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 max-h-40 overflow-y-auto">
                      {debugResult.debug_info?.processed_assignments?.assigned_classes?.map((cls: any, index: number) => (
                        <div key={index} className="text-sm text-foreground/80 mb-1">
                          {cls.name} ({cls._id})
                        </div>
                      )) || <div className="text-sm text-foreground/60">No classes found</div>}
                    </div>
                  </div>

                  <div>
                    <h3 className="font-medium text-foreground mb-2">📖 Assigned Subjects</h3>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded p-3 max-h-40 overflow-y-auto">
                      {debugResult.debug_info?.processed_assignments?.assigned_subjects?.map((subject: any, index: number) => (
                        <div key={index} className="text-sm text-foreground/80 mb-1">
                          {subject.name} in {subject.class_name}
                        </div>
                      )) || <div className="text-sm text-foreground/60">No subjects found</div>}
                    </div>
                  </div>
                </div>

                <details className="cursor-pointer">
                  <summary className="font-medium text-foreground mb-2">🔍 Raw Debug Data</summary>
                  <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-x-auto">
                    {JSON.stringify(debugResult, null, 2)}
                  </pre>
                </details>
              </div>
            </div>
          )}

          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
              Debugging Tips
            </h3>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>• Check the browser console for detailed error messages</li>
              <li>• Verify that the teacher has classes assigned in the school</li>
              <li>• Ensure students are enrolled in the teacher's classes</li>
              <li>• Check if attendance records exist for the class</li>
              <li>• Verify that the teacher has proper permissions</li>
              <li>• Test each API endpoint individually to isolate issues</li>
            </ul>
          </div>
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
