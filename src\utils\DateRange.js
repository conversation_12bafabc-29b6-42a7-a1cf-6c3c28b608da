// utils/dateUtils.js

/**
 * Returns the start and end dates for a given month of a year.
 * @param {number} year - The full year (e.g., 2025).
 * @param {number} month - The month (0-based, January = 0).
 * @returns {{start: Date, end: Date}} - Start (inclusive) and end (exclusive) Date objects.
 */
function getMonthDateRange(year, month) {
  const start = new Date(year, month, 1);
  const end = new Date(year, month + 1, 1);
  return { start, end };
}

module.exports = { getMonthDateRange };
