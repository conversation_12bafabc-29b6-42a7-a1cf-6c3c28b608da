const mongoose = require('mongoose');
const { ensureUniqueId } = require('../utils/generateId');

const activityLogSchema = new mongoose.Schema({
  log_id: {
    type: String,
    required: true,
    unique: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: false // Some activities might not be school-specific
  },
  action: {
    type: String,
    required: true,
    enum: [
      // Staff management
      'staff_created',
      'staff_updated',
      'staff_deleted',
      'staff_password_reset',
      'staff_access_code_generated',
      
      // Student management
      'student_created',
      'student_updated',
      'student_deleted',
      
      // Academic records
      'grade_entered',
      'grade_updated',
      'attendance_taken',
      'attendance_updated',
      
      // Announcements
      'announcement_created',
      'announcement_updated',
      'announcement_deleted',
      'announcement_published',
      
      // Authentication
      'user_login',
      'user_logout',
      'password_changed',
      
      // System
      'firebase_sync',
      'data_export',
      'backup_created',
      
      // Classes
      'class_created',
      'class_updated',
      'class_deleted',
      'class_assignment_changed',
      
      // Resources
      'resource_uploaded',
      'resource_updated',
      'resource_deleted',
      
      // Financial
      'fee_payment_recorded',
      'financial_report_generated',

      // Attendance
      'attendance_marked',
      'attendance_updated',
      'attendance_deleted',
      'attendance_report_generated',

      //terms
      'term_created',
      'term_updated',
      'term_deleted',
      'term_report_generated',
      'term_set_current',

      // Schedule/Timetable
      'schedule_created',
      'schedule_updated',
      'schedule_deleted',

      // Exams
      'exam_created',
      'exam_updated',
      'exam_deleted',
      'exam_supervisor_assigned'
    ]
  },
  target_type: {
    type: String,
    required: false,
    enum: ['user', 'student', 'class', 'announcement', 'resource', 'payment', 'report', 'attendance', 'term', 'schedule']
  },
  target_id: {
    type: String,
    required: false // ID of the target object (staff_id, student_id, etc.)
  },
  target_name: {
    type: String,
    required: false // Human-readable name of the target
  },
  details: {
    type: mongoose.Schema.Types.Mixed,
    required: false // Additional details about the action
  },
  ip_address: {
    type: String,
    required: false
  },
  user_agent: {
    type: String,
    required: false
  },
  status: {
    type: String,
    required: true,
    enum: ['success', 'failed', 'pending'],
    default: 'success'
  },
  error_message: {
    type: String,
    required: false
  },
  metadata: {
    type: mongoose.Schema.Types.Mixed,
    required: false // Any additional metadata
  }
}, {
  timestamps: true // This adds createdAt and updatedAt
});

// Indexes for better query performance
activityLogSchema.index({ user_id: 1, createdAt: -1 });
activityLogSchema.index({ school_id: 1, createdAt: -1 });
activityLogSchema.index({ action: 1, createdAt: -1 });
activityLogSchema.index({ target_type: 1, target_id: 1 });
activityLogSchema.index({ createdAt: -1 }); // For general sorting

// Virtual for formatted timestamp
activityLogSchema.virtual('formatted_timestamp').get(function() {
  return this.createdAt.toLocaleString();
});

// Method to get human-readable action description
activityLogSchema.methods.getActionDescription = function() {
  const descriptions = {
    // Staff management
    staff_created: `Created staff member ${this.target_name}`,
    staff_updated: `Updated staff member ${this.target_name}`,
    staff_deleted: `Removed staff member ${this.target_name}`,
    staff_password_reset: `Reset password for ${this.target_name}`,
    staff_access_code_generated: `Generated access code for teacher ${this.target_name}`,
    
    // Student management
    student_created: `Added student ${this.target_name}`,
    student_updated: `Updated student ${this.target_name}`,
    student_deleted: `Removed student ${this.target_name}`,
    
    // Academic records
    grade_entered: `Entered grades for ${this.target_name}`,
    grade_updated: `Updated grades for ${this.target_name}`,
    attendance_taken: `Took attendance for ${this.target_name}`,
    attendance_updated: `Updated attendance for ${this.target_name}`,
    
    // Announcements
    announcement_created: `Created announcement: ${this.target_name}`,
    announcement_updated: `Updated announcement: ${this.target_name}`,
    announcement_deleted: `Deleted announcement: ${this.target_name}`,
    announcement_published: `Published announcement: ${this.target_name}`,
    
    // Authentication
    user_login: 'Logged in',
    user_logout: 'Logged out',
    password_changed: 'Changed password',
    
    // System
    firebase_sync: 'Synchronized data with Firebase',
    data_export: 'Exported data',
    backup_created: 'Created system backup',
    
    // Classes
    class_created: `Created class ${this.target_name}`,
    class_updated: `Updated class ${this.target_name}`,
    class_deleted: `Deleted class ${this.target_name}`,
    class_assignment_changed: `Changed class assignments for ${this.target_name}`,
    
    // Resources
    resource_uploaded: `Uploaded resource: ${this.target_name}`,
    resource_updated: `Updated resource: ${this.target_name}`,
    resource_deleted: `Deleted resource: ${this.target_name}`,
    
    // Financial
    fee_payment_recorded: `Recorded fee payment for ${this.target_name}`,
    financial_report_generated: `Generated financial report`,

    // Attendance
    attendance_marked: `Marked attendance for ${this.target_name}`,
    attendance_updated: `Updated attendance for ${this.target_name}`,
    attendance_deleted: `Deleted attendance for ${this.target_name}`,
    attendance_report_generated: `Generated attendance report`,

    // Terms
    term_created: `Created term ${this.target_name}`,
    term_updated: `Updated term ${this.target_name}`,
    term_deleted: `Deleted term ${this.target_name}`,
    term_report_generated: `Generated term report`,
    term_set_current: `Set term ${this.target_name} as current`,

    // Schedule/Timetable
    schedule_created: `Created schedule entry for ${this.target_name}`,
    schedule_updated: `Updated schedule entry for ${this.target_name}`,
    schedule_deleted: `Deleted schedule entry for ${this.target_name}`,

    // Exams
    exam_created: `Created exam schedule for ${this.target_name}`,
    exam_updated: `Updated exam schedule for ${this.target_name}`,
    exam_deleted: `Deleted exam schedule for ${this.target_name}`,
    exam_supervisor_assigned: `Assigned exam supervisor for ${this.target_name}`
  };

  return descriptions[this.action] || `Performed action: ${this.action}`;
};

// Static method to log activity
activityLogSchema.statics.logActivity = async function(data) {
  
  try {
    const logId = await ensureUniqueId('LOG', this, 'log_id');
    
    const logEntry = new this({
      log_id: logId,
      ...data
    });
    
    await logEntry.save();
    return logEntry;
  } catch (error) {
    console.error('Error logging activity:', error);
    // Don't throw error to prevent breaking the main operation
    return null;
  }
};

// Static method to get recent activities
activityLogSchema.statics.getRecentActivities = async function(filters = {}, limit = 50) {
  const query = {};
  
  if (filters.user_id) query.user_id = filters.user_id;
  if (filters.school_id) query.school_id = filters.school_id;
  if (filters.action) query.action = filters.action;
  if (filters.target_type) query.target_type = filters.target_type;
  
  return this.find(query)
    .populate('user_id', 'first_name last_name email role')
    .populate('school_id', 'name')
    .sort({ createdAt: -1 })
    .limit(limit);
};

module.exports = mongoose.model('ActivityLog', activityLogSchema);
