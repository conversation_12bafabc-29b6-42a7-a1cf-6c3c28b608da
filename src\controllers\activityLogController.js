const ActivityLog = require('../models/ActivityLog');

// Get recent activities with filters
const getRecentActivities = async (req, res) => {
  try {
    const { user_id, school_id, action, target_type, limit = 50 } = req.query;
    
    // Build filter object
    const filters = {};
    
    if (user_id) filters.user_id = user_id;
    if (school_id) filters.school_id = school_id;
    if (action) filters.action = action;
    if (target_type) filters.target_type = target_type;
    
    // For teachers, they can only see their own activities unless they're viewing school-wide data
    if (req.user.role === 'teacher' && !school_id) {
      filters.user_id = req.user.id;
    }
    
    // For school admins, limit to their school
    if (req.user.role === 'school_admin' && req.user.school_id) {
      filters.school_id = req.user.school_id;
    }
    
    console.log('🔍 Fetching activities with filters:', filters);
    
    const activities = await ActivityLog.getRecentActivities(filters, parseInt(limit));
    
    console.log('✅ Found activities:', activities.length);
    
    res.status(200).json({
      activities,
      count: activities.length,
      filters: filters,
      message: 'Recent activities retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching recent activities:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// Get activities for a specific user
const getUserActivities = async (req, res) => {
  try {
    const { user_id } = req.params;
    const { school_id, limit = 50 } = req.query;
    
    const filters = { user_id };
    if (school_id) filters.school_id = school_id;
    
    // Authorization check
    if (req.user.role === 'teacher' && req.user.id !== user_id) {
      return res.status(403).json({ message: 'Access denied. Teachers can only view their own activities.' });
    }
    
    if (req.user.role === 'school_admin' && req.user.school_id && school_id !== req.user.school_id) {
      return res.status(403).json({ message: 'Access denied. School admins can only view activities from their school.' });
    }
    
    const activities = await ActivityLog.getRecentActivities(filters, parseInt(limit));
    
    res.status(200).json({
      activities,
      count: activities.length,
      user_id,
      message: 'User activities retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching user activities:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

// Get activities for a specific school
const getSchoolActivities = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { user_id, action, target_type, limit = 100 } = req.query;
    
    const filters = { school_id };
    if (user_id) filters.user_id = user_id;
    if (action) filters.action = action;
    if (target_type) filters.target_type = target_type;
    
    // Authorization check for school admins
    if (req.user.role === 'school_admin' && req.user.school_id !== school_id) {
      return res.status(403).json({ message: 'Access denied. You can only view activities from your school.' });
    }
    
    const activities = await ActivityLog.getRecentActivities(filters, parseInt(limit));
    
    res.status(200).json({
      activities,
      count: activities.length,
      school_id,
      message: 'School activities retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching school activities:', error);
    res.status(500).json({ 
      message: 'Internal server error',
      error: error.message 
    });
  }
};

module.exports = {
  getRecentActivities,
  getUserActivities,
  getSchoolActivities
};
