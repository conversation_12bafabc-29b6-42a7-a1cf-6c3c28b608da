'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  ExternalLink, 
  Plus, 
  Eye, 
  Edit, 
  Trash2, 
  Settings, 
  Users, 
  School,
  GraduationCap,
  BarChart3,
  FileText,
  Calendar,
  MessageSquare
} from 'lucide-react';
import { ChatbotAction } from '@/services/ChatbotService';

interface ActionButtonsProps {
  actions: ChatbotAction[];
  onActionClick: (action: ChatbotAction) => void;
}

const ActionButtons: React.FC<ActionButtonsProps> = ({ actions, onActionClick }) => {
  const getActionIcon = (type: string, target?: string) => {
    // First check by action type
    switch (type) {
      case 'navigate': 
        // Then check by target for navigation
        if (target?.includes('school')) return School;
        if (target?.includes('user')) return Users;
        if (target?.includes('student')) return GraduationCap;
        if (target?.includes('dashboard')) return BarChart3;
        if (target?.includes('report')) return FileText;
        if (target?.includes('schedule')) return Calendar;
        return ExternalLink;
      case 'open_modal': 
        if (target?.includes('Create')) return Plus;
        if (target?.includes('Edit')) return Edit;
        if (target?.includes('Delete')) return Trash2;
        if (target?.includes('Settings')) return Settings;
        return Plus;
      case 'show_data': return Eye;
      case 'execute_function': return Edit;
      default: return Plus;
    }
  };

  const getActionColor = (type: string, target?: string) => {
    switch (type) {
      case 'navigate': 
        if (target?.includes('dashboard')) return 'bg-blue-500 hover:bg-blue-600';
        if (target?.includes('school')) return 'bg-indigo-500 hover:bg-indigo-600';
        if (target?.includes('user')) return 'bg-purple-500 hover:bg-purple-600';
        return 'bg-blue-500 hover:bg-blue-600';
      case 'open_modal': 
        if (target?.includes('Create')) return 'bg-green-500 hover:bg-green-600';
        if (target?.includes('Edit')) return 'bg-yellow-500 hover:bg-yellow-600';
        if (target?.includes('Delete')) return 'bg-red-500 hover:bg-red-600';
        return 'bg-green-500 hover:bg-green-600';
      case 'show_data': return 'bg-purple-500 hover:bg-purple-600';
      case 'execute_function': return 'bg-orange-500 hover:bg-orange-600';
      default: return 'bg-teal-500 hover:bg-teal-600';
    }
  };

  const getActionLabel = (action: ChatbotAction) => {
    if (action.label) return action.label;
    
    // Generate label from target if no label provided
    const target = action.target;
    if (target.includes('Modal')) {
      return target.replace('Modal', '').replace(/([A-Z])/g, ' $1').trim();
    }
    if (target.startsWith('/')) {
      return target.split('/').pop()?.replace(/-/g, ' ') || 'Go';
    }
    return target;
  };

  if (!actions || actions.length === 0) return null;

  return (
    <div className="mt-3 flex flex-wrap gap-2">
      {actions.map((action, index) => {
        const Icon = getActionIcon(action.type, action.target);
        const colorClass = getActionColor(action.type, action.target);
        const label = getActionLabel(action);
        
        return (
          <motion.button
            key={index}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => onActionClick(action)}
            className={`flex items-center space-x-2 px-3 py-2 rounded-lg text-white text-sm font-medium transition-colors shadow-sm ${colorClass}`}
            title={`${action.type}: ${action.target}`}
          >
            <Icon size={14} />
            <span className="capitalize">{label}</span>
          </motion.button>
        );
      })}
    </div>
  );
};

export default ActionButtons;
