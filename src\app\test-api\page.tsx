"use client";

import { useState } from "react";
import { BASE_API_URL } from "@/app/services/AuthContext";
import { getTokenFromCookie } from "@/app/services/UserServices";
import { getAcademicYears } from "@/app/services/AcademicYearServices";

export default function TestApiPage() {
  const [result, setResult] = useState<string>("");
  const [loading, setLoading] = useState(false);

  const testBasicConnection = async () => {
    setLoading(true);
    setResult("Testing basic connection...");

    try {
      const response = await fetch(`${BASE_API_URL}/health`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        setResult("✅ API is reachable! Status: " + response.status);
      } else {
        setResult("❌ API responded with error: " + response.status);
      }
    } catch (error) {
      setResult("❌ Cannot reach API: " + error);
    } finally {
      setLoading(false);
    }
  };

  const testAcademicYears = async () => {
    setLoading(true);
    setResult("Testing academic years endpoint...");

    try {
      const years = await getAcademicYears();
      setResult("✅ Academic years loaded: " + JSON.stringify(years, null, 2));
    } catch (error) {
      setResult("❌ Academic years failed: " + error);
    } finally {
      setLoading(false);
    }
  };

  const testAuth = () => {
    const token = getTokenFromCookie("idToken");
    if (token) {
      setResult("✅ Authentication token found: " + token.substring(0, 50) + "...");
    } else {
      setResult("❌ No authentication token found");
    }
  };

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">API Test Page</h1>
      
      <div className="mb-6">
        <p className="text-lg mb-4">
          <strong>API URL:</strong> {BASE_API_URL}
        </p>
      </div>

      <div className="space-y-4">
        <button
          onClick={testBasicConnection}
          disabled={loading}
          className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600 disabled:bg-gray-400"
        >
          {loading ? "Testing..." : "Test Basic Connection"}
        </button>

        <button
          onClick={testAuth}
          disabled={loading}
          className="bg-green-500 text-white px-4 py-2 rounded hover:bg-green-600 disabled:bg-gray-400 ml-2"
        >
          Check Authentication
        </button>

        <button
          onClick={testAcademicYears}
          disabled={loading}
          className="bg-purple-500 text-white px-4 py-2 rounded hover:bg-purple-600 disabled:bg-gray-400 ml-2"
        >
          Test Academic Years
        </button>
      </div>

      {result && (
        <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-800 rounded-lg">
          <pre className="whitespace-pre-wrap text-sm">{result}</pre>
        </div>
      )}
    </div>
  );
}