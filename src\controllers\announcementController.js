// controllers/announcementController.js

const Announcement = require('../models/Announcement'); // Assuming you have an Announcement model
const { ensureUniqueId } = require('../utils/generateId');
const NotificationService = require('../services/notificationService');
const User = require('../models/User');
const School = require('../models/School');

const testAnnouncementResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is announcement' });
};

// // Get all announcements
const getAllAnnouncements = async (req, res) => {
  try {
    const announcements = await Announcement.find();
    res.json(announcements);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Create a new announcement
const createAnnouncement = async (req, res) => {
  try {
    const announcementId = await ensureUniqueId(Announcement, 'announcement_id', 'ANC');

    // Extract required fields from request body
    const {
      title,
      content,
      school_id,
      author_id,
      target_audience = 'all',
      priority = 'medium',
      is_published = false,
      expires_at,
      announcement // Keep for backward compatibility
    } = req.body;

    // Validate required fields
    if (!title || !content || !school_id || !author_id) {
      return res.status(400).json({
        message: 'Missing required fields: title, content, school_id, and author_id are required'
      });
    }

    // Create the new announcement object
    const newAnnouncement = new Announcement({
      announcement_id: announcementId,
      title,
      content,
      school_id,
      author_id,
      target_audience,
      priority,
      is_published,
      expires_at: expires_at ? new Date(expires_at) : undefined,
      published_at: is_published ? new Date() : undefined,
      announcement // Keep for backward compatibility
    });

    await newAnnouncement.save();

    // Populate the response with author and school info
    const populatedAnnouncement = await Announcement.findById(newAnnouncement._id)
      .populate('author_id', 'first_name last_name email')
      .populate('school_id', 'name');

    // 🔔 SEND NOTIFICATIONS FOR ANNOUNCEMENT CREATION
    try {
      await sendAnnouncementNotifications(populatedAnnouncement, 'created', author_id);
    } catch (notificationError) {
      console.error('Error sending announcement creation notifications:', notificationError);
      // Don't fail the request if notifications fail
    }

    res.status(201).json(populatedAnnouncement);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Get an announcement by ID
const getAnnouncementById = async (req, res) => {
  try {
    const announcement = await Announcement.findOne({announcement_id:req.params.id});
    if (!announcement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }
    res.json(announcement);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update announcement by ID
const updateAnnouncementById = async (req, res) => {
  try {
    const {
      title,
      content,
      target_audience,
      priority,
      is_published,
      expires_at,
      announcement // Keep for backward compatibility
    } = req.body;

    // Prepare update data
    const updateData = {};
    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (target_audience !== undefined) updateData.target_audience = target_audience;
    if (priority !== undefined) updateData.priority = priority;
    if (is_published !== undefined) {
      updateData.is_published = is_published;
      // Set published_at when publishing for the first time
      if (is_published) {
        updateData.published_at = new Date();
      }
    }
    if (expires_at !== undefined) updateData.expires_at = expires_at ? new Date(expires_at) : null;
    if (announcement !== undefined) updateData.announcement = announcement; // Backward compatibility

    const updatedAnnouncement = await Announcement.findOneAndUpdate(
      { announcement_id: req.params.id },
      updateData,
      { new: true }
    ).populate('author_id', 'first_name last_name email')
     .populate('school_id', 'name');

    if (!updatedAnnouncement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }

    // 🔔 SEND NOTIFICATIONS FOR ANNOUNCEMENT UPDATE
    try {
      await sendAnnouncementNotifications(updatedAnnouncement, 'updated', updatedAnnouncement.author_id._id);
    } catch (notificationError) {
      console.error('Error sending announcement update notifications:', notificationError);
      // Don't fail the request if notifications fail
    }

    res.json(updatedAnnouncement);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete announcement by ID
const deleteAnnouncementById = async (req, res) => {
  try {
    const deletedAnnouncement = await Announcement.findOneAndDelete({announcement_id:req.params.id});
    if (!deletedAnnouncement) {
      return res.status(404).json({ message: 'Announcement not found' });
    }
    res.json({ message: 'Announcement deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};
// // Delete multiple announcements by IDs
const deleteMultipleAnnouncements = async (req, res) => {
  const { ids } = req.body; // Expecting an array of announcement_ids in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete announcements where announcement_id is in the provided array of IDs
    const result = await Announcement.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No announcements found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} announcements deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL announcement records
const deleteAllAnnouncements = async (req, res) => {
  try {
    // First, count how many announcements exist
    const announcementCount = await Announcement.countDocuments();

    if (announcementCount === 0) {
      return res.status(404).json({ message: 'No announcements found to delete' });
    }

    // Delete all announcement records
    const result = await Announcement.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} announcement records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get announcements by school ID
const getAnnouncementsBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Fetch announcements for the specific school
    const announcements = await Announcement.find({ school_id })
      .populate('author_id', 'first_name last_name email')
      .populate('school_id', 'name')
      .sort({ createdAt: -1 }); // Sort by creation date, newest first

    res.status(200).json(announcements);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// 🔔 NOTIFICATION HELPER FUNCTION FOR ANNOUNCEMENT CREATION/UPDATE
const sendAnnouncementNotifications = async (announcement, action, authorId) => {
  try {
    const school = await School.findById(announcement.school_id);
    const author = await User.findById(authorId);

    if (!school) {
      console.error('School not found for announcement notifications');
      return;
    }

    // Only send notifications for published announcements
    if (!announcement.is_published) {
      console.log('Announcement is not published, skipping notifications');
      return;
    }

    const actionText = action === 'created' ? 'published' : 'updated';
    const actionEmoji = action === 'created' ? '📢' : '✏️';

    // Determine recipients based on target_audience
    let recipients = [];
    let notificationTitle = '';
    let notificationMessage = '';

    switch (announcement.target_audience) {
      case 'teachers':
        // Find all teachers in the school
        recipients = await User.find({
          school_ids: announcement.school_id,
          role: 'teacher',
          is_staff_active: true
        });
        notificationTitle = `${actionEmoji} New Announcement for Teachers`;
        notificationMessage = `${announcement.title} - ${author ? author.first_name + ' ' + author.last_name : 'School Admin'} has ${actionText} an announcement for teachers.`;
        break;

      case 'parents':
        // Find all parents in the school (through students)
        const students = await User.find({
          school_ids: announcement.school_id,
          role: 'student'
        }).populate('parent_id');

        recipients = students
          .filter(student => student.parent_id)
          .map(student => student.parent_id)
          .filter((parent, index, self) =>
            parent && self.findIndex(p => p._id.toString() === parent._id.toString()) === index
          );

        notificationTitle = `${actionEmoji} School Announcement`;
        notificationMessage = `${announcement.title} - ${school.name} has ${actionText} an important announcement for parents.`;
        break;

      case 'students':
        // Find all students in the school
        recipients = await User.find({
          school_ids: announcement.school_id,
          role: 'student'
        });
        notificationTitle = `${actionEmoji} Student Announcement`;
        notificationMessage = `${announcement.title} - ${school.name} has ${actionText} an announcement for students.`;
        break;

      case 'all':
      default:
        // Find all users in the school (except super admins)
        const allUsers = await User.find({
          school_ids: announcement.school_id,
          role: { $ne: 'super' },
          $or: [
            { is_staff_active: true },
            { role: { $in: ['student', 'parent', 'teacher', 'admin'] } }
          ]
        });

        // Also get parents through students
        const schoolStudents = await User.find({
          school_ids: announcement.school_id,
          role: 'student'
        }).populate('parent_id');

        const parents = schoolStudents
          .filter(student => student.parent_id)
          .map(student => student.parent_id)
          .filter((parent, index, self) =>
            parent && self.findIndex(p => p._id.toString() === parent._id.toString()) === index
          );

        recipients = [...allUsers, ...parents];
        notificationTitle = `${actionEmoji} School Announcement`;
        notificationMessage = `${announcement.title} - ${school.name} has ${actionText} an important announcement.`;
        break;
    }

    // Remove duplicates and filter out null/undefined
    recipients = recipients.filter((recipient, index, self) =>
      recipient && self.findIndex(r => r._id.toString() === recipient._id.toString()) === index
    );

    console.log(`Sending announcement notifications to ${recipients.length} recipients for target: ${announcement.target_audience}`);

    // Send notifications to all recipients
    for (const recipient of recipients) {
      await NotificationService.createNotification({
        recipient_id: recipient._id,
        school_id: announcement.school_id,
        type: announcement.priority === 'urgent' ? 'warning' : 'info',
        category: 'announcement',
        title: notificationTitle,
        message: notificationMessage,
        sender_id: authorId,
        sender_type: 'user',
        related_entity: {
          entity_type: 'announcement',
          entity_id: announcement._id
        },
        action_url: `/announcements/view?id=${announcement.announcement_id}`,
        action_label: 'View Announcement',
        priority: announcement.priority,
        channels: {
          in_app: true,
          email: announcement.priority === 'urgent' || announcement.priority === 'high'
        }
      });
    }

    // Send confirmation notification to the author
    await NotificationService.createNotification({
      recipient_id: authorId,
      school_id: announcement.school_id,
      type: 'success',
      category: 'system',
      title: `Announcement ${action === 'created' ? 'Published' : 'Updated'} Successfully`,
      message: `Your announcement "${announcement.title}" has been ${actionText} and notifications sent to ${recipients.length} recipients.`,
      sender_type: 'system',
      related_entity: {
        entity_type: 'announcement',
        entity_id: announcement._id
      },
      action_url: `/school-admin/announcements`,
      action_label: 'View Announcements',
      channels: {
        in_app: true
      }
    });

    console.log(`✅ Announcement ${action} notifications sent successfully for "${announcement.title}"`);

  } catch (error) {
    console.error(`❌ Error sending announcement ${action} notifications:`, error);
    throw error;
  }
};

module.exports = {
  testAnnouncementResponse,
  getAllAnnouncements,
  createAnnouncement,
  getAnnouncementById,
  updateAnnouncementById,
  deleteAnnouncementById,
  deleteMultipleAnnouncements,
  deleteAllAnnouncements,
  getAnnouncementsBySchool,
};
