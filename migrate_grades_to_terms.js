// Migration script to update existing grades to use term_id instead of term string
const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const Grade = require('./src/models/Grade');
const Term = require('./src/models/Term');

async function migrateGradesToTerms() {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB for migration');

    // Get all grades that don't have term_id yet
    const gradesToMigrate = await Grade.find({
      term_id: { $exists: false }
    });

    console.log(`Found ${gradesToMigrate.length} grades to migrate`);

    if (gradesToMigrate.length === 0) {
      console.log('No grades to migrate');
      return;
    }

    // Group grades by school_id and academic_year for efficient processing
    const gradesBySchoolAndYear = {};
    
    gradesToMigrate.forEach(grade => {
      const key = `${grade.school_id}_${grade.academic_year}`;
      if (!gradesBySchoolAndYear[key]) {
        gradesBySchoolAndYear[key] = [];
      }
      gradesBySchoolAndYear[key].push(grade);
    });

    console.log(`Processing ${Object.keys(gradesBySchoolAndYear).length} school-year combinations`);

    let migratedCount = 0;
    let errorCount = 0;

    for (const [key, grades] of Object.entries(gradesBySchoolAndYear)) {
      const [schoolId, academicYear] = key.split('_');
      
      console.log(`\nProcessing school ${schoolId}, academic year ${academicYear}`);
      console.log(`Found ${grades.length} grades for this school-year`);

      // Get all terms for this school and academic year
      const terms = await Term.find({
        school_id: schoolId,
        academic_year: academicYear
      });

      if (terms.length === 0) {
        console.log(`No terms found for school ${schoolId}, academic year ${academicYear}`);
        console.log('Creating default terms...');
        
        // Create default terms for this school and academic year
        const defaultTerms = [
          {
            school_id: schoolId,
            name: 'Premier Trimestre',
            term_number: 1,
            sequences: [
              { sequence_number: 1, sequence_name: '1ère Séquence' },
              { sequence_number: 2, sequence_name: '2ème Séquence' }
            ],
            academic_year: academicYear,
            start_date: new Date(`${academicYear.split('-')[0]}-09-01`),
            end_date: new Date(`${academicYear.split('-')[0]}-12-15`),
            is_current: false,
            is_active: true
          },
          {
            school_id: schoolId,
            name: 'Deuxième Trimestre',
            term_number: 2,
            sequences: [
              { sequence_number: 3, sequence_name: '3ème Séquence' },
              { sequence_number: 4, sequence_name: '4ème Séquence' }
            ],
            academic_year: academicYear,
            start_date: new Date(`${academicYear.split('-')[1]}-01-01`),
            end_date: new Date(`${academicYear.split('-')[1]}-03-31`),
            is_current: false,
            is_active: true
          },
          {
            school_id: schoolId,
            name: 'Troisième Trimestre',
            term_number: 3,
            sequences: [
              { sequence_number: 5, sequence_name: '5ème Séquence' },
              { sequence_number: 6, sequence_name: '6ème Séquence' }
            ],
            academic_year: academicYear,
            start_date: new Date(`${academicYear.split('-')[1]}-04-01`),
            end_date: new Date(`${academicYear.split('-')[1]}-07-31`),
            is_current: false,
            is_active: true
          }
        ];

        try {
          const createdTerms = await Term.insertMany(defaultTerms);
          console.log(`Created ${createdTerms.length} default terms`);
          terms.push(...createdTerms);
        } catch (error) {
          console.error('Error creating default terms:', error);
          errorCount += grades.length;
          continue;
        }
      }

      // Create a mapping from term string to term_id
      const termMapping = {
        'First Term': terms.find(t => t.term_number === 1)?._id,
        'Second Term': terms.find(t => t.term_number === 2)?._id,
        'Third Term': terms.find(t => t.term_number === 3)?._id,
        'Premier Trimestre': terms.find(t => t.term_number === 1)?._id,
        'Deuxième Trimestre': terms.find(t => t.term_number === 2)?._id,
        'Troisième Trimestre': terms.find(t => t.term_number === 3)?._id
      };

      console.log('Term mapping:', termMapping);

      // Update each grade
      for (const grade of grades) {
        try {
          const termId = termMapping[grade.term];
          
          if (!termId) {
            console.log(`No term mapping found for "${grade.term}" in grade ${grade._id}`);
            errorCount++;
            continue;
          }

          // Default to sequence 1 if not specified
          const sequenceNumber = 1; // You might want to make this more intelligent based on exam_type or date

          await Grade.findByIdAndUpdate(grade._id, {
            term_id: termId,
            sequence_number: sequenceNumber
          });

          migratedCount++;
          
          if (migratedCount % 100 === 0) {
            console.log(`Migrated ${migratedCount} grades so far...`);
          }
        } catch (error) {
          console.error(`Error migrating grade ${grade._id}:`, error);
          errorCount++;
        }
      }
    }

    console.log(`\nMigration completed!`);
    console.log(`Successfully migrated: ${migratedCount} grades`);
    console.log(`Errors: ${errorCount} grades`);

    // Verify migration
    const remainingGrades = await Grade.countDocuments({
      term_id: { $exists: false }
    });
    
    console.log(`Remaining grades without term_id: ${remainingGrades}`);

  } catch (error) {
    console.error('Migration error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  }
}

// Run the migration
if (require.main === module) {
  migrateGradesToTerms();
}

module.exports = { migrateGradesToTerms };
