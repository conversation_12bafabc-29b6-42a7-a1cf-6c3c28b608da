const mongoose = require('mongoose');

const ChatbotUsageSchema = new mongoose.Schema({
  // Référence à l'école
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'School',
    required: true
  },
  
  // Référence à l'utilisateur
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  
  // Référence à la souscription
  subscription_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'SchoolSubscription',
    required: true
  },
  
  // Identifiant de conversation
  conversation_id: {
    type: String,
    required: true
  },
  
  // Message de l'utilisateur
  user_message: {
    type: String,
    required: true,
    maxlength: 2000
  },
  
  // Réponse du chatbot
  bot_response: {
    type: String,
    required: true,
    maxlength: 5000
  },
  
  // Métadonnées du message
  message_metadata: {
    message_length: Number,
    response_length: Number,
    processing_time: Number, // en millisecondes
    model_used: String,
    tokens_used: Number,
    confidence_score: Number
  },
  
  // Coût en crédits
  credits_charged: {
    type: Number,
    required: true,
    default: 1
  },
  
  // Date et heure
  usage_date: {
    type: Date,
    default: Date.now
  },
  
  // Informations de session
  session_info: {
    ip_address: String,
    user_agent: String,
    device_type: String,
    browser: String,
    os: String
  },
  
  // Contexte de la conversation
  conversation_context: {
    previous_messages_count: Number,
    conversation_duration: Number, // en minutes
    topic_category: String,
    intent_detected: String
  },
  
  // Évaluation de la réponse
  feedback: {
    rating: {
      type: Number,
      min: 1,
      max: 5
    },
    helpful: Boolean,
    feedback_text: String,
    feedback_date: Date
  },
  
  // Statut du message
  status: {
    type: String,
    enum: ['completed', 'failed', 'timeout', 'cancelled'],
    default: 'completed'
  },
  
  // Informations d'erreur si applicable
  error_info: {
    error_code: String,
    error_message: String,
    error_details: mongoose.Schema.Types.Mixed
  },
  
  // Période de facturation (pour le plan standard)
  billing_period: {
    year: Number,
    month: Number
  },
  
  // Indicateurs de qualité
  quality_metrics: {
    response_relevance: Number,
    user_satisfaction: Number,
    resolution_achieved: Boolean
  }
}, {
  timestamps: true
});

// Index pour optimiser les requêtes
ChatbotUsageSchema.index({ school_id: 1, usage_date: -1 });
ChatbotUsageSchema.index({ user_id: 1, usage_date: -1 });
ChatbotUsageSchema.index({ conversation_id: 1, usage_date: 1 });
ChatbotUsageSchema.index({ 'billing_period.year': 1, 'billing_period.month': 1, school_id: 1 });

// Méthodes d'instance
ChatbotUsageSchema.methods.addFeedback = function(rating, helpful, feedback_text) {
  this.feedback = {
    rating,
    helpful,
    feedback_text,
    feedback_date: new Date()
  };
  
  return this.save();
};

// Méthodes statiques
ChatbotUsageSchema.statics.recordUsage = async function(data) {
  const {
    school_id,
    user_id,
    subscription_id,
    conversation_id,
    user_message,
    bot_response,
    message_metadata = {},
    credits_charged = 1,
    session_info = {},
    conversation_context = {}
  } = data;
  
  const now = new Date();
  const billing_period = {
    year: now.getFullYear(),
    month: now.getMonth() + 1
  };
  
  const usage = new this({
    school_id,
    user_id,
    subscription_id,
    conversation_id,
    user_message,
    bot_response,
    message_metadata,
    credits_charged,
    session_info,
    conversation_context,
    billing_period
  });
  
  return usage.save();
};

ChatbotUsageSchema.statics.getMonthlyUsage = function(school_id, year, month) {
  return this.aggregate([
    {
      $match: {
        school_id: new mongoose.Types.ObjectId(school_id),
        'billing_period.year': year,
        'billing_period.month': month,
        status: 'completed'
      }
    },
    {
      $group: {
        _id: null,
        total_messages: { $sum: 1 },
        total_credits: { $sum: '$credits_charged' },
        unique_users: { $addToSet: '$user_id' },
        unique_conversations: { $addToSet: '$conversation_id' },
        avg_processing_time: { $avg: '$message_metadata.processing_time' },
        avg_response_length: { $avg: '$message_metadata.response_length' }
      }
    },
    {
      $project: {
        total_messages: 1,
        total_credits: 1,
        unique_users_count: { $size: '$unique_users' },
        unique_conversations_count: { $size: '$unique_conversations' },
        avg_processing_time: 1,
        avg_response_length: 1
      }
    }
  ]);
};

ChatbotUsageSchema.statics.getUserUsage = function(user_id, limit = 50) {
  return this.find({ user_id })
    .sort({ usage_date: -1 })
    .limit(limit)
    .select('conversation_id user_message bot_response usage_date credits_charged feedback');
};

ChatbotUsageSchema.statics.getConversationHistory = function(conversation_id) {
  return this.find({ conversation_id })
    .sort({ usage_date: 1 })
    .select('user_message bot_response usage_date message_metadata');
};

ChatbotUsageSchema.statics.getUsageStats = function(school_id, period = 'month') {
  const now = new Date();
  let matchCondition = { school_id: new mongoose.Types.ObjectId(school_id), status: 'completed' };
  
  switch (period) {
    case 'day':
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
      matchCondition.usage_date = { $gte: today };
      break;
    case 'week':
      const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
      matchCondition.usage_date = { $gte: weekAgo };
      break;
    case 'month':
      matchCondition['billing_period.year'] = now.getFullYear();
      matchCondition['billing_period.month'] = now.getMonth() + 1;
      break;
  }
  
  return this.aggregate([
    { $match: matchCondition },
    {
      $group: {
        _id: null,
        total_messages: { $sum: 1 },
        total_credits: { $sum: '$credits_charged' },
        unique_users: { $addToSet: '$user_id' },
        avg_rating: { $avg: '$feedback.rating' },
        helpful_responses: { $sum: { $cond: ['$feedback.helpful', 1, 0] } }
      }
    },
    {
      $project: {
        total_messages: 1,
        total_credits: 1,
        unique_users_count: { $size: '$unique_users' },
        avg_rating: { $round: ['$avg_rating', 2] },
        helpful_percentage: {
          $round: [
            { $multiply: [{ $divide: ['$helpful_responses', '$total_messages'] }, 100] },
            2
          ]
        }
      }
    }
  ]);
};

// Middleware pre-save
ChatbotUsageSchema.pre('save', function(next) {
  // Définir la période de facturation si pas encore définie
  if (!this.billing_period.year || !this.billing_period.month) {
    const now = new Date();
    this.billing_period = {
      year: now.getFullYear(),
      month: now.getMonth() + 1
    };
  }
  
  // Calculer les métadonnées du message si pas encore définies
  if (!this.message_metadata.message_length) {
    this.message_metadata.message_length = this.user_message.length;
  }
  
  if (!this.message_metadata.response_length) {
    this.message_metadata.response_length = this.bot_response.length;
  }
  
  next();
});

const ChatbotUsage = mongoose.models.ChatbotUsage || mongoose.model('ChatbotUsage', ChatbotUsageSchema);

module.exports = ChatbotUsage;
