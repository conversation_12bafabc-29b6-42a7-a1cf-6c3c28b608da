'use client';
import React, { useEffect, useState } from 'react';
import { SubjectSchema } from '@/app/models/Subject';
import { Loader2, X } from 'lucide-react';
import CircularLoader from '@/components/widgets/CircularLoader';
import { motion } from 'framer-motion';
import CustomInput from '@/components/inputs/CustomInput';
import CustomCheckboxInput from '@/components/inputs/CustomCheckBoxInput';
import { ClassSchema } from '@/app/models/ClassModel';
import { getClassById, getClassesBySchool } from '@/app/services/ClassServices';
import SubmissionFeedback from '@/components/widgets/SubmissionFeedback';

interface CreateSubjectModalProps {
    onClose: () => void;
    onSave: (subjectData: SubjectSchema) => Promise<void>;
    submitStatus: 'success' | 'failure' | null;
    isSubmitting: boolean;
    SchoolId: string;
    initialData?: SubjectSchema | null;
}

const CreateSubjectModal: React.FC<CreateSubjectModalProps> = ({
    onClose,
    onSave,
    submitStatus,
    isSubmitting,
    SchoolId,
    initialData = null,

}) => {
    const [formData, setFormData] = useState<SubjectSchema>({
        _id: '',
        name: '',
        subject_id: '',
        subject_code: '',
        compulsory: false,
        school_id: SchoolId,
        class_id: [],
        description: '',
        coefficient: 0,
        createdAt: '',
        updatedAt: '',
    });

    const [classes, setClasses] = useState<ClassSchema[]>([]);
    const [searchTerm, setSearchTerm] = useState('');
    const isEditMode = !!initialData;

    useEffect(() => {
        if (initialData) {
            setFormData({
                ...initialData,
                school_id: SchoolId,
                coefficient: initialData.coefficient || 0,
            });
        }
    }, [initialData, SchoolId]);
    const fetchClass = async () => {
        const result = await getClassesBySchool(SchoolId);
        const classArray = Array.isArray(result) ? result : [];
        const mappedClasses = classArray.map((cls: any) => ({
            _id: cls._id,
            name: cls.name,
            class_id: cls.class_id ?? '',
            class_level: cls.class_level ?? '',
            class_code: cls.class_code ?? '',
            school_id: cls.school_id ?? '',
        }));
        setClasses(mappedClasses);
    };

    useEffect(() => {
        fetchClass();
    }, []);

    const handleChange = (
        e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
    ) => {
        const { name, value, type } = e.target;

        setFormData((prev) => ({
            ...prev,
            [name]: type === 'checkbox'
                ? (e.target as HTMLInputElement).checked
                : value,
        }));
    };

    const handleClassCheckboxChange = (classId: string) => {
        setFormData(prev => {
            const selected = new Set(prev.class_id);
            selected.has(classId) ? selected.delete(classId) : selected.add(classId);
            return { ...prev, class_id: Array.from(selected) };
        });
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        await onSave(formData);
    };

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60">
            <div className="bg-background text-foreground rounded-2xl shadow-xl w-full max-w-2xl p-6 relative">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-2xl font-semibold mb-4">
                        {isEditMode ? 'Edit Subject' : 'Create New Subject'}
                    </h2>
                    <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
                        <X size={20} />
                    </button>
                </div>
                {submitStatus ? (
                    <SubmissionFeedback status={submitStatus}
                        message={
                            submitStatus === "success"
                                ? "Subject has been sent Successfully!"
                                : "There was an error Creating this Subject. Try again and if this persist contact support!"
                        } />
                ) : (
                    <form onSubmit={handleSubmit} className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <CustomInput
                            label="Subject Name"
                            id="name"
                            name="name"
                            placeholder="Subject Name"
                            value={formData.name}
                            onChange={handleChange}
                            required
                        />
                        <CustomInput
                            label="Subject Code"
                            id="subject_code"
                            name="subject_code"
                            placeholder="Subject Code"
                            value={formData.subject_code}
                            onChange={handleChange}
                            required
                        />

                        {/* Searchable class checkbox */}
                        <div className="col-span-2">
                            <label className="block mb-1 text-sm font-medium text-gray-700 dark:text-gray-300">
                                Search and Select Class(es)
                            </label>
                            <input
                                type="text"
                                value={searchTerm}
                                onChange={(e) => setSearchTerm(e.target.value)}
                                placeholder="Search class..."
                                className="w-full mb-2 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm dark:bg-gray-700"
                            />
                            <div className="max-h-24 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md p-2 bg-white dark:bg-gray-800 custom-scrollbar">
                                {classes
                                    .filter(cls => cls.name.toLowerCase().includes(searchTerm.toLowerCase()))
                                    .map(cls => (
                                        <div key={cls._id} className="flex items-center mb-1">
                                            <input
                                                type="checkbox"
                                                id={`class-${cls._id}`}
                                                checked={formData.class_id.includes(cls._id)}
                                                onChange={() => handleClassCheckboxChange(cls._id)}
                                                className="w-4 h-4 text-teal-600 border-gray-300 rounded focus:ring-teal-500"
                                            />
                                            <label htmlFor={`class-${cls._id}`} className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                                {cls.name}
                                            </label>
                                        </div>
                                    ))}
                                {classes.filter(cls => cls.name.toLowerCase().includes(searchTerm.toLowerCase())).length === 0 && (
                                    <p className="text-sm text-gray-500 italic">No classes found</p>
                                )}
                            </div>
                        </div>

                        <CustomInput
                            label="Coefficient"
                            id='coefficient'
                            name="coefficient"
                            placeholder="Coefficient"
                            value={formData.coefficient.toString()}
                            onChange={handleChange}
                            type='number'
                            required
                        />
                        <CustomCheckboxInput
                            name="compulsory"
                            checked={formData.compulsory}
                            onChange={handleChange}
                            label="Compulsory Subject"
                            id="compulsory"
                        />

                        <textarea
                            name="description"
                            placeholder="Description"
                            value={formData.description}
                            onChange={handleChange}
                            rows={4}
                            className="col-span-2 w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm text-foreground dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-teal"
                        />
                        <div className="flex justify-end space-x-2 mt-6 col-span-2">
                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: 'spring', stiffness: 300 }}
                                type="button"
                                onClick={onClose}
                                className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                                disabled={isSubmitting}
                            >
                                Cancel
                            </motion.button>

                            <motion.button
                                whileHover={{ scale: 1.05 }}
                                whileTap={{ scale: 0.95 }}
                                transition={{ type: 'spring', stiffness: 300 }}
                                type="submit"
                                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 flex items-center gap-2"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <CircularLoader size={18} color="teal-500" />
                                        Saving...
                                    </>
                                ) : (
                                    isEditMode ? 'Update Subject' : 'Create Subject'
                                )}
                            </motion.button>
                        </div>
                    </form>
                )}
            </div>
        </div>
    );
};

export default CreateSubjectModal;
