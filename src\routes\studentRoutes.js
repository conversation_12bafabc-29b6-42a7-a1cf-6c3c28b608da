const express = require('express');
const multer = require('multer')
const upload = require('../utils/multer');
const { uploadCSV, uploadAvatarImage } = require('../utils/uploaders');

const studentController = require('../controllers/studentController'); // Updated controller import
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');
//registerStudentWithPaymentAndParent
const router = express.Router();
// router.get('/test', studentController.testStudentResponse); // Updated route to match student

router.post('/register-students', authenticate, authorize(['admin', 'super']), studentController.registerStudentWithPaymentAndParent);

router.get('/search-students', authenticate, checkSubscription, authorize(['admin', 'super', 'teacher', 'parent', 'school_admin', 'dean_of_studies']), studentController.searchStudent);

// GET /students to fetch all student records
router.get('/get-students', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), studentController.getAllStudents);
router.get('/get-students-by-school', authenticate, checkSubscription, authorize(['admin', 'super','teacher', 'school_admin', 'dean_of_studies', 'bursar']), studentController.getStudentsBySchoolId);
// Get students by ID
router.get('/get-student/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']), studentController.getStudentById);

router.get("/class/:classId/school/:schoolId", authenticate, checkSubscription, authorize(['admin', 'super', 'teacher', 'school_admin', 'dean_of_studies']), studentController.getStudentsByClassAndSchool);
// POST /students to create a new student record
router.post('/create-student', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), studentController.createStudent);

// PUT /students/:id to update a specific student record
router.put('/update-student/:id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), studentController.updateStudentById);

// DELETE /students/:id to delete a specific student record
router.delete('/delete-student/:id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), studentController.deleteStudentById);

//DELETE multiple students
router.delete('/delete-students', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), studentController.deleteMultipleStudents);

//DELETE ALL students
router.delete('/delete-all-students', authenticate, authorize(['super']), studentController.deleteAllStudents);

router.post('/import-csv-students/:schoolId',uploadCSV.single("file"), authenticate,authorize(['admin', 'super', 'school_admin', 'dean_of_studies']),studentController.importStudentsFromCSV);
router.post('/upload-student-photo/:id/avatar', uploadAvatarImage.single('avatar'),authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), studentController.uploadStudentAvatar);
router.get(
  "/total-students",
  authenticate,
  checkSubscription,
  authorize(["super"]),
  studentController.getTotalStudents
);

// Route to get number of students created this month and percentage change
router.get(
  "/students-count-change",
  authenticate,
  checkSubscription,
  authorize(["super"]),
  studentController.getStudentCountWithChange
);

router.get("/verify-id/:id", studentController.getStudentNameAndId);

module.exports = router;
