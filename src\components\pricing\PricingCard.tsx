"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Check, Star, ArrowRight, MessageCircle, Mail, Phone } from 'lucide-react';
import { SubscriptionPlanSchema } from '@/app/models/SchoolSubscriptionModel';
import { useRouter } from 'next/navigation';

interface PricingCardProps {
  plan: SubscriptionPlanSchema;
}

export default function PricingCard({ plan }: PricingCardProps) {
  const router = useRouter();

  const handleSelectPlan = () => {
    if (plan.plan_name === 'custom') {
      // Redirect to contact form or show contact modal
      window.location.href = `mailto:${plan.contact_info?.email}?subject=Demande de plan personnalisé`;
    } else {
      // Redirect to school admin registration or login
      router.push(`/auth/register?plan=${plan.plan_name}`);
    }
  };

  const getCardStyle = () => {
    const baseStyle = "relative bg-white rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl";
    
    if (plan.is_popular) {
      return `${baseStyle} border-blue-500 transform hover:scale-105`;
    }
    
    if (plan.plan_name === 'custom') {
      return `${baseStyle} border-purple-500 bg-gradient-to-br from-purple-50 to-white`;
    }
    
    return `${baseStyle} border-gray-200 hover:border-gray-300`;
  };

  const getButtonStyle = () => {
    if (plan.plan_name === 'custom') {
      return "w-full py-3 px-6 bg-gradient-to-r from-purple-600 to-purple-700 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-purple-800 transition-all duration-300";
    }
    
    if (plan.is_popular) {
      return "w-full py-3 px-6 bg-blue-600 text-white font-semibold rounded-lg hover:bg-blue-700 transition-colors";
    }
    
    return "w-full py-3 px-6 border-2 border-gray-300 text-gray-700 font-semibold rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors";
  };

  return (
    <motion.div
      className={getCardStyle()}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      {/* Popular Badge */}
      {plan.is_popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-blue-600 text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center">
            <Star className="h-4 w-4 mr-1" />
            Plus populaire
          </div>
        </div>
      )}

      <div className="p-8">
        {/* Plan Header */}
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 mb-2">
            {plan.display_name}
          </h3>
          <p className="text-gray-600 mb-6">
            {plan.description}
          </p>
          
          {/* Pricing */}
          <div className="mb-6">
            {plan.plan_name === 'custom' ? (
              <div>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  Sur mesure
                </div>
                <div className="text-gray-600">
                  Tarification personnalisée
                </div>
              </div>
            ) : (
              <div>
                <div className="text-4xl font-bold text-gray-900 mb-2">
                  {plan.price_per_credit.toLocaleString()} FCFA
                  <span className="text-lg font-normal text-gray-600">/crédit</span>
                </div>
                <div className="text-gray-600">
                  Minimum {plan.minimum_purchase} crédit{plan.minimum_purchase > 1 ? 's' : ''}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Features */}
        <div className="mb-8">
          <h4 className="font-semibold text-gray-900 mb-4">Fonctionnalités incluses :</h4>
          <ul className="space-y-3">
            {plan.benefits.map((benefit, index) => (
              <li key={index} className="flex items-start">
                <Check className="h-5 w-5 text-green-500 mr-3 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700">{benefit}</span>
              </li>
            ))}
          </ul>
          
          {/* Chatbot Info */}
          {plan.chatbot_enabled && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg">
              <div className="flex items-center text-blue-700">
                <MessageCircle className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">
                  Chatbot IA : {plan.chatbot_cost_per_message} crédit/message
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Limitations */}
        {plan.limitations && plan.limitations.length > 0 && (
          <div className="mb-8">
            <h4 className="font-semibold text-gray-900 mb-4">Limitations :</h4>
            <ul className="space-y-2">
              {plan.limitations.map((limitation, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600">
                  <span className="mr-2">•</span>
                  <span>{limitation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Contact Info for Custom Plan */}
        {plan.plan_name === 'custom' && plan.contact_info && (
          <div className="mb-8 p-4 bg-purple-50 rounded-lg">
            <h4 className="font-semibold text-purple-900 mb-3">Contactez-nous :</h4>
            <div className="space-y-2">
              {plan.contact_info.email && (
                <div className="flex items-center text-purple-700">
                  <Mail className="h-4 w-4 mr-2" />
                  <span className="text-sm">{plan.contact_info.email}</span>
                </div>
              )}
              {plan.contact_info.phone && (
                <div className="flex items-center text-purple-700">
                  <Phone className="h-4 w-4 mr-2" />
                  <span className="text-sm">{plan.contact_info.phone}</span>
                </div>
              )}
            </div>
            {plan.contact_info.message && (
              <p className="text-sm text-purple-600 mt-3">
                {plan.contact_info.message}
              </p>
            )}
          </div>
        )}

        {/* CTA Button */}
        <button
          onClick={handleSelectPlan}
          className={getButtonStyle()}
        >
          <span className="flex items-center justify-center">
            {plan.plan_name === 'custom' ? 'Nous contacter' : 'Choisir ce plan'}
            <ArrowRight className="h-4 w-4 ml-2" />
          </span>
        </button>

        {/* Additional Info */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            {plan.plan_name === 'custom' 
              ? 'Devis personnalisé sous 24h'
              : 'Commencez avec 5 crédits gratuits'
            }
          </p>
        </div>
      </div>
    </motion.div>
  );
}
