"use client";

import React from 'react';
import { motion } from 'framer-motion';
import {
  SkeletonCard,
  SkeletonLine,
  SkeletonCircle,
  SkeletonButton,
  SkeletonBadge,
  SkeletonIcon,
  SkeletonHeader,
  SkeletonStatsGrid
} from './SkeletonCard';

interface TeacherDashboardSkeletonProps {
  itemCount?: number;
}

const TeacherDashboardSkeleton: React.FC<TeacherDashboardSkeletonProps> = ({
  itemCount = 4
}) => {
  return (
    <div className="space-y-6">
      {/* Welcome Section Skeleton */}
      <SkeletonCard>
        <div className="flex items-center space-x-3 mb-4">
          <SkeletonCircle size="w-12 h-12" />
          <div className="flex-1">
            <SkeletonLine width="w-2/3" height="h-8" className="mb-2" />
            <SkeletonLine width="w-1/2" height="h-4" />
          </div>
        </div>
      </SkeletonCard>

      {/* Dashboard Stats Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <SkeletonCard>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <SkeletonLine width="w-16" height="h-4" className="mb-2" />
                  <SkeletonLine width="w-12" height="h-8" className="mb-1" />
                  <SkeletonLine width="w-20" height="h-3" />
                </div>
                <SkeletonCircle size="w-12 h-12" />
              </div>
            </SkeletonCard>
          </motion.div>
        ))}
      </div>

      {/* Quick Actions Grid Skeleton */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
        {Array.from({ length: 4 }).map((_, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 + index * 0.1 }}
          >
            <SkeletonCard>
              <div className="flex items-center space-x-3 mb-3">
                <SkeletonCircle size="w-10 h-10" />
                <div className="flex-1">
                  <SkeletonLine width="w-3/4" height="h-5" className="mb-1" />
                  <SkeletonLine width="w-full" height="h-3" />
                </div>
              </div>
              <SkeletonButton className="w-full" />
            </SkeletonCard>
          </motion.div>
        ))}
      </div>

      {/* Today's Schedule Preview Skeleton */}
      <SkeletonCard>
        <div className="flex items-center justify-between mb-4">
          <SkeletonLine width="w-40" height="h-6" />
          <SkeletonButton width="w-32" height="h-6" />
        </div>
        
        <div className="text-center py-8">
          <SkeletonCircle size="w-12 h-12" className="mx-auto mb-4" />
          <SkeletonLine width="w-48" height="h-4" className="mx-auto mb-2" />
          <SkeletonLine width="w-56" height="h-3" className="mx-auto" />
        </div>
      </SkeletonCard>
    </div>
  );
};

export default TeacherDashboardSkeleton;
