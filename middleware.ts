import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
    const { pathname } = request.nextUrl;
    
    // Allow access to maintenance mode endpoints and API routes
    if (
        pathname.startsWith('/api/') ||
        pathname.startsWith('/_next/') ||
        pathname.startsWith('/static/') ||
        pathname.includes('maintenance') ||
        pathname.includes('favicon.ico') ||
        pathname.includes('logo')
    ) {
        return NextResponse.next();
    }

    // Check if maintenance mode is enabled
    // This is a simplified check - in a real app, you'd want to check against your database
    const maintenanceMode = process.env.MAINTENANCE_MODE === 'true';
    
    // Allow super-admins to bypass maintenance mode
    const isAdminPath = pathname.includes('/super-admin');
    
    if (maintenanceMode && !isAdminPath) {
        // Redirect to maintenance page
        const maintenanceUrl = new URL('/maintenance', request.url);
        return NextResponse.redirect(maintenanceUrl);
    }

    return NextResponse.next();
}

export const config = {
    matcher: [
        /*
         * Match all request paths except for the ones starting with:
         * - api (API routes)
         * - _next/static (static files)
         * - _next/image (image optimization files)
         * - favicon.ico (favicon file)
         */
        '/((?!api|_next/static|_next/image|favicon.ico).*)',
    ],
};