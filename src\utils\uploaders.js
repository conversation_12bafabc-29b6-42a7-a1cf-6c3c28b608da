const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { CloudinaryStorage } = require('multer-storage-cloudinary');
const cloudinary = require('../utils/cloudinary');

/* ------------------------------------------------------------------ */
/*  🗂  1. CSV UPLOADER  — local disk                                 */
/* ------------------------------------------------------------------ */

const csvStorage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadDir = 'uploads/';

    // Check if folder exists, if not create it
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    cb(null, uploadDir);
  },
  filename: (req, file, cb) => {
    const unique = Date.now() + '-' + Math.round(Math.random() * 1e9);
    cb(null, unique + path.extname(file.originalname)); // e.g. 1629999999-123.csv
  },
});

const csvFileFilter = (req, file, cb) => {
  const ok =
    file.mimetype === 'text/csv' ||
    file.mimetype === 'application/vnd.ms-excel' ||
    file.originalname.toLowerCase().endsWith('.csv');

  cb(ok ? null : new Error('Only CSV files are allowed'), ok);
};

const uploadCSV = multer({ storage: csvStorage, fileFilter: csvFileFilter });

/* ------------------------------------------------------------------ */
/*  🖼  2. AVATAR IMAGE UPLOADER  — Cloudinary                        */
/* ------------------------------------------------------------------ */

const avatarStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req) => {
    const studentId = req.params.id || 'unknown';
    return {
      folder: `students/${studentId}/avatar`,
      allowed_formats: ['jpg', 'jpeg', 'png'],
      public_id: 'avatar', // ensures one avatar per student (overwrite)
    };
  },
});

const uploadAvatarImage = multer({ storage: avatarStorage });

/* ------------------------------------------------------------------ */
/* ------------------------------------------------------------------ */
/*  📄 3. GENERIC FILE UPLOADER — Cloudinary                         */
/* ------------------------------------------------------------------ */

const justificationFileStorage = new CloudinaryStorage({
  cloudinary,
  params: async (req, file) => {
    const schoolNameRaw = req.body.schoolName || req.params.schoolName || 'unknown-school';
    const schoolName = schoolNameRaw.replace(/\s+/g, '-').toLowerCase(); // sanitize folder name

    return {
      folder: `${schoolName}/justifications`,
      resource_type: 'auto', // handles images, pdfs, docs, etc.
      public_id: path.parse(file.originalname).name, // keep original name (no extension)
    };
  },
});

const uploadJustificationFile = multer({ storage: justificationFileStorage });


module.exports = {
  uploadCSV,          // .single('file')
  uploadAvatarImage,  // .single('image')
  uploadJustificationFile,
};
