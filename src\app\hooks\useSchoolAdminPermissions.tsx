"use client";

import { useState, useEffect } from "react";
import useAuth from "./useAuth";
import { getCurrentUserStaffPermissions, StaffPermissions } from "../services/StaffPermissionServices";

// Define permission structure for school admin roles (matching backend StaffPermission model)
export interface SchoolAdminPermissions {
  students: {
    view_all_students: boolean;
    add_edit_delete_students: boolean;
    generate_id_cards: boolean;
    generate_report_cards: boolean;
  };
  academic_records: {
    view_grades_assigned_classes: boolean;
    enter_edit_grades_assigned_classes: boolean;
    view_all_school_grades: boolean;
    take_attendance_assigned_classes: boolean;
    view_all_attendance: boolean;
    manage_terms: boolean;
    manage_timetables: boolean;
    manage_periods: boolean;
    manage_subjects: boolean;
    manage_classes: boolean;
    manage_exam_types: boolean;
    manage_discipline: boolean;
  };
  staff: {
    view_staff_list: boolean;
    add_edit_delete_staff: boolean;
    manage_staff_permissions: boolean;
    reset_staff_passwords: boolean;
    manage_teacher_assignments: boolean;
  };
  financials: {
    view_student_fee_balances: boolean;
    record_fee_payments: boolean;
    manage_school_credit_balance: boolean;
    view_financial_reports: boolean;
    manage_fee_types: boolean;
    view_transactions: boolean;
  };
  classes: {
    view_all_classes: boolean;
    add_edit_delete_classes: boolean;
    manage_class_schedules: boolean;
    assign_teachers_to_classes: boolean;
  };
  announcements: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
  };
  resources: {
    view_resources: boolean;
    add_edit_delete_resources: boolean;
    manage_resource_categories: boolean;
  };
  reports: {
    generate_student_reports: boolean;
    generate_financial_reports: boolean;
    generate_attendance_reports: boolean;
    export_data: boolean;
  };
  communications: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
    manage_resources: boolean;
  };
  school_management: {
    view_school_info: boolean;
    edit_school_info: boolean;
    manage_school_settings: boolean;
  };
}

interface UseSchoolAdminPermissionsReturn {
  permissions: SchoolAdminPermissions | null;
  hasPermission: (module: keyof SchoolAdminPermissions, action: string) => boolean;
  hasAnyPermission: (module: keyof SchoolAdminPermissions, actions: string[]) => boolean;
  hasAllPermissions: (module: keyof SchoolAdminPermissions, actions: string[]) => boolean;
  canAccess: (requiredPermissions: { module: keyof SchoolAdminPermissions; action: string }[]) => boolean;
  isLoading: boolean;
  userRole: string | null;
  isSchoolAdmin: boolean;
  isBursar: boolean;
  isDeanOfStudies: boolean;
  isSuperAdmin: boolean;
  selectedSchoolId: string | null;
  setSelectedSchoolId: (schoolId: string | null) => void;
}

export default function useSchoolAdminPermissions(schoolId?: string): UseSchoolAdminPermissionsReturn {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<SchoolAdminPermissions | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedSchoolId, setSelectedSchoolId] = useState<string | null>(schoolId || null);

  useEffect(() => {
    setSelectedSchoolId(schoolId? schoolId : null);
    const loadPermissions = async () => {
      console.log("Loading permissions for school admin... : ", user, "schoolId: ", selectedSchoolId);
      if (!user) {
        setPermissions(null);
        setIsLoading(false);
        return;
      }

      // For super admin, grant all permissions
      if (user.role === 'super' || user.role === 'admin') {
        const allPermissions: SchoolAdminPermissions = {
          students: {
            view_all_students: true,
            add_edit_delete_students: true,
            generate_id_cards: true,
            generate_report_cards: true,
          },
          academic_records: {
            view_grades_assigned_classes: true,
            enter_edit_grades_assigned_classes: true,
            view_all_school_grades: true,
            take_attendance_assigned_classes: true,
            view_all_attendance: true,
            manage_terms: true,
            manage_timetables: true,
            manage_periods: true,
            manage_subjects: true,
            manage_classes: true,
            manage_exam_types: true,
            manage_discipline: true,
          },
          staff: {
            view_staff_list: true,
            add_edit_delete_staff: true,
            manage_staff_permissions: true,
            reset_staff_passwords: true,
            manage_teacher_assignments: true,
          },
          financials: {
            view_student_fee_balances: true,
            record_fee_payments: true,
            manage_school_credit_balance: true,
            view_financial_reports: true,
            manage_fee_types: true,
            view_transactions: true,
          },
          classes: {
            view_all_classes: true,
            add_edit_delete_classes: true,
            manage_class_schedules: true,
            assign_teachers_to_classes: true,
          },
          announcements: {
            view_announcements: true,
            create_edit_announcements: true,
            delete_announcements: true,
            publish_announcements: true,
          },
          resources: {
            view_resources: true,
            add_edit_delete_resources: true,
            manage_resource_categories: true,
          },
          reports: {
            generate_student_reports: true,
            generate_financial_reports: true,
            generate_attendance_reports: true,
            export_data: true,
          },
          communications: {
            view_announcements: true,
            create_edit_announcements: true,
            delete_announcements: true,
            publish_announcements: true,
            manage_resources: true,
          },
          school_management: {
            view_school_info: true,
            edit_school_info: true,
            manage_school_settings: true,
          },
        };
        setPermissions(allPermissions);
        setIsLoading(false);
        return;
      }

      // For other roles, fetch permissions from database
      if (selectedSchoolId) {
        try {
          const staffPermissions = await getCurrentUserStaffPermissions(selectedSchoolId);
          console.log("Staff Permissions:", staffPermissions);
          if (staffPermissions) {
            setPermissions(staffPermissions.permissions);
          } else {
            // No permissions found, set empty permissions
            setPermissions(null);
          }
        } catch (error) {
          console.error("Error loading staff permissions:", error);
          setPermissions(null);
        }
      } else {
        setPermissions(null);
      }

      setIsLoading(false);
    };

    loadPermissions();
  }, [user, selectedSchoolId]);



  const hasPermission = (module: keyof SchoolAdminPermissions, action: string): boolean => {
    if (!permissions) return false;
    if (user?.role === 'super' || user?.role === 'admin') return true;
    
    const modulePermissions = permissions[module] as Record<string, boolean>;
    return modulePermissions?.[action] || false;
  };

  const hasAnyPermission = (module: keyof SchoolAdminPermissions, actions: string[]): boolean => {
    return actions.some(action => hasPermission(module, action));
  };

  const hasAllPermissions = (module: keyof SchoolAdminPermissions, actions: string[]): boolean => {
    return actions.every(action => hasPermission(module, action));
  };

  const canAccess = (requiredPermissions: { module: keyof SchoolAdminPermissions; action: string }[]): boolean => {
    return requiredPermissions.every(({ module, action }) => hasPermission(module, action));
  };

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess,
    isLoading,
    userRole: user?.role || null,
    isSchoolAdmin: user?.role === 'school_admin',
    isBursar: user?.role === 'bursar',
    isDeanOfStudies: user?.role === 'dean_of_studies',
    isSuperAdmin: user?.role === 'super' || user?.role === 'admin',
    selectedSchoolId,
    setSelectedSchoolId,
  };
}
