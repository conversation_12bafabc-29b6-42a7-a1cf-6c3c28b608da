"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    SkeletonCard,
    SkeletonLine,
    SkeletonCircle,
    SkeletonButton,
    SkeletonBadge,
    SkeletonIcon,
    SkeletonHeader,
    SkeletonStatsGrid,
} from "./SkeletonCard";

interface TeacherClassesSkeletonProps {
    itemCount?: number;
}

const TeacherClassesSkeleton: React.FC<TeacherClassesSkeletonProps> = ({
    itemCount = 4,
}) => {
    return (
        <div className="space-y-6">
            {/* Header Skeleton */}
            <SkeletonHeader />

            {/* Classes Grid Skeleton */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                {Array.from({ length: itemCount }).map((_, index) => (
                    <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                    >
                        <SkeletonCard>
                            {/* Class Header Skeleton */}
                            <div className="flex items-start justify-between mb-4">
                                <div className="flex-1">
                                    <SkeletonLine
                                        width="w-3/4"
                                        height="h-6"
                                        className="mb-2"
                                    />
                                    <div className="flex items-center space-x-2">
                                        <SkeletonIcon />
                                        <SkeletonLine
                                            width="w-20"
                                            height="h-4"
                                        />
                                    </div>
                                </div>
                                <SkeletonButton width="w-8" height="h-8" />
                            </div>

                            {/* Subjects Skeleton */}
                            <div className="mb-4">
                                <SkeletonLine
                                    width="w-16"
                                    height="h-4"
                                    className="mb-2"
                                />
                                <div className="flex flex-wrap gap-2">
                                    {Array.from({
                                        length: (index % 3) + 1,
                                    }).map((_, idx) => (
                                        <SkeletonBadge key={idx} />
                                    ))}
                                </div>
                            </div>

                            {/* Schedule Preview Skeleton */}
                            <div className="mb-4">
                                <SkeletonLine
                                    width="w-20"
                                    height="h-4"
                                    className="mb-2"
                                />
                                <div className="space-y-1">
                                    {Array.from({ length: 2 }).map((_, idx) => (
                                        <div
                                            key={idx}
                                            className="flex items-center space-x-2"
                                        >
                                            <SkeletonIcon size="w-3 h-3" />
                                            <SkeletonLine
                                                width="w-16"
                                                height="h-3"
                                            />
                                            <SkeletonIcon size="w-3 h-3" />
                                            <SkeletonLine
                                                width="w-20"
                                                height="h-3"
                                            />
                                            <SkeletonIcon size="w-1 h-1" />
                                            <SkeletonLine
                                                width="w-12"
                                                height="h-3"
                                            />
                                        </div>
                                    ))}
                                </div>
                            </div>

                            {/* Quick Actions Skeleton */}
                            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2 pt-4 border-t border-stroke">
                                <SkeletonButton className="flex-1" />
                                <SkeletonButton className="flex-1" />
                            </div>
                        </SkeletonCard>
                    </motion.div>
                ))}
            </div>

            {/* Quick Stats Skeleton */}
            <SkeletonStatsGrid itemCount={4} />
        </div>
    );
};

export default TeacherClassesSkeleton;
