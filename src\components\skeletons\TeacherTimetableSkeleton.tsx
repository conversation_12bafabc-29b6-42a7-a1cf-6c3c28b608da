"use client";

import React from "react";
import { motion } from "framer-motion";
import {
    SkeletonCard,
    SkeletonLine,
    SkeletonCircle,
    SkeletonButton,
    SkeletonBadge,
    SkeletonIcon,
    SkeletonHeader,
    SkeletonStatsGrid,
} from "./SkeletonCard";

interface TeacherTimetableSkeletonProps {
    itemCount?: number;
}

const TeacherTimetableSkeleton: React.FC<TeacherTimetableSkeletonProps> = ({
    itemCount = 5,
}) => {
    const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
    const periods = Array.from({ length: 8 }, (_, i) => i + 1);

    return (
        <div className="space-y-6">
            {/* Header Skeleton */}
            <SkeletonCard>
                <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                        <SkeletonCircle size="w-12 h-12" />
                        <div>
                            <SkeletonLine
                                width="w-48"
                                height="h-8"
                                className="mb-2"
                            />
                            <SkeletonLine width="w-32" height="h-4" />
                        </div>
                    </div>
                    <div className="flex items-center space-x-4">
                        <SkeletonButton width="w-32" />
                        <SkeletonButton width="w-24" />
                    </div>
                </div>
            </SkeletonCard>

            {/* Stats Cards Skeleton */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 sm:gap-6">
                {Array.from({ length: 4 }).map((_, index) => (
                    <motion.div
                        key={index}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                    >
                        <SkeletonCard>
                            <div className="flex items-center justify-between">
                                <div className="flex-1">
                                    <SkeletonLine
                                        width="w-16"
                                        height="h-4"
                                        className="mb-2"
                                    />
                                    <SkeletonLine
                                        width="w-12"
                                        height="h-8"
                                        className="mb-1"
                                    />
                                    <SkeletonLine width="w-20" height="h-3" />
                                </div>
                                <SkeletonCircle size="w-10 h-10" />
                            </div>
                        </SkeletonCard>
                    </motion.div>
                ))}
            </div>

            {/* Timetable Grid Skeleton */}
            <SkeletonCard>
                <SkeletonLine width="w-32" height="h-6" className="mb-6" />

                {/* Desktop Timetable */}
                <div className="hidden md:block">
                    <div className="overflow-x-auto">
                        <div className="min-w-full">
                            {/* Header Row */}
                            <div className="grid grid-cols-9 gap-2 mb-4">
                                <div className="p-3">
                                    <SkeletonLine width="w-12" height="h-4" />
                                </div>
                                {days.map((_, index) => (
                                    <div
                                        key={index}
                                        className="p-3 text-center"
                                    >
                                        <SkeletonLine
                                            width="w-16"
                                            height="h-4"
                                            className="mx-auto"
                                        />
                                    </div>
                                ))}
                            </div>

                            {/* Timetable Rows */}
                            {periods.map((_, periodIndex) => (
                                <div
                                    key={periodIndex}
                                    className="grid grid-cols-9 gap-2 mb-2"
                                >
                                    <div className="p-3 text-center">
                                        <SkeletonLine
                                            width="w-8"
                                            height="h-4"
                                            className="mx-auto mb-1"
                                        />
                                        <SkeletonLine
                                            width="w-12"
                                            height="h-3"
                                            className="mx-auto"
                                        />
                                    </div>
                                    {days.map((_, dayIndex) => (
                                        <div
                                            key={dayIndex}
                                            className="p-2 border border-stroke rounded-md min-h-[80px]"
                                        >
                                            {(dayIndex + periodIndex) % 3 !==
                                            0 ? (
                                                <div className="animate-pulse">
                                                    <SkeletonLine
                                                        width="w-full"
                                                        height="h-4"
                                                        className="mb-1"
                                                    />
                                                    <SkeletonLine
                                                        width="w-3/4"
                                                        height="h-3"
                                                        className="mb-1"
                                                    />
                                                    <SkeletonBadge />
                                                </div>
                                            ) : (
                                                <div className="h-full flex items-center justify-center">
                                                    <SkeletonLine
                                                        width="w-8"
                                                        height="h-3"
                                                    />
                                                </div>
                                            )}
                                        </div>
                                    ))}
                                </div>
                            ))}
                        </div>
                    </div>
                </div>

                {/* Mobile Timetable */}
                <div className="md:hidden space-y-4">
                    {days.map((_, dayIndex) => (
                        <div
                            key={dayIndex}
                            className="border border-stroke rounded-lg p-4"
                        >
                            <SkeletonLine
                                width="w-20"
                                height="h-5"
                                className="mb-3"
                            />
                            <div className="space-y-3">
                                {Array.from({ length: (dayIndex % 3) + 2 }).map(
                                    (_, periodIndex) => (
                                        <div
                                            key={periodIndex}
                                            className="flex items-center space-x-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-md"
                                        >
                                            <div className="flex-shrink-0">
                                                <SkeletonLine
                                                    width="w-12"
                                                    height="h-4"
                                                    className="mb-1"
                                                />
                                                <SkeletonLine
                                                    width="w-16"
                                                    height="h-3"
                                                />
                                            </div>
                                            <div className="flex-1">
                                                <SkeletonLine
                                                    width="w-3/4"
                                                    height="h-4"
                                                    className="mb-1"
                                                />
                                                <SkeletonLine
                                                    width="w-1/2"
                                                    height="h-3"
                                                    className="mb-2"
                                                />
                                                <SkeletonBadge />
                                            </div>
                                        </div>
                                    )
                                )}
                            </div>
                        </div>
                    ))}
                </div>
            </SkeletonCard>
        </div>
    );
};

export default TeacherTimetableSkeleton;
