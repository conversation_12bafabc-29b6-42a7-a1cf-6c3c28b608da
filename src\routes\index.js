const express = require('express');
const userRoutes = require('./userRoutes');
const schoolRoutes = require('./schoolRoutes');
const classRoutes = require('./classRoutes');
const subjectRoutes = require('./subjectRoutes');
const attendanceRoutes = require('./attendanceRoutes');
const gradeRoutes = require('./gradeRoutes');
const studentRoutes = require('./studentRoutes');
const resourcesRoutes = require('./resourcesRoutes');
const disciplineRoutes = require('./disciplineRoutes');
const announcementRoutes = require('./announcementRoutes');
const subscriptionRoutes = require('./subscriptionRoutes')
const authRoutes = require('./authRoutes')
const paymentRoutes = require('./paymentRoute')
const periodRoutes = require('./PeriodRoutes')
const classScheduleRoutes = require('./classScheduleRoutes')
const examTypeRoutes = require('./examTypeRoutes')
const classLevelRoutes = require('./classLevelRoute')
const invitationRoutes = require('./InvitationRoutes')
const academicYearRoutes = require('./academicYearRoutes')
const schoolResourcesRoutes = require('./schoolResourcesRoutes')
const FeesRoutes = require('./FeesRoutes')
const FeePayment = require('./feePaymountRoutes')
const SettingsRoutes = require('./settingsRoutes');
const CreditRoutes = require('./creditRoutes');
const CreditTransactionRoutes = require('./creditTransactionRoutes');
const schoolSubscriptionRoutes = require('./schoolSubscriptionRoutes');
const creditPurchaseRoutes = require('./creditPurchaseRoutes');
const subscriptionPlanRoutes = require('./subscriptionPlanRoutes');
const staffRoutes = require('./staffRoutes');
const timetableRoutes = require('./timetableRoutes');
const teacherAssignmentRoutes = require('./teacherAssignmentRoutes');
const termRoutes = require('./termRoutes');
const notificationRoutes = require('./notificationRoutes');
const studentDataRoutes = require('./studentDataRoute')
const justificationRoutes = require('./justificationRoutes');
const activityLogRoutes = require('./activityLogRoutes');
const examPeriodRoutes = require('./examPeriodRoutes');
const router = express.Router();

router.use('/user', userRoutes);
router.use('/school', schoolRoutes);
router.use('/class', classRoutes);
router.use('/subject', subjectRoutes);
router.use('/attendance', attendanceRoutes);
router.use('/grades', gradeRoutes);
router.use('/student', studentRoutes);
router.use('/resources', resourcesRoutes);
router.use('/discipline', disciplineRoutes);
router.use('/announcement', announcementRoutes);
router.use('/subscription',subscriptionRoutes);
router.use('/auth',authRoutes);
router.use('/payment',paymentRoutes);
router.use('/periods',periodRoutes);
router.use('/schedule',classScheduleRoutes);
router.use('/exam',examTypeRoutes);
router.use('/class-level',classLevelRoutes);
router.use('/invitation',invitationRoutes);
router.use('/academic-years',academicYearRoutes);
router.use('/school-resources',schoolResourcesRoutes);
router.use('/fees',FeesRoutes)
router.use('/fee-payment',FeePayment)
router.use('/settings',SettingsRoutes)
router.use('/credit', CreditRoutes);
router.use('/credit-transaction', CreditTransactionRoutes);
router.use('/school-subscription', schoolSubscriptionRoutes);
router.use('/credit-purchase', creditPurchaseRoutes);
router.use('/subscription-plans', subscriptionPlanRoutes);
router.use('/staff', staffRoutes);
router.use('/timetable', timetableRoutes);
router.use('/teacher', teacherAssignmentRoutes);
router.use('/terms', termRoutes);
router.use('/notifications', notificationRoutes);
router.use('/studentData', studentDataRoutes)
router.use('/justification', justificationRoutes)
router.use('/activity-log', activityLogRoutes);
router.use('/exam-periods', examPeriodRoutes);

module.exports = router;
