const ClassSchedule = require('../models/ClassSchedule');
const Class = require('../models/Class');
const Subject = require('../models/Subject');
const User = require('../models/User');
const Period = require('../models/Periods');
const ActivityLog = require('../models/ActivityLog');

// Get timetable for a school with filters
const getTimetable = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, teacher_id, day_of_week } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };
    if (class_id) filter.class_id = class_id;
    if (teacher_id) filter.teacher_id = teacher_id;
    if (day_of_week) filter.day_of_week = day_of_week;

    // Get schedule records with populated data
    const scheduleRecords = await ClassSchedule.find(filter)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name name')
      .populate('period_id', 'period_number start_time end_time')
      .sort({ day_of_week: 1, 'period_id.period_number': 1 })
      .lean();

    // Format the response
    const formattedRecords = scheduleRecords.map(record => ({
      _id: record._id,
      class_id: record.class_id?._id,
      class_name: record.class_id?.name || 'Unknown Class',
      subject_id: record.subject_id?._id,
      subject_name: record.subject_id?.name || 'Unknown Subject',
      teacher_id: record.teacher_id?._id,
      teacher_name: record.teacher_id ?
        (record.teacher_id.first_name && record.teacher_id.last_name ?
          `${record.teacher_id.first_name} ${record.teacher_id.last_name}` :
          record.teacher_id.name || 'Unknown Teacher') : 'No Teacher Assigned',
      period_id: record.period_id?._id,
      period_number: record.period_id?.period_number || 0,
      start_time: record.period_id?.start_time || '00:00:00',
      end_time: record.period_id?.end_time || '00:00:00',
      day_of_week: record.day_of_week,
      schedule_type: record.schedule_type
    }));

    res.status(200).json({
      schedule_records: formattedRecords,
      message: 'Timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get timetable organized by days and periods
const getOrganizedTimetable = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { class_id, teacher_id } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build filter object
    const filter = { school_id };
    if (class_id) filter.class_id = class_id;
    if (teacher_id) filter.teacher_id = teacher_id;

    // Get all periods for the school
    const periods = await Period.find({ school_id })
      .sort({ period_number: 1 })
      .lean();

    // Get schedule records
    const scheduleRecords = await ClassSchedule.find(filter)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name name')
      .populate('period_id', 'period_number start_time end_time')
      .lean();

    // Organize by day and period
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const organizedTimetable = {};

    days.forEach(day => {
      organizedTimetable[day] = {};
      periods.forEach(period => {
        organizedTimetable[day][period.period_number] = null;
      });
    });

    // Fill in the schedule data
    scheduleRecords.forEach(record => {
      if (organizedTimetable[record.day_of_week] && record.period_id) {
        organizedTimetable[record.day_of_week][record.period_id.period_number] = {
          _id: record._id,
          class_name: record.class_id?.name || 'Unknown Class',
          subject_name: record.subject_id?.name || 'Unknown Subject',
          teacher_name: record.teacher_id ?
            (record.teacher_id.first_name && record.teacher_id.last_name ?
              `${record.teacher_id.first_name} ${record.teacher_id.last_name}` :
              record.teacher_id.name || 'Unknown Teacher') : 'No Teacher Assigned',
          period_number: record.period_id.period_number,
          start_time: record.period_id.start_time,
          end_time: record.period_id.end_time,
          day_of_week: record.day_of_week,
          schedule_type: record.schedule_type
        };
      }
    });

    res.status(200).json({
      timetable: organizedTimetable,
      periods: periods,
      message: 'Organized timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching organized timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get teacher's personal timetable
const getTeacherTimetable = async (req, res) => {
  try {
    const { school_id, teacher_id } = req.params;
    const { academic_year } = req.query;

    if (!school_id || !teacher_id) {
      return res.status(400).json({ message: 'School ID and Teacher ID are required' });
    }

    // Get all periods for the school
    const periods = await Period.find({ school_id })
      .sort({ period_number: 1 })
      .lean();

    // Build query with optional academic year filter
    const query = {
      school_id,
      teacher_id,
      schedule_type: { $ne: 'Exam' } // Exclude exam supervisions from teacher dashboard
    };

    if (academic_year) {
      query.academic_year = academic_year;
    }

    // Get teacher's schedule records (exclude exam supervisions)
    const scheduleRecords = await ClassSchedule.find(query)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('period_id', 'period_number start_time end_time')
      .lean();

    // Get student count for each class
    const Student = require('../models/Student');
    const classIds = [...new Set(scheduleRecords.map(record => record.class_id?._id).filter(Boolean))];
    
    const studentCounts = {};
    for (const classId of classIds) {
      const count = await Student.countDocuments({ class_id: classId, school_id });
      studentCounts[classId.toString()] = count;
    }

    // Organize by day and period
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    const organizedTimetable = {};

    days.forEach(day => {
      organizedTimetable[day] = {};
      periods.forEach(period => {
        organizedTimetable[day][period.period_number] = null;
      });
    });

    // Fill in the schedule data
    scheduleRecords.forEach(record => {
      if (organizedTimetable[record.day_of_week] && record.period_id) {
        organizedTimetable[record.day_of_week][record.period_id.period_number] = {
          _id: record._id,
          class_name: record.class_id?.name || 'Unknown Class',
          subject_name: record.subject_id?.name || 'Unknown Subject',
          period_number: record.period_id.period_number,
          start_time: record.period_id.start_time,
          end_time: record.period_id.end_time,
          day_of_week: record.day_of_week,
          student_count: studentCounts[record.class_id?._id?.toString()] || 0,
          schedule_type: record.schedule_type
        };
      }
    });

    res.status(200).json({
      timetable: organizedTimetable,
      periods: periods,
      teacher_stats: {
        total_classes: scheduleRecords.length,
        different_classes: new Set(scheduleRecords.map(r => r.class_id?._id)).size,
        total_students: Object.values(studentCounts).reduce((sum, count) => sum + count, 0),
        subjects: [...new Set(scheduleRecords.map(r => r.subject_id?.name).filter(Boolean))]
      },
      message: 'Teacher timetable retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching teacher timetable:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get teacher's exam supervisions
const getTeacherSupervisions = async (req, res) => {
  try {
    const { school_id, teacher_id } = req.params;
    const { academic_year } = req.query;

    if (!school_id || !teacher_id) {
      return res.status(400).json({ message: 'School ID and Teacher ID are required' });
    }

    // Build query with optional academic year filter
    const query = {
      school_id,
      teacher_id,
      schedule_type: 'Exam' // Only exam supervisions
    };

    if (academic_year) {
      query.academic_year = academic_year;
    }

    // Get teacher's exam supervision records only
    const supervisionRecords = await ClassSchedule.find(query)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('period_id', 'period_number start_time end_time')
      .sort({ day_of_week: 1, 'period_id.period_number': 1 })
      .lean();

    // Format the supervision records
    const formattedSupervisions = supervisionRecords.map(record => ({
      _id: record._id,
      class_name: record.class_id?.name || 'Unknown Class',
      subject_name: record.subject_id?.name || 'Unknown Subject',
      period_number: record.period_id?.period_number || 0,
      start_time: record.period_id?.start_time || '',
      end_time: record.period_id?.end_time || '',
      day_of_week: record.day_of_week,
      schedule_type: record.schedule_type,
      supervision_date: record.createdAt || new Date()
    }));

    res.status(200).json({
      supervisions: formattedSupervisions,
      total: formattedSupervisions.length,
      message: 'Teacher supervisions retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching teacher supervisions:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new schedule entry
const createScheduleEntry = async (req, res) => {
  try {
    const { school_id } = req.params;
    const {
      class_id,
      subject_id,
      teacher_id,
      period_id,
      day_of_week,
      schedule_type = 'Normal',
      exam_period_id,
      academic_year,
      session_year,
      specific_date,
      notes
    } = req.body;

    if (!school_id || !class_id || !subject_id || !period_id || !day_of_week) {
      return res.status(400).json({
        message: 'School ID, class ID, subject ID, period ID, and day of week are required'
      });
    }

    // Validate exam period for Exam schedule type
    if (schedule_type === 'Exam' && !exam_period_id) {
      return res.status(400).json({
        message: 'Exam period ID is required for Exam schedule type'
      });
    }

    // Validate academic year and session year
    if (!academic_year) {
      return res.status(400).json({
        message: 'Academic year is required'
      });
    }

    if (!session_year) {
      return res.status(400).json({
        message: 'Session year is required'
      });
    }

    // Check for conflicts with priority handling
    const conflictCheck = await ClassSchedule.checkConflictWithPriority(
      school_id,
      class_id,
      period_id,
      day_of_week,
      schedule_type
    );

    if (conflictCheck.hasConflict) {
      const existingSchedule = conflictCheck.existingSchedule;
      const existingType = existingSchedule.schedule_type || 'Normal';
      const currentType = schedule_type || 'Normal';

      // Since we only check for conflicts with the same schedule type,
      // conflicts are always same_type conflicts
      if (conflictCheck.canReplace) {
        return res.status(400).json({
          available: false,
          conflict: true,
          conflictType: 'replaceable',
          canReplace: true,
          message: `A ${currentType.toLowerCase()} schedule already exists for this class, period, and day, but your new ${currentType.toLowerCase()} schedule has higher priority and can replace it.`,
          conflictDetails: {
            class_name: existingSchedule.class_id?.name || 'Unknown Class',
            subject_name: existingSchedule.subject_id?.name || 'Unknown Subject',
            teacher_name: existingSchedule.teacher_id?.first_name && existingSchedule.teacher_id?.last_name
              ? `${existingSchedule.teacher_id.first_name} ${existingSchedule.teacher_id.last_name}`
              : existingSchedule.teacher_id?.name || 'Unknown Teacher',
            period_info: existingSchedule.period_id ? {
              period_number: existingSchedule.period_id.period_number,
              start_time: existingSchedule.period_id.start_time,
              end_time: existingSchedule.period_id.end_time
            } : null,
            day_of_week: existingSchedule.day_of_week,
            existing_type: existingType,
            current_type: currentType,
            existing_priority: existingSchedule.priority,
            current_priority: schedule_type === 'Exam' ? 100 : 50,
            suggestion: `Click "Replace" to replace the existing ${currentType.toLowerCase()} schedule with your new ${currentType.toLowerCase()} schedule.`
          }
        });
      } else {
        // Cannot replace - existing has higher or equal priority
        return res.status(400).json({
          available: false,
          conflict: true,
          conflictType: 'same_type',
          canReplace: false,
          message: `A ${currentType.toLowerCase()} schedule already exists for this class, period, and day`,
          conflictDetails: {
            class_name: existingSchedule.class_id?.name || 'Unknown Class',
            subject_name: existingSchedule.subject_id?.name || 'Unknown Subject',
            teacher_name: existingSchedule.teacher_id?.first_name && existingSchedule.teacher_id?.last_name
              ? `${existingSchedule.teacher_id.first_name} ${existingSchedule.teacher_id.last_name}`
              : existingSchedule.teacher_id?.name || 'Unknown Teacher',
            period_info: existingSchedule.period_id ? {
              period_number: existingSchedule.period_id.period_number,
              start_time: existingSchedule.period_id.start_time,
              end_time: existingSchedule.period_id.end_time
            } : null,
            day_of_week: existingSchedule.day_of_week,
            existing_type: existingType,
            current_type: currentType,
            suggestion: `Consider choosing a different time period or day for this ${currentType.toLowerCase()} schedule, or modify the existing one.`
          }
        });
      }
    }

    // Check if teacher is available at this time (if teacher is assigned)
    // Only check for conflicts with the same schedule type
    if (teacher_id) {
      const teacherConflict = await ClassSchedule.findOne({
        school_id,
        teacher_id,
        period_id,
        day_of_week,
        schedule_type // Only check for conflicts with the same schedule type
      })
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name name')
      .populate('period_id', 'period_number start_time end_time');

      if (teacherConflict) {
        const teacherName = teacherConflict.teacher_id?.first_name && teacherConflict.teacher_id?.last_name
          ? `${teacherConflict.teacher_id.first_name} ${teacherConflict.teacher_id.last_name}`
          : teacherConflict.teacher_id?.name || 'Unknown Teacher';

        const conflictType = teacherConflict.schedule_type === 'Exam' ? 'exam supervision' : 'class';
        const currentType = schedule_type === 'Exam' ? 'exam supervision' : 'class';

        return res.status(400).json({
          available: false,
          conflict: true,
          message: `${teacherName} is already scheduled for ${conflictType} during this time slot. Please choose a different teacher or time period.`,
          conflictDetails: {
            teacher_name: teacherName,
            class_name: teacherConflict.class_id?.name || 'Unknown Class',
            subject_name: teacherConflict.subject_id?.name || 'Unknown Subject',
            period_info: teacherConflict.period_id ? {
              period_number: teacherConflict.period_id.period_number,
              start_time: teacherConflict.period_id.start_time,
              end_time: teacherConflict.period_id.end_time
            } : null,
            day_of_week: teacherConflict.day_of_week,
            conflict_type: conflictType,
            current_type: currentType
          }
        });
      }
    }

    const newSchedule = new ClassSchedule({
      school_id,
      class_id,
      subject_id,
      teacher_id,
      period_id,
      day_of_week,
      schedule_type,
      exam_period_id: schedule_type === 'Exam' ? exam_period_id : undefined,
      academic_year,
      session_year,
      specific_date: specific_date ? new Date(specific_date) : undefined,
      notes
    });

    // If there was a replaceable conflict, handle the replacement
    if (conflictCheck.hasConflict && conflictCheck.canReplace) {
      console.log(`🔄 Replacing lower priority ${schedule_type} schedule with higher priority ${schedule_type} schedule`);

      // Since we only check for conflicts with the same schedule type,
      // we can safely delete the existing schedule
      const existingSchedule = conflictCheck.existingSchedule;
      await ClassSchedule.findByIdAndDelete(existingSchedule._id);
      console.log(`🗑️ Deleted lower priority ${schedule_type} schedule: ${existingSchedule._id}`);
    }

    await newSchedule.save();

    // Sync teacher assignments if teacher is assigned
    if (teacher_id) {
      try {
        const { syncTeacherAssignments } = require('../utils/teacherAssignmentSync');
        await syncTeacherAssignments(teacher_id, school_id);
      } catch (syncError) {
        console.error('Error syncing teacher assignments:', syncError);
        // Don't fail the main operation if sync fails
      }
    }

    // Populate the created schedule for response
    const populatedSchedule = await ClassSchedule.findById(newSchedule._id)
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name name')
      .populate('period_id', 'period_number start_time end_time');

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: schedule_type === 'Exam' ? 'exam_created' : 'schedule_created',
      target_type: 'class',
      target_id: class_id,
      target_name: populatedSchedule.class_id?.name,
      details: {
        subject: populatedSchedule.subject_id?.name,
        teacher: populatedSchedule.teacher_id ?
          (populatedSchedule.teacher_id.first_name && populatedSchedule.teacher_id.last_name ?
            `${populatedSchedule.teacher_id.first_name} ${populatedSchedule.teacher_id.last_name}` :
            populatedSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
        period: populatedSchedule.period_id?.period_number,
        day: day_of_week,
        schedule_type: schedule_type
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // Send notification for exam supervision assignment
    if (schedule_type === 'Exam' && teacher_id) {
      try {
        const NotificationService = require('../services/notificationService');
        await NotificationService.notifyExamSupervision(
          teacher_id,
          {
            schedule_id: newSchedule._id,
            subject_name: populatedSchedule.subject_id?.name,
            class_name: populatedSchedule.class_id?.name,
            day_of_week: day_of_week,
            period: populatedSchedule.period_id
          },
          school_id,
          req.user.id
        );

        // Log specific activity for exam supervisor assignment
        await ActivityLog.logActivity({
          user_id: req.user.id,
          school_id: school_id,
          action: 'exam_supervisor_assigned',
          target_type: 'schedule',
          target_id: newSchedule._id,
          target_name: `${populatedSchedule.subject_id?.name} - ${populatedSchedule.class_id?.name}`,
          details: {
            supervisor: populatedSchedule.teacher_id ?
              (populatedSchedule.teacher_id.first_name && populatedSchedule.teacher_id.last_name ?
                `${populatedSchedule.teacher_id.first_name} ${populatedSchedule.teacher_id.last_name}` :
                populatedSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
            subject: populatedSchedule.subject_id?.name,
            class: populatedSchedule.class_id?.name,
            day: day_of_week,
            period: populatedSchedule.period_id?.period_number
          },
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        });

        console.log('✅ Exam supervision notification sent successfully to teacher:', teacher_id);
      } catch (notificationError) {
        console.error('Error sending exam supervision notification:', notificationError);
        // Don't fail the main operation if notification fails
      }
    }

    res.status(201).json({
      schedule: populatedSchedule,
      message: 'Schedule entry created successfully'
    });
  } catch (error) {
    console.error('Error creating schedule entry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get timetable statistics
const getTimetableStats = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Get total periods
    const totalPeriods = await Period.countDocuments({ school_id });

    // Get total schedule entries
    const totalScheduleEntries = await ClassSchedule.countDocuments({ school_id });

    // Get unique teachers
    const uniqueTeachers = await ClassSchedule.distinct('teacher_id', { school_id });

    // Get unique subjects
    const uniqueSubjects = await ClassSchedule.distinct('subject_id', { school_id });

    // Get unique classes
    const uniqueClasses = await ClassSchedule.distinct('class_id', { school_id });

    // Calculate free periods (total possible slots - used slots)
    const totalPossibleSlots = totalPeriods * 5; // 5 days a week
    const usedSlots = totalScheduleEntries;
    const freeSlots = totalPossibleSlots - usedSlots;

    res.status(200).json({
      stats: {
        total_periods: totalPeriods,
        total_schedule_entries: totalScheduleEntries,
        unique_teachers: uniqueTeachers.filter(id => id).length,
        unique_subjects: uniqueSubjects.length,
        unique_classes: uniqueClasses.length,
        free_slots: freeSlots,
        utilization_rate: totalPossibleSlots > 0 ? 
          Math.round((usedSlots / totalPossibleSlots) * 100) : 0
      },
      message: 'Timetable statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching timetable stats:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update a schedule entry
const updateScheduleEntry = async (req, res) => {
  try {
    const { school_id, schedule_id } = req.params;
    const {
      class_id,
      subject_id,
      teacher_id,
      period_id,
      day_of_week,
      schedule_type = 'Normal'
    } = req.body;

    if (!school_id || !schedule_id) {
      return res.status(400).json({
        message: 'School ID and Schedule ID are required'
      });
    }

    // Find the existing schedule entry
    const existingSchedule = await ClassSchedule.findOne({
      _id: schedule_id,
      school_id
    });

    if (!existingSchedule) {
      return res.status(404).json({
        message: 'Schedule entry not found'
      });
    }

    // Check if the new schedule conflicts with existing ones (excluding current entry)
    // Only check for conflicts with the same schedule type
    if (class_id && period_id && day_of_week) {
      const conflictingSchedule = await ClassSchedule.findOne({
        school_id,
        class_id,
        period_id,
        day_of_week,
        schedule_type: schedule_type || existingSchedule.schedule_type, // Check same schedule type
        _id: { $ne: schedule_id }
      });

      if (conflictingSchedule) {
        return res.status(400).json({
          message: `Schedule already exists for this class, period, and day with the same schedule type (${schedule_type || existingSchedule.schedule_type})`
        });
      }
    }

    // Check if teacher is available at this time (if teacher is being changed)
    // Only check for conflicts with the same schedule type
    if (teacher_id && period_id && day_of_week) {
      const teacherConflict = await ClassSchedule.findOne({
        school_id,
        teacher_id,
        period_id,
        day_of_week,
        schedule_type: schedule_type || existingSchedule.schedule_type, // Check same schedule type
        _id: { $ne: schedule_id }
      })
      .populate('class_id', 'name grade_level')
      .populate('subject_id', 'name')
      .populate('teacher_id', 'first_name last_name name')
      .populate('period_id', 'period_number start_time end_time');

      if (teacherConflict) {
        const teacherName = teacherConflict.teacher_id?.first_name && teacherConflict.teacher_id?.last_name
          ? `${teacherConflict.teacher_id.first_name} ${teacherConflict.teacher_id.last_name}`
          : teacherConflict.teacher_id?.name || 'Unknown Teacher';

        const conflictType = teacherConflict.schedule_type === 'Exam' ? 'exam supervision' : 'class';
        const currentType = (schedule_type || existingSchedule.schedule_type) === 'Exam' ? 'exam supervision' : 'class';

        return res.status(400).json({
          available: false,
          conflict: true,
          message: `${teacherName} is already scheduled for ${conflictType} during this time slot. Please choose a different teacher or time period.`,
          conflictDetails: {
            teacher_name: teacherName,
            class_name: teacherConflict.class_id?.name || 'Unknown Class',
            subject_name: teacherConflict.subject_id?.name || 'Unknown Subject',
            period_info: teacherConflict.period_id ? {
              period_number: teacherConflict.period_id.period_number,
              start_time: teacherConflict.period_id.start_time,
              end_time: teacherConflict.period_id.end_time
            } : null,
            day_of_week: teacherConflict.day_of_week,
            conflict_type: conflictType,
            current_type: currentType
          }
        });
      }
    }

    // Update the schedule entry
    const updateData = {};
    if (class_id) updateData.class_id = class_id;
    if (subject_id) updateData.subject_id = subject_id;
    if (teacher_id) updateData.teacher_id = teacher_id;
    if (period_id) updateData.period_id = period_id;
    if (day_of_week) updateData.day_of_week = day_of_week;
    if (schedule_type) updateData.schedule_type = schedule_type;

    const updatedSchedule = await ClassSchedule.findByIdAndUpdate(
      schedule_id,
      updateData,
      { new: true }
    ).populate('class_id', 'name grade_level')
     .populate('subject_id', 'name')
     .populate('teacher_id', 'first_name last_name name')
     .populate('period_id', 'period_number start_time end_time');

    // Sync teacher assignments if teacher was updated
    if (teacher_id || existingSchedule.teacher_id) {
      try {
        const { syncTeacherAssignments } = require('../utils/teacherAssignmentSync');

        // Sync for the new teacher if changed
        if (teacher_id && teacher_id !== existingSchedule.teacher_id?.toString()) {
          await syncTeacherAssignments(teacher_id, school_id);
        }

        // Sync for the old teacher if teacher was removed or changed
        if (existingSchedule.teacher_id &&
            (!teacher_id || teacher_id !== existingSchedule.teacher_id.toString())) {
          await syncTeacherAssignments(existingSchedule.teacher_id, school_id);
        }
      } catch (syncError) {
        console.error('Error syncing teacher assignments:', syncError);
        // Don't fail the main operation if sync fails
      }
    }

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: updatedSchedule.schedule_type === 'Exam' ? 'exam_updated' : 'schedule_updated',
      target_type: 'class',
      target_id: updatedSchedule.class_id._id,
      target_name: updatedSchedule.class_id?.name,
      details: {
        subject: updatedSchedule.subject_id?.name,
        teacher: updatedSchedule.teacher_id ?
          (updatedSchedule.teacher_id.first_name && updatedSchedule.teacher_id.last_name ?
            `${updatedSchedule.teacher_id.first_name} ${updatedSchedule.teacher_id.last_name}` :
            updatedSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
        period: updatedSchedule.period_id?.period_number,
        day: updatedSchedule.day_of_week,
        schedule_type: updatedSchedule.schedule_type
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    // Send notification for exam supervision assignment (if teacher was changed and it's an exam)
    if (updatedSchedule.schedule_type === 'Exam' && teacher_id && teacher_id !== existingSchedule.teacher_id?.toString()) {
      try {
        const NotificationService = require('../services/notificationService');
        await NotificationService.notifyExamSupervision(
          teacher_id,
          {
            schedule_id: updatedSchedule._id,
            subject_name: updatedSchedule.subject_id?.name,
            class_name: updatedSchedule.class_id?.name,
            day_of_week: updatedSchedule.day_of_week,
            period: updatedSchedule.period_id
          },
          school_id,
          req.user.id
        );

        // Log specific activity for exam supervisor assignment change
        await ActivityLog.logActivity({
          user_id: req.user.id,
          school_id: school_id,
          action: 'exam_supervisor_assigned',
          target_type: 'schedule',
          target_id: updatedSchedule._id,
          target_name: `${updatedSchedule.subject_id?.name} - ${updatedSchedule.class_id?.name}`,
          details: {
            supervisor: updatedSchedule.teacher_id ?
              (updatedSchedule.teacher_id.first_name && updatedSchedule.teacher_id.last_name ?
                `${updatedSchedule.teacher_id.first_name} ${updatedSchedule.teacher_id.last_name}` :
                updatedSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
            previous_supervisor: existingSchedule.teacher_id ?
              (existingSchedule.teacher_id.first_name && existingSchedule.teacher_id.last_name ?
                `${existingSchedule.teacher_id.first_name} ${existingSchedule.teacher_id.last_name}` :
                existingSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
            subject: updatedSchedule.subject_id?.name,
            class: updatedSchedule.class_id?.name,
            day: updatedSchedule.day_of_week,
            period: updatedSchedule.period_id?.period_number
          },
          ip_address: req.ip,
          user_agent: req.get('User-Agent')
        });
      } catch (notificationError) {
        console.error('Error sending exam supervision notification:', notificationError);
        // Don't fail the main operation if notification fails
      }
    }

    res.status(200).json({
      schedule: {
        _id: updatedSchedule._id,
        class_name: updatedSchedule.class_id?.name || 'Unknown Class',
        subject_name: updatedSchedule.subject_id?.name || 'Unknown Subject',
        teacher_name: updatedSchedule.teacher_id ?
          (updatedSchedule.teacher_id.first_name && updatedSchedule.teacher_id.last_name ?
            `${updatedSchedule.teacher_id.first_name} ${updatedSchedule.teacher_id.last_name}` :
            updatedSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
        period_number: updatedSchedule.period_id?.period_number || 0,
        start_time: updatedSchedule.period_id?.start_time || '',
        end_time: updatedSchedule.period_id?.end_time || '',
        day_of_week: updatedSchedule.day_of_week,
        schedule_type: updatedSchedule.schedule_type
      },
      message: 'Schedule entry updated successfully'
    });
  } catch (error) {
    console.error('Error updating schedule entry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete a schedule entry
const deleteScheduleEntry = async (req, res) => {
  try {
    const { school_id, schedule_id } = req.params;

    if (!school_id || !schedule_id) {
      return res.status(400).json({
        message: 'School ID and Schedule ID are required'
      });
    }

    // Find the existing schedule entry
    const existingSchedule = await ClassSchedule.findOne({
      _id: schedule_id,
      school_id
    }).populate('class_id', 'name')
     .populate('subject_id', 'name')
     .populate('teacher_id', 'first_name last_name name');

    if (!existingSchedule) {
      return res.status(404).json({
        message: 'Schedule entry not found'
      });
    }

    // Delete the schedule entry
    await ClassSchedule.findByIdAndDelete(schedule_id);

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: school_id,
      action: existingSchedule.schedule_type === 'Exam' ? 'exam_deleted' : 'schedule_deleted',
      target_type: 'class',
      target_id: existingSchedule.class_id._id,
      target_name: existingSchedule.class_id?.name,
      details: {
        subject: existingSchedule.subject_id?.name,
        teacher: existingSchedule.teacher_id ?
          (existingSchedule.teacher_id.first_name && existingSchedule.teacher_id.last_name ?
            `${existingSchedule.teacher_id.first_name} ${existingSchedule.teacher_id.last_name}` :
            existingSchedule.teacher_id.name || 'Unknown Teacher') : 'No Teacher',
        day: existingSchedule.day_of_week,
        schedule_type: existingSchedule.schedule_type
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    res.json({
      message: 'Schedule entry deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting schedule entry:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Test endpoint for ActivityLog debugging
const testActivityLog = async (req, res) => {
  try {
    console.log('🧪 Testing ActivityLog functionality...');

    // Test 1: Create a simple log entry
    const testLog = await ActivityLog.logActivity({
      user_id: req.user.id,
      school_id: req.body.school_id || req.user.school_id,
      action: 'schedule_created',
      target_type: 'class',
      target_id: 'test-class-id',
      target_name: 'Test Class',
      details: {
        subject: 'Test Subject',
        teacher: 'Test Teacher',
        day: 'Monday',
        test: true
      },
      ip_address: req.ip,
      user_agent: req.get('User-Agent')
    });

    console.log('✅ ActivityLog test result:', testLog ? 'SUCCESS' : 'FAILED');

    if (testLog) {
      console.log('📝 Created log:', {
        id: testLog._id,
        log_id: testLog.log_id,
        action: testLog.action
      });
    }

    // Test 2: Count total logs
    const totalLogs = await ActivityLog.countDocuments();
    console.log('📊 Total ActivityLogs in database:', totalLogs);

    // Test 3: Get recent logs
    const recentLogs = await ActivityLog.getRecentActivities({}, 5);
    console.log('📊 Recent logs count:', recentLogs.length);

    res.json({
      success: testLog !== null,
      log_created: testLog ? {
        id: testLog._id,
        log_id: testLog.log_id,
        action: testLog.action,
        created_at: testLog.createdAt
      } : null,
      total_logs: totalLogs,
      recent_logs_count: recentLogs.length,
      message: testLog ? 'ActivityLog test successful' : 'ActivityLog test failed'
    });

  } catch (error) {
    console.error('❌ ActivityLog test error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack,
      message: 'ActivityLog test failed with error'
    });
  }
};

// Test endpoint for Notification debugging
const testNotification = async (req, res) => {
  try {
    console.log('🧪 Testing Notification functionality...');

    const { teacher_id, school_id } = req.body;
    const targetTeacherId = teacher_id || req.user.id;
    const targetSchoolId = school_id || req.user.school_id;

    console.log('📝 Creating test exam supervision notification for:', {
      teacher_id: targetTeacherId,
      school_id: targetSchoolId,
      creator: req.user.id
    });

    // Test 1: Create a test exam supervision notification
    const NotificationService = require('../services/notificationService');
    const testNotification = await NotificationService.notifyExamSupervision(
      targetTeacherId,
      {
        schedule_id: 'test-schedule-id',
        subject_name: 'Test Mathematics',
        class_name: 'Test Class 6A',
        day_of_week: 'Monday',
        period: {
          start_time: '09:00',
          end_time: '10:00',
          period_number: 2
        }
      },
      targetSchoolId,
      req.user.id
    );

    console.log('✅ Test notification result:', testNotification ? 'SUCCESS' : 'FAILED');

    if (testNotification) {
      console.log('📝 Created notification:', {
        id: testNotification._id,
        notification_id: testNotification.notification_id,
        title: testNotification.title,
        recipient_id: testNotification.recipient_id
      });
    }

    // Test 2: Count total notifications for this user
    const Notification = require('../models/Notification');
    const userNotifications = await Notification.find({ recipient_id: targetTeacherId });
    console.log('📊 Total notifications for user:', userNotifications.length);

    // Test 3: Get recent notifications for this user
    const recentNotifications = await Notification.find({ recipient_id: targetTeacherId })
      .sort({ createdAt: -1 })
      .limit(5);
    console.log('📊 Recent notifications:', recentNotifications.map(n => ({
      id: n.notification_id,
      title: n.title,
      category: n.category,
      read: n.read
    })));

    res.json({
      success: testNotification !== null,
      notification_created: testNotification ? {
        id: testNotification._id,
        notification_id: testNotification.notification_id,
        title: testNotification.title,
        category: testNotification.category,
        recipient_id: testNotification.recipient_id,
        created_at: testNotification.createdAt
      } : null,
      user_notifications_count: userNotifications.length,
      recent_notifications: recentNotifications.map(n => ({
        id: n.notification_id,
        title: n.title,
        category: n.category,
        read: n.read,
        created_at: n.createdAt
      })),
      message: testNotification ? 'Notification test successful' : 'Notification test failed'
    });

  } catch (error) {
    console.error('❌ Notification test error:', error);
    res.status(500).json({
      success: false,
      error: error.message,
      stack: error.stack,
      message: 'Notification test failed with error'
    });
  }
};

// Get class statistics for dashboard
const getClassStatistics = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { academic_year, session_year, schedule_type } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    // Build query
    const query = { school_id };
    if (academic_year) query.academic_year = academic_year;
    if (session_year) query.session_year = session_year;
    if (schedule_type) query.schedule_type = schedule_type;

    // Get all classes
    const Class = require('../models/Class');
    const classes = await Class.find({ school_id }).lean();

    // Get all periods
    const Period = require('../models/Periods');
    const periods = await Period.find({ school_id }).lean();

    // Get all schedules
    const schedules = await ClassSchedule.find(query)
      .populate('class_id', 'name grade_level student_count section')
      .populate('teacher_id', 'first_name last_name name')
      .populate('subject_id', 'name')
      .lean();

    // Get teacher assignments
    const TeacherAssignment = require('../models/TeacherAssignment');
    const assignments = await TeacherAssignment.find({ school_id })
      .populate('teacher_id', 'first_name last_name name')
      .lean();

    // Calculate statistics for each class
    const classStats = classes.map(classData => {
      // Total possible slots (5 days * number of periods)
      const totalSlots = 5 * periods.length;

      // Get schedules for this class
      const classSchedules = schedules.filter(schedule =>
        schedule.class_id && schedule.class_id._id.toString() === classData._id.toString()
      );

      // Count occupied slots
      const occupiedSlots = classSchedules.length;
      const freeSlots = totalSlots - occupiedSlots;

      // Count exam slots if in exam mode
      const examSlots = classSchedules.filter(schedule =>
        schedule.schedule_type === 'Exam'
      ).length;

      // Get unique teachers assigned to this class
      const classAssignments = assignments.filter(assignment =>
        assignment.class_id && assignment.class_id.toString() === classData._id.toString()
      );

      const assignedTeachers = new Set(
        classAssignments.map(assignment => assignment.teacher_id?._id?.toString())
      ).size;

      // Get unique subjects for this class
      const totalSubjects = new Set(
        classAssignments.map(assignment => assignment.subjects?.[0]?.toString() || assignment.subject_id?.toString())
      ).size;

      return {
        classData: {
          _id: classData._id,
          name: classData.name,
          grade_level: classData.grade_level,
          student_count: classData.student_count,
          section: classData.section
        },
        stats: {
          totalSlots,
          occupiedSlots,
          freeSlots,
          assignedTeachers,
          totalSubjects,
          examSlots: schedule_type === 'Exam' ? examSlots : undefined
        }
      };
    });

    res.status(200).json({
      classStatistics: classStats,
      totalClasses: classes.length,
      message: 'Class statistics retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching class statistics:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  getTimetable,
  getOrganizedTimetable,
  getTeacherTimetable,
  getTeacherSupervisions,
  createScheduleEntry,
  updateScheduleEntry,
  deleteScheduleEntry,
  getTimetableStats,
  getClassStatistics,
  testActivityLog,
  testNotification
};
