"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Play, CheckCircle, XCircle, RefreshCw } from 'lucide-react';
import { TeacherAssignmentTests } from '@/app/services/TeacherAssignmentTestService';

interface TeacherAssignmentDebugProps {
  schoolId: string;
  teacherId?: string;
}

interface TestResult {
  success: boolean;
  data?: any;
  error?: string;
  summary?: any;
}

const TeacherAssignmentDebug: React.FC<TeacherAssignmentDebugProps> = ({ 
  schoolId, 
  teacherId 
}) => {
  const [isRunning, setIsRunning] = useState(false);
  const [results, setResults] = useState<{
    connectivity?: TestResult;
    assignments?: TestResult;
    sync?: TestResult;
  }>({});

  const runAllTests = async () => {
    setIsRunning(true);
    setResults({});

    try {
      // Test 1: API Connectivity
      console.log("🚀 Starting Teacher Assignment Debug Tests...");
      const connectivityResult = await TeacherAssignmentTests.testAPIConnectivity();
      setResults(prev => ({ ...prev, connectivity: connectivityResult }));

      // Test 2: Teacher Assignments
      const assignmentsResult = await TeacherAssignmentTests.testTeacherAssignments(schoolId);
      setResults(prev => ({ ...prev, assignments: assignmentsResult }));

      // Test 3: Sync (if teacherId provided and user has admin rights)
      if (teacherId) {
        const syncResult = await TeacherAssignmentTests.testSyncTeacherAssignments(schoolId, teacherId);
        setResults(prev => ({ ...prev, sync: syncResult }));
      }

      console.log("✅ All tests completed!");

    } catch (error) {
      console.error("❌ Test suite failed:", error);
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (result?: TestResult) => {
    if (!result) return <RefreshCw className="h-4 w-4 text-gray-400" />;
    return result.success ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusColor = (result?: TestResult) => {
    if (!result) return "border-gray-200 bg-gray-50";
    return result.success ? 
      "border-green-200 bg-green-50" : 
      "border-red-200 bg-red-50";
  };

  return (
    <div className="bg-widget rounded-lg border border-stroke p-6 mb-6">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-foreground">
            🔧 Teacher Assignment Debug
          </h3>
          <p className="text-sm text-foreground/60">
            Test teacher assignment functionality and data flow
          </p>
        </div>
        
        <motion.button
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
          onClick={runAllTests}
          disabled={isRunning}
          className="flex items-center space-x-2 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 disabled:opacity-50"
        >
          {isRunning ? (
            <RefreshCw className="h-4 w-4 animate-spin" />
          ) : (
            <Play className="h-4 w-4" />
          )}
          <span>{isRunning ? 'Running Tests...' : 'Run Tests'}</span>
        </motion.button>
      </div>

      {/* Test Parameters */}
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
        <h4 className="text-sm font-medium text-foreground mb-2">Test Parameters:</h4>
        <div className="text-xs text-foreground/70 space-y-1">
          <div>School ID: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{schoolId}</code></div>
          {teacherId && (
            <div>Teacher ID: <code className="bg-gray-200 dark:bg-gray-700 px-1 rounded">{teacherId}</code></div>
          )}
        </div>
      </div>

      {/* Test Results */}
      <div className="space-y-3">
        {/* API Connectivity Test */}
        <div className={`p-3 border rounded-md ${getStatusColor(results.connectivity)}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon(results.connectivity)}
              <span className="font-medium">API Connectivity</span>
            </div>
            {results.connectivity && (
              <span className="text-xs text-foreground/60">
                {results.connectivity.success ? '✅ Connected' : '❌ Failed'}
              </span>
            )}
          </div>
          {results.connectivity?.error && (
            <div className="mt-2 text-xs text-red-600">
              Error: {results.connectivity.error}
            </div>
          )}
        </div>

        {/* Teacher Assignments Test */}
        <div className={`p-3 border rounded-md ${getStatusColor(results.assignments)}`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {getStatusIcon(results.assignments)}
              <span className="font-medium">Teacher Assignments</span>
            </div>
            {results.assignments?.summary && (
              <div className="text-xs text-foreground/60">
                Classes: {results.assignments.summary.assignedClasses} | 
                Students: {results.assignments.summary.students}
              </div>
            )}
          </div>
          {results.assignments?.error && (
            <div className="mt-2 text-xs text-red-600">
              Error: {results.assignments.error}
            </div>
          )}
          {results.assignments?.summary && (
            <div className="mt-2 text-xs text-foreground/70 space-y-1">
              <div>📚 Assigned Classes: {results.assignments.summary.assignedClasses}</div>
              <div>📖 Assigned Subjects: {results.assignments.summary.assignedSubjects}</div>
              <div>👥 Students: {results.assignments.summary.students}</div>
              <div>📅 Schedule Entries: {results.assignments.summary.scheduleEntries}</div>
              <div>🔐 Permissions: {results.assignments.summary.hasPermissions ? '✅' : '❌'}</div>
            </div>
          )}
        </div>

        {/* Sync Test (if applicable) */}
        {teacherId && (
          <div className={`p-3 border rounded-md ${getStatusColor(results.sync)}`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getStatusIcon(results.sync)}
                <span className="font-medium">Assignment Sync</span>
              </div>
              {results.sync && (
                <span className="text-xs text-foreground/60">
                  {results.sync.success ? '✅ Synced' : '❌ Failed'}
                </span>
              )}
            </div>
            {results.sync?.error && (
              <div className="mt-2 text-xs text-red-600">
                Error: {results.sync.error}
              </div>
            )}
            {results.sync?.data && (
              <div className="mt-2 text-xs text-foreground/70">
                {results.sync.data.message}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Instructions */}
      <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-md">
        <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
          🔍 Debug Instructions:
        </h4>
        <div className="text-xs text-blue-700 dark:text-blue-300 space-y-1">
          <div>1. Click "Run Tests" to check if teacher assignments are working</div>
          <div>2. Check the browser console for detailed logs</div>
          <div>3. If tests fail, verify that:</div>
          <div className="ml-4">- The teacher has been assigned to classes in school-admin</div>
          <div className="ml-4">- The backend server is running</div>
          <div className="ml-4">- The teacher is logged in with correct permissions</div>
        </div>
      </div>
    </div>
  );
};

export default TeacherAssignmentDebug;
