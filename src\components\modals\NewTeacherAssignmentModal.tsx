"use client";

import { useState, useEffect } from "react";
import { X, Save, Loader2, Search, ChevronDown, Plus, Trash2 } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface NewTeacherAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  assignment?: any;
  classes: any[];
  subjects: any[];
  teachers: any[];
  loading?: boolean;
}

export default function NewTeacherAssignmentModal({
  isOpen,
  onClose,
  onSubmit,
  assignment,
  classes,
  subjects,
  teachers,
  loading = false
}: NewTeacherAssignmentModalProps) {
  const [formData, setFormData] = useState({
    teacher_id: '',
    class_id: '',
    subjects: [] as string[],
    academic_year: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Search states
  const [classSearch, setClassSearch] = useState('');
  const [teacherSearch, setTeacherSearch] = useState('');
  const [showClassDropdown, setShowClassDropdown] = useState(false);
  const [showTeacherDropdown, setShowTeacherDropdown] = useState(false);

  // Subject selection
  const [selectedSubjects, setSelectedSubjects] = useState<string[]>([]);
  const [subjectSearch, setSubjectSearch] = useState('');

  // Reset form when modal opens/closes or assignment changes
  useEffect(() => {
    if (isOpen) {
      if (assignment) {
        // Edit mode - populate form with assignment data
        const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;
        const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;

        setFormData({
          teacher_id: teacherId || '',
          class_id: classId || '',
          subjects: assignment.subjects || [],
          academic_year: assignment.academic_year || new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
        });
        setSelectedSubjects(assignment.subjects || []);
      } else {
        // Create mode - reset form
        setFormData({
          teacher_id: '',
          class_id: '',
          subjects: [],
          academic_year: new Date().getFullYear() + '-' + (new Date().getFullYear() + 1)
        });
        setSelectedSubjects([]);
      }
      setErrors({});
    }
  }, [isOpen, assignment]);

  // Update formData when selectedSubjects changes
  useEffect(() => {
    setFormData(prev => ({
      ...prev,
      subjects: selectedSubjects
    }));
  }, [selectedSubjects]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const addSubject = (subjectName: string) => {
    if (!selectedSubjects.includes(subjectName)) {
      setSelectedSubjects(prev => [...prev, subjectName]);
    }
    setSubjectSearch('');
  };

  const removeSubject = (subjectName: string) => {
    setSelectedSubjects(prev => prev.filter(s => s !== subjectName));
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.teacher_id) {
      newErrors.teacher_id = 'Teacher is required';
    }

    if (!formData.class_id) {
      newErrors.class_id = 'Class is required';
    }

    if (selectedSubjects.length === 0) {
      newErrors.subjects = 'At least one subject is required';
    }

    if (!formData.academic_year) {
      newErrors.academic_year = 'Academic year is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
    } catch (error) {
      console.error('Error submitting assignment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter functions
  const filteredClasses = classes.filter(cls =>
    cls.name.toLowerCase().includes(classSearch.toLowerCase()) ||
    cls.class_code?.toLowerCase().includes(classSearch.toLowerCase())
  );

  const filteredTeachers = teachers.filter(teacher => {
    const displayName = teacher.first_name && teacher.last_name 
      ? `${teacher.first_name} ${teacher.last_name}`
      : teacher.name || teacher.display_name;
    return displayName.toLowerCase().includes(teacherSearch.toLowerCase()) ||
           teacher.email?.toLowerCase().includes(teacherSearch.toLowerCase());
  });

  const availableSubjects = subjects.filter(subject =>
    !selectedSubjects.includes(subject.name) &&
    subject.name.toLowerCase().includes(subjectSearch.toLowerCase())
  );

  const getTeacherDisplayName = (teacher: any) => {
    return teacher.first_name && teacher.last_name 
      ? `${teacher.first_name} ${teacher.last_name}`
      : teacher.name || teacher.display_name;
  };

  const getClassDisplayName = (cls: any) => {
    return cls.class_code ? `${cls.name} (${cls.class_code})` : cls.name;
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-foreground">
              {assignment ? 'Edit Teacher Assignment' : 'Create Teacher Assignment'}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="h-5 w-5 text-foreground" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Teacher Selection */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Teacher <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="relative">
                  <input
                    type="text"
                    value={teacherSearch}
                    onChange={(e) => setTeacherSearch(e.target.value)}
                    onFocus={() => setShowTeacherDropdown(true)}
                    placeholder="Search teachers..."
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                  />
                  <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
                
                {showTeacherDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-widget border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {filteredTeachers.map((teacher) => (
                      <button
                        key={teacher._id}
                        type="button"
                        onClick={() => {
                          handleInputChange('teacher_id', teacher._id);
                          setTeacherSearch(getTeacherDisplayName(teacher));
                          setShowTeacherDropdown(false);
                        }}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-foreground"
                      >
                        <div className="font-medium">{getTeacherDisplayName(teacher)}</div>
                        <div className="text-sm text-gray-500">{teacher.email}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {errors.teacher_id && (
                <p className="text-red-500 text-sm">{errors.teacher_id}</p>
              )}
            </div>

            {/* Class Selection */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Class <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <div className="relative">
                  <input
                    type="text"
                    value={classSearch}
                    onChange={(e) => setClassSearch(e.target.value)}
                    onFocus={() => setShowClassDropdown(true)}
                    placeholder="Search classes..."
                    className="w-full px-3 py-2 pr-10 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                  />
                  <Search className="absolute right-3 top-2.5 h-5 w-5 text-gray-400" />
                </div>
                
                {showClassDropdown && (
                  <div className="absolute z-10 w-full mt-1 bg-widget border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-60 overflow-y-auto">
                    {filteredClasses.map((cls) => (
                      <button
                        key={cls._id}
                        type="button"
                        onClick={() => {
                          handleInputChange('class_id', cls._id);
                          setClassSearch(getClassDisplayName(cls));
                          setShowClassDropdown(false);
                        }}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-foreground"
                      >
                        <div className="font-medium">{getClassDisplayName(cls)}</div>
                        <div className="text-sm text-gray-500">Level: {cls.level}</div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {errors.class_id && (
                <p className="text-red-500 text-sm">{errors.class_id}</p>
              )}
            </div>

            {/* Subjects Selection */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Subjects <span className="text-red-500">*</span>
              </label>
              
              {/* Selected Subjects */}
              {selectedSubjects.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-2">
                  {selectedSubjects.map((subject) => (
                    <span
                      key={subject}
                      className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-teal/10 text-teal border border-teal/20"
                    >
                      {subject}
                      <button
                        type="button"
                        onClick={() => removeSubject(subject)}
                        className="ml-2 hover:text-red-500"
                      >
                        <Trash2 className="h-3 w-3" />
                      </button>
                    </span>
                  ))}
                </div>
              )}

              {/* Add Subject */}
              <div className="relative">
                <input
                  type="text"
                  value={subjectSearch}
                  onChange={(e) => setSubjectSearch(e.target.value)}
                  placeholder="Type to search and add subjects..."
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                />
                
                {subjectSearch && availableSubjects.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-widget border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg max-h-40 overflow-y-auto">
                    {availableSubjects.map((subject) => (
                      <button
                        key={subject._id}
                        type="button"
                        onClick={() => addSubject(subject.name)}
                        className="w-full px-3 py-2 text-left hover:bg-gray-100 dark:hover:bg-gray-700 text-foreground flex items-center"
                      >
                        <Plus className="h-4 w-4 mr-2 text-teal" />
                        {subject.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>
              {errors.subjects && (
                <p className="text-red-500 text-sm">{errors.subjects}</p>
              )}
            </div>

            {/* Academic Year */}
            <div className="space-y-2">
              <label className="block text-sm font-medium text-foreground">
                Academic Year <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={formData.academic_year}
                onChange={(e) => handleInputChange('academic_year', e.target.value)}
                placeholder="e.g., 2024-2025"
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              />
              {errors.academic_year && (
                <p className="text-red-500 text-sm">{errors.academic_year}</p>
              )}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end space-x-3 pt-4 border-t border-stroke">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || loading}
                className="px-6 py-2 bg-teal text-white rounded-lg hover:bg-teal/90 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
              >
                {(isSubmitting || loading) && (
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                )}
                <Save className="h-4 w-4 mr-2" />
                {assignment ? 'Update Assignment' : 'Create Assignment'}
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
