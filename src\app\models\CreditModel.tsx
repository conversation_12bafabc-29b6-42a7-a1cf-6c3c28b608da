// credit.model.ts

export interface CreditSchema extends Record<string, unknown> {
  _id: string;
  student_id: string;        // MongoDB ObjectId as string
  school_id: string;         // MongoDB ObjectId as string
  academicYear_id: string;   // MongoDB ObjectId as string
  amountPaid: number;
  paidAt?: string;           // Date as ISO string, optional because default is Date.now
  createdAt?: string;        // Auto-generated timestamp
  updatedAt?: string;        // Auto-generated timestamp
}

export interface CreditCreateSchema extends Record<string, unknown> {
  student_id: string;        // Required
  school_id: string;         // Required
  academicYear_id: string;   // Required
  amountPaid: number;        // Required
  paidAt?: string;           // Optional; if not provided, defaults to now
}

export interface CreditUpdateSchema extends Record<string, unknown> {
  _id: string;               // Required to identify which credit to update
  student_id?: string;
  school_id?: string;
  academicYear_id?: string;
  amountPaid?: number;
  paidAt?: string;
}

export interface CreditDeleteSchema extends Record<string, unknown> {
  _id: string;               // Required MongoDB ID to delete
}
