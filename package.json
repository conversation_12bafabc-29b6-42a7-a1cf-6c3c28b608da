{"name": "scolarify-backend", "version": "1.0.0", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node src/server.js", "dev": "nodemon src/server.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@arcjet/node": "^1.0.0-beta.2", "@vonage/server-sdk": "^3.20.0", "axios": "^1.7.9", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "bson": "^6.10.4", "cloudinary": "^1.41.3", "cookie": "^1.0.2", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "express": "^4.21.2", "firebase": "^11.2.0", "firebase-admin": "^13.0.2", "fs": "^0.0.1-security", "moment": "^2.30.1", "mongoose": "^8.9.3", "mongosh": "^2.3.8", "multer": "^2.0.0", "multer-storage-cloudinary": "^4.0.0", "node-cron": "^3.0.3", "node-fetch": "^3.3.2", "nodemailer": "^6.10.0", "pdfkit": "^0.17.1", "xlsx": "^0.18.5"}, "devDependencies": {"nodemon": "^3.1.10"}}