"use client";

import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout';
import CircularLoader from '@/components/widgets/CircularLoader';
import React, { Suspense, useEffect, useState, useMemo } from 'react'; // Added useMemo
import { Coins, Search, MapPin, Building2, MoveRight } from 'lucide-react'; // Added more icons
import { useRouter } from 'next/navigation';
import { CreditTransactionSchema } from '@/app/models/CreditTransactionModel';
import { getSchools } from '@/app/services/SchoolServices';
import { getCreditTransactions } from '@/app/services/CreditTransactionServices';
import { SchoolSchema } from '@/app/models/SchoolModel';
import Link from 'next/link';
// Removed DataTableFix as we're going with cards
// import DataTableFix from '@/components/utils/TableFix';
// CreditTransactionModal is for a different view, so it's not directly used here for the main display
// import CreditTransactionModal from './components/CreditTransactionModal';

export default function Page() {
    const BASE_URL = "/super-admin";

    const navigation = {
        icon: Coins,
        baseHref: `${BASE_URL}/credit`,
        title: "Credit Transactions",
    };

    function Credit() {
        const router = useRouter();
        const [transactions, setTransactions] = useState<CreditTransactionSchema[]>([]);
        const [schools, setSchools] = useState<SchoolSchema[]>([]);
        const [loadingData, setLoadingData] = useState(false);
        const [searchTerm, setSearchTerm] = useState(''); // State for search term

        // Fetch data on component mount
        useEffect(() => {
            const fetchData = async () => {
                setLoadingData(true);
                try {
                    const [fetchedTransactions, fetchedSchools] = await Promise.all([
                        getCreditTransactions(),
                        getSchools(),
                    ]);
                    setTransactions(fetchedTransactions);
                    setSchools(fetchedSchools);
                } catch (error) {
                    console.error("Error fetching data:", error);
                    // Optionally, show an error message to the user
                } finally {
                    setLoadingData(false);
                }
            };
            fetchData();
        }, []);

        // Filter schools based on search term
        const filteredSchools = useMemo(() => {
            const lowercasedSearchTerm = searchTerm.toLowerCase();
            return schools.filter(school =>
                school.name.toLowerCase().includes(lowercasedSearchTerm) ||
                (school.address ?? "").toLowerCase().includes(lowercasedSearchTerm)
            );
        }, [schools, searchTerm]);

        // Function to get transaction count for a school
        const getTransactionCount = (schoolId: string) => {
            return transactions.filter(tr => tr.school_id === schoolId).length;
        };

        return (
            <div className="p-6">
                {/* Search Bar */}
                <div className="mb-6 relative">
                    <input
                        type="text"
                        placeholder="Search schools by name or address..."
                        className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                    />
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
                </div>

                {loadingData ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {/* Loading Skeletons */}
                        {[...Array(6)].map((_, index) => (
                            <div key={index} className="bg-white dark:bg-gray-700 rounded-lg shadow-md p-6 animate-pulse">
                                <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-4"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
                                <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-2/3 mb-6"></div>
                                <div className="h-10 bg-gray-300 dark:bg-gray-600 rounded w-full"></div>
                            </div>
                        ))}
                    </div>
                ) : filteredSchools.length === 0 ? (
                    <div className="text-center py-10 text-gray-500 dark:text-gray-400">
                        <p>No schools found matching your search criteria.</p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        {filteredSchools.map((school) => (
                            <div key={school._id} className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 flex flex-col justify-between transition-transform transform hover:scale-105">
                                <div>
                                    <h3 className="text-xl font-semibold mb-2 text-foreground flex items-center">
                                        <Building2 className="mr-2 text-teal-500" size={24} />
                                        <Link href={`${BASE_URL}/schools/view?id=${school._id}`} className="hover:text-teal-600 transition-colors">
                                            {school.name}
                                        </Link>
                                    </h3>
                                    <p className="text-gray-600 dark:text-gray-300 mb-2 flex items-center">
                                        <MapPin className="mr-2 text-gray-400" size={18} />
                                        {school.address}
                                    </p>
                                    <p className="text-gray-600 dark:text-gray-300 mb-4 flex items-center gap-2">
                                        <Coins className="mr-2 text-yellow-500" size={18} />
                                        <span className="font-medium">{getTransactionCount(school._id)}</span> Credit Transactions
                                    </p>
                                </div>
                                <div className="mt-4">
                                    <button
                                        onClick={() => router.push(`${BASE_URL}/credit/manage?id=${school._id}`)}
                                        className="w-full gap-4 px-4 py-2 text-teal rounded-md transition-colors flex items-center justify-center"
                                    >
                                        Manage Credit
                                      <MoveRight className="mr-2" size={18} /> {/* Changed icon to BookText */}
                                        
                                    </button>
                                </div>
                            </div>
                        ))}
                    </div>
                )}
            </div>
        );
    }

    return (
        <Suspense fallback={
            <div className="flex justify-center items-center h-screen w-screen fixed top-0 left-0 bg-gray-50 dark:bg-gray-900 z-50">
                <CircularLoader size={32} color="teal" />
            </div>
        }>
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <Credit />
            </SuperLayout>
        </Suspense>
    );
}