import React from 'react';
import DataTableWithBulkActions from '@/components/enhanced/DataTableWithBulkActions';
import { ClassLevelSchema } from '@/app/models/ClassLevel';
import { deleteMultipleClassLevels, deleteAllClassLevels } from '@/app/services/ClassLevels';

interface ClassLevelsTableWithBulkActionsProps {
  classLevels: ClassLevelSchema[];
  setClassLevels: React.Dispatch<React.SetStateAction<ClassLevelSchema[]>>;
  columns: any[];
  actions: any[];
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: ClassLevelSchema[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const ClassLevelsTableWithBulkActions: React.FC<ClassLevelsTableWithBulkActionsProps> = ({
  classLevels,
  setClassLevels,
  columns,
  actions,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  onSuccess,
  onError
}) => {
  return (
    <DataTableWithBulkActions
      columns={columns}
      data={classLevels}
      actions={actions}
      defaultItemsPerPage={5}
      loading={loading}
      onLoadingChange={onLoadingChange}
      onSelectionChange={onSelectionChange}
      idAccessor="_id"
      enableBulkActions={true}
      deleteMultipleService={deleteMultipleClassLevels}
      deleteAllService={deleteAllClassLevels}
      itemName="class level"
      setItems={setClassLevels}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
};

export default ClassLevelsTableWithBulkActions;
