"use client";

import React from "react";
import { Settings, Al<PERSON><PERSON>rian<PERSON>, <PERSON> } from "lucide-react";

interface MaintenanceModeProps {
    message?: string;
    platformName?: string;
    supportEmail?: string;
}

const MaintenanceMode: React.FC<MaintenanceModeProps> = ({
    message = "We are currently performing scheduled maintenance. Please check back soon.",
    platformName = "Scholarify",
    supportEmail = "<EMAIL>"
}) => {
    return (
        <div className="min-h-screen bg-gradient-to-br from-teal/10 to-blue-500/10 flex items-center justify-center p-4">
            <div className="max-w-md w-full">
                <div className="bg-white rounded-2xl shadow-2xl p-8 text-center border border-gray-100">
                    {/* Icon */}
                    <div className="mb-6 flex justify-center">
                        <div className="relative">
                            <div className="w-20 h-20 bg-teal/10 rounded-full flex items-center justify-center">
                                <Settings className="w-10 h-10 text-teal animate-spin" style={{ animationDuration: '3s' }} />
                            </div>
                            <div className="absolute -top-1 -right-1 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center">
                                <AlertTriangle className="w-3 h-3 text-white" />
                            </div>
                        </div>
                    </div>

                    {/* Platform Name */}
                    <h1 className="text-2xl font-bold text-gray-900 mb-2">
                        {platformName}
                    </h1>

                    {/* Title */}
                    <h2 className="text-xl font-semibold text-gray-800 mb-4">
                        Under Maintenance
                    </h2>

                    {/* Message */}
                    <p className="text-gray-600 mb-6 leading-relaxed">
                        {message}
                    </p>

                    {/* Status indicators */}
                    <div className="flex items-center justify-center space-x-2 text-sm text-gray-500">
                        <Clock className="w-4 h-4" />
                        <span>Estimated completion: Soon</span>
                    </div>

                    {/* Footer */}
                    <div className="mt-8 pt-6 border-t border-gray-100">
                        <p className="text-xs text-gray-400">
                            Thank you for your patience
                        </p>
                    </div>
                </div>

                {/* Additional info card */}
                <div className="mt-4 bg-white/50 backdrop-blur-sm rounded-lg p-4 text-center border border-gray-100">
                    <p className="text-sm text-gray-600">
                        Need urgent support? Contact us at{" "}
                        <a 
                            href={`mailto:${supportEmail}`} 
                            className="text-teal hover:underline font-medium"
                        >
                            {supportEmail}
                        </a>
                    </p>
                </div>
            </div>
        </div>
    );
};

export default MaintenanceMode;