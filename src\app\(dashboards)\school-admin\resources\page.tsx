"use client";

import { BookOpen } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { ResourceSchema, getResources, getResourcesBySchoolId, createResource, updateResource, deleteResource, deleteMultipleResources } from "@/app/services/ResourcesServices";
import ResourceModal from "@/components/modals/ResourceModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import ResourcesSkeleton from "@/components/skeletons/ResourcesSkeleton";

const BASE_URL = "/school-admin";

const navigation = {
  icon: BookOpen,
  baseHref: `${BASE_URL}/resources`,
  title: "Resources"
};

function ResourcesContent() {
  const [resources, setResources] = useState<ResourceSchema[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedResources, setSelectedResources] = useState<ResourceSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [resourceToEdit, setResourceToEdit] = useState<ResourceSchema | null>(null);
  const [resourceToDelete, setResourceToDelete] = useState<ResourceSchema | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const { user } = useAuth();
  const router = useRouter();

  // Columns for the table
  const columns = [
    {
      header: "Resource ID",
      accessorKey: "resource_id",
      accessor: (row: ResourceSchema) => row.resource_id
    },
    {
      header: "Name",
      accessorKey: "name",
      accessor: (row: ResourceSchema) => row.name
    },
    {
      header: "Type",
      accessorKey: "resource_type",
      accessor: (row: ResourceSchema) => row.resource_type
    },
    {
      header: "Link",
      accessor: (row: ResourceSchema) => (
        <a
          href={row.link}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-500 hover:text-blue-700 underline"
        >
          View Resource
        </a>
      )
    },
    {
      header: "Created At",
      accessor: (row: ResourceSchema) => new Date(row.createdAt || "").toLocaleDateString()
    },
    {
      header: "Actions",
      accessor: (row: ResourceSchema) => row._id,
      cell: (props: any) => (
        <div className="flex space-x-2">
          <button
            onClick={() => handleEditResource(props.row.original)}
            className="px-2 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Edit
          </button>
          <button
            onClick={() => handleDeleteResource(props.row.original)}
            className="px-2 py-1 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors"
          >
            Delete
          </button>
        </div>
      ),
    },
  ];

  // Actions for the table
  const actions = [
    {
      label: "Delete Selected",
      onClick: () => {
        if (selectedResources.length > 0) {
          handleDeleteMultiple();
        }
      },
      disabled: selectedResources.length === 0,
    },
  ];

  // Load resources on page load
  useEffect(() => {
    fetchResources();
  }, []);

  const fetchResources = async () => {
    try {
      setLoadingData(true);
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const resourcesData = await getResourcesBySchoolId(schoolId);
        setResources(resourcesData);
      } else {
        const resourcesData = await getResources();
        setResources(resourcesData);
      }
    } catch (error) {
      console.error("Error fetching resources:", error);
      setSubmitStatus(createErrorNotification("Failed to fetch resources"));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new resource
  const handleCreateResource = () => {
    setResourceToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing resource
  const handleEditResource = (resource: ResourceSchema) => {
    setResourceToEdit(resource);
    setIsModalOpen(true);
  };

  // Handle deleting single resource
  const handleDeleteResource = (resource: ResourceSchema) => {
    setResourceToDelete(resource);
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple resources
  const handleDeleteMultiple = () => {
    setResourceToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle save (create or update)
  const handleSave = async (resourceData: any) => {
    setIsSubmitting(true);
    try {
      if (resourceToEdit) {
        // Update existing resource
        await updateResource(resourceToEdit.resource_id, resourceData);
        setSubmitStatus(createSuccessNotification("Resource updated successfully"));
      } else {
        // Create new resource
        await createResource(resourceData);
        setSubmitStatus(createSuccessNotification("Resource created successfully"));
      }

      setIsModalOpen(false);
      setResourceToEdit(null);
      await fetchResources(); // Refresh the list
    } catch (error) {
      console.error("Error saving resource:", error);
      setSubmitStatus(createErrorNotification("Failed to save resource"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = async () => {
    setIsSubmitting(true);
    try {
      if (resourceToDelete) {
        // Delete single resource
        await deleteResource(resourceToDelete._id);
        setSubmitStatus(createSuccessNotification("Resource deleted successfully"));
      } else if (selectedResources.length > 0) {
        // Delete multiple resources
        const ids = selectedResources.map(resource => resource._id);
        await deleteMultipleResources(ids);
        setSubmitStatus(createSuccessNotification(`${selectedResources.length} resources deleted successfully`));
        setSelectedResources([]);
      }

      setIsDeleteModalOpen(false);
      setResourceToDelete(null);
      await fetchResources(); // Refresh the list
    } catch (error) {
      console.error("Error deleting resource(s):", error);
      setSubmitStatus(createErrorNotification("Failed to delete resource(s)"));
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <ResourcesSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">Resources Management</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateResource}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        Add New Resource
      </motion.button>

      <DataTableFix<ResourceSchema>
        columns={columns}
        data={resources}
        actions={actions}
        defaultItemsPerPage={10}
        loading={loadingData}
        onLoadingChange={setLoadingData}
        onSelectionChange={setSelectedResources}
      />

      {/* Resource Modal */}
      <ResourceModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setResourceToEdit(null);
        }}
        onSave={handleSave}
        resource={resourceToEdit}
        isSubmitting={isSubmitting}
      />

      {/* Delete Confirmation Modal */}
      <DeleteConfirmationModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setResourceToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={resourceToDelete ? "Delete Resource" : "Delete Selected Resources"}
        message={
          resourceToDelete
            ? `Are you sure you want to delete the resource "${resourceToDelete.name}"? This action cannot be undone.`
            : `Are you sure you want to delete ${selectedResources.length} selected resources? This action cannot be undone.`
        }
        isSubmitting={isSubmitting}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <ResourcesContent />
      </SchoolLayout>
    </Suspense>
  );
}
