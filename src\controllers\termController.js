const Term = require('../models/Term');
const ActivityLog = require('../models/ActivityLog');

// Test route
const testTermResponse = async (req, res) => {
  res.status(200).json({ message: 'Term routes are working!' });
};

// Get all terms for a school
const getTermsBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { academic_year } = req.query;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    let query = { school_id, is_active: true };
    if (academic_year) {
      query.academic_year = academic_year;
    }

    const terms = await Term.find(query)
      .sort({ term_number: 1 })
      .lean();

    res.status(200).json({
      terms,
      message: 'Terms retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching terms:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Get current term for a school
const getCurrentTerm = async (req, res) => {
  try {
    const { school_id } = req.params;

    if (!school_id) {
      return res.status(400).json({ message: 'School ID is required' });
    }

    const currentTerm = await Term.findOne({ 
      school_id, 
      is_current: true, 
      is_active: true 
    }).lean();

    if (!currentTerm) {
      return res.status(404).json({ message: 'No current term found for this school' });
    }

    res.status(200).json({
      term: currentTerm,
      message: 'Current term retrieved successfully'
    });
  } catch (error) {
    console.error('Error fetching current term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Create a new term
const createTerm = async (req, res) => {
  try {
    const { school_id } = req.params;
    const { name, term_number, sequences, academic_year, start_date, end_date, is_current } = req.body;

    if (!school_id || !name || !term_number || !sequences || !academic_year || !start_date || !end_date) {
      return res.status(400).json({ 
        message: 'All required fields must be provided' 
      });
    }

    // Check if term number already exists for this school and academic year
    const existingTerm = await Term.findOne({ 
      school_id, 
      term_number,
      academic_year 
    });

    if (existingTerm) {
      return res.status(400).json({ 
        message: `Term ${term_number} already exists for academic year ${academic_year}` 
      });
    }

    // If this term is set as current, unset other current terms for this school
    if (is_current) {
      await Term.updateMany(
        { school_id, is_current: true },
        { is_current: false }
      );
    }

    // Validate sequences
    if (!Array.isArray(sequences) || sequences.length === 0) {
      return res.status(400).json({ 
        message: 'At least one sequence is required' 
      });
    }

    // Validate sequence numbers are unique within the term
    const sequenceNumbers = sequences.map(seq => seq.sequence_number);
    const uniqueSequenceNumbers = [...new Set(sequenceNumbers)];
    if (sequenceNumbers.length !== uniqueSequenceNumbers.length) {
      return res.status(400).json({ 
        message: 'Sequence numbers must be unique within a term' 
      });
    }

    const newTerm = new Term({
      school_id,
      name,
      term_number,
      sequences,
      academic_year,
      start_date,
      end_date,
      is_current: is_current || false
    });

    await newTerm.save();

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user._id,
      action: 'term_created',
      target_type: 'term',
      target_id: newTerm._id,
      details: `Created term: ${name} for school ${school_id}`
    });

    res.status(201).json({
      term: newTerm,
      message: 'Term created successfully'
    });
  } catch (error) {
    console.error('Error creating term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Update a term
const updateTerm = async (req, res) => {
  try {
    const { school_id, term_id } = req.params;
    const updateData = req.body;

    if (!school_id || !term_id) {
      return res.status(400).json({ 
        message: 'School ID and Term ID are required' 
      });
    }

    const term = await Term.findOne({ _id: term_id, school_id });
    if (!term) {
      return res.status(404).json({ message: 'Term not found' });
    }

    // If updating to current term, unset other current terms
    if (updateData.is_current === true) {
      await Term.updateMany(
        { school_id, is_current: true, _id: { $ne: term_id } },
        { is_current: false }
      );
    }

    // If term_number is being changed, check for conflicts
    if (updateData.term_number && updateData.term_number !== term.term_number) {
      const existingTerm = await Term.findOne({ 
        school_id, 
        term_number: updateData.term_number,
        academic_year: updateData.academic_year || term.academic_year,
        _id: { $ne: term_id }
      });

      if (existingTerm) {
        return res.status(400).json({ 
          message: `Term ${updateData.term_number} already exists for this academic year` 
        });
      }
    }

    // Validate sequences if provided
    if (updateData.sequences) {
      if (!Array.isArray(updateData.sequences) || updateData.sequences.length === 0) {
        return res.status(400).json({ 
          message: 'At least one sequence is required' 
        });
      }

      const sequenceNumbers = updateData.sequences.map(seq => seq.sequence_number);
      const uniqueSequenceNumbers = [...new Set(sequenceNumbers)];
      if (sequenceNumbers.length !== uniqueSequenceNumbers.length) {
        return res.status(400).json({ 
          message: 'Sequence numbers must be unique within a term' 
        });
      }
    }

    const updatedTerm = await Term.findByIdAndUpdate(
      term_id,
      updateData,
      { new: true, runValidators: true }
    );

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user._id,
      action: 'term_updated',
      target_type: 'term',
      target_id: term_id,
      details: `Updated term: ${updatedTerm.name} for school ${school_id}`
    });

    res.status(200).json({
      term: updatedTerm,
      message: 'Term updated successfully'
    });
  } catch (error) {
    console.error('Error updating term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Delete a term
const deleteTerm = async (req, res) => {
  try {
    const { school_id, term_id } = req.params;

    if (!school_id || !term_id) {
      return res.status(400).json({ 
        message: 'School ID and Term ID are required' 
      });
    }

    const term = await Term.findOne({ _id: term_id, school_id });
    if (!term) {
      return res.status(404).json({ message: 'Term not found' });
    }

    // Soft delete by setting is_active to false
    await Term.findByIdAndUpdate(term_id, { is_active: false });

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user._id,
      action: 'term_deleted',
      target_type: 'term',
      target_id: term_id,
      details: `Deleted term: ${term.name} for school ${school_id}`
    });

    res.status(200).json({
      message: 'Term deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

// Set current term
const setCurrentTerm = async (req, res) => {
  try {
    const { school_id, term_id } = req.params;

    if (!school_id || !term_id) {
      return res.status(400).json({ 
        message: 'School ID and Term ID are required' 
      });
    }

    const term = await Term.findOne({ _id: term_id, school_id, is_active: true });
    if (!term) {
      return res.status(404).json({ message: 'Term not found or inactive' });
    }

    // Unset current term for all other terms in this school
    await Term.updateMany(
      { school_id, is_current: true },
      { is_current: false }
    );

    // Set this term as current
    await Term.findByIdAndUpdate(term_id, { is_current: true });

    // Log activity
    await ActivityLog.logActivity({
      user_id: req.user._id,
      action: 'term_set_current',
      target_type: 'term',
      target_id: term_id,
      details: `Set term: ${term.name} as current for school ${school_id}`
    });

    res.status(200).json({
      message: 'Current term updated successfully'
    });
  } catch (error) {
    console.error('Error setting current term:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
};

module.exports = {
  testTermResponse,
  getTermsBySchool,
  getCurrentTerm,
  createTerm,
  updateTerm,
  deleteTerm,
  setCurrentTerm
};
