// routes/subjectRoutes.js
const express = require('express');
const subjectController = require('../controllers/subjectController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');


const router = express.Router();
// router.get('/test', subjectController.testSubjectResponse);

// GET /subjects to fetch all subjects
router.get('/get-subjects', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']) , subjectController.getAllSubjects);

//GET subjects by id
router.get('/get-subject/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']) , subjectController.getSubjectById);
router.get('/get-subject-by-id/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'parent', 'teacher', 'school_admin', 'dean_of_studies', 'bursar']) , subjectController.getSubjectBy_Id);

// POST /subjects to create a new subject
router.post('/create-subject' , authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), subjectController.createSubject);

// PUT /subjects/:id to update a specific subject
router.put('/update-subject/:id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']) , subjectController.updateSubjectById);

// DELETE /subjects/:id to delete a specific subject
router.delete('/delete-subject/:id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']) ,  subjectController.deleteSubjectById);

//DELETE multiple subjects
router.delete('/delete-subjects', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), subjectController.deleteMultipleSubjects);

//DELETE ALL subjects
router.delete('/delete-all-subjects', authenticate, authorize(['super']), subjectController.deleteAllSubjects);

// Get subjects by school_id
router.get('/get-subject-by-school/:schoolId', authenticate, checkSubscription, authorize(['admin', 'super', 'teacher', 'parent', 'school_admin', 'dean_of_studies', 'bursar']), subjectController.getSubjectsBySchoolId);
 
// Get subjects by class_id
router.get('/get-subject-by-class/:classId', authenticate, checkSubscription, authorize(['admin', 'super', 'teacher', 'parent', 'school_admin', 'dean_of_studies', 'bursar']), subjectController.getSubjectsByClassId);
module.exports = router;
