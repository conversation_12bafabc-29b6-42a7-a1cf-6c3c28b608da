import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";
// const BASE_API_URL = "http://localhost:3002/api"; // For local development, change as needed
export interface ScheduleEntry {
  _id: string;
  class_name: string;
  subject_name: string;
  teacher_name: string;
  period_number: number;
  start_time: string;
  end_time: string;
  day_of_week: string;
  schedule_type: string;
  student_count?: number;
}

export interface TimetableData {
  [day: string]: {
    [period: string]: ScheduleEntry | null;
  };
}

export interface Period {
  _id: string;
  period_number: number;
  start_time: string;
  end_time: string;
}

export interface TimetableFilters {
  class_id?: string;
  teacher_id?: string;
  day_of_week?: string;
}

export interface ConflictDetails {
  teacher_name: string;
  class_name: string;
  subject_name: string;
  period_info: {
    period_number: number;
    start_time: string;
    end_time: string;
  } | null;
  day_of_week: string;
}

export interface TeacherConflictError extends Error {
  conflict: boolean;
  available: boolean;
  conflictDetails: ConflictDetails;
}

export interface ScheduleConflictError extends Error {
  conflict: boolean;
  available: boolean;
  conflictType: 'same_type' | 'different_type';
  conflictDetails: ConflictDetails & {
    existing_type: string;
    current_type: string;
    suggestion?: string;
  };
}

export interface TimetableStats {
  total_periods: number;
  total_schedule_entries: number;
  unique_teachers: number;
  unique_subjects: number;
  unique_classes: number;
  free_slots: number;
  utilization_rate: number;
}

export interface TeacherStats {
  total_classes: number;
  different_classes: number;
  total_students: number;
  subjects: string[];
}

// Get timetable for a school
export async function getTimetable(schoolId: string, filters: TimetableFilters = {}): Promise<{
  schedule_records: ScheduleEntry[];
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/timetable/school/${schoolId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching timetable:", response.statusText);
      throw new Error("Failed to fetch timetable");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch timetable error:", error);
    throw new Error("Failed to fetch timetable");
  }
}

// Get organized timetable (by days and periods)
export async function getOrganizedTimetable(schoolId: string, filters: TimetableFilters = {}): Promise<{
  timetable: TimetableData;
  periods: Period[];
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/timetable/school/${schoolId}/organized${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching organized timetable:", response.statusText);
      throw new Error("Failed to fetch organized timetable");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch organized timetable error:", error);
    throw new Error("Failed to fetch organized timetable");
  }
}

// Get teacher's personal timetable
export async function getTeacherTimetable(schoolId: string, teacherId: string, academicYear?: string): Promise<{
  timetable: TimetableData;
  periods: Period[];
  teacher_stats: TeacherStats;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string with academic year if provided
    const queryParams = new URLSearchParams();
    if (academicYear) {
      queryParams.append('academic_year', academicYear);
    }

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/timetable/school/${schoolId}/teacher/${teacherId}${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher timetable:", response.statusText);
      throw new Error("Failed to fetch teacher timetable");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch teacher timetable error:", error);
    throw new Error("Failed to fetch teacher timetable");
  }
}

// Get teacher's exam supervisions
export async function getTeacherSupervisions(schoolId: string, teacherId: string, academicYear?: string): Promise<{
  supervisions: ScheduleEntry[];
  total: number;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string with academic year if provided
    const queryParams = new URLSearchParams();
    if (academicYear) {
      queryParams.append('academic_year', academicYear);
    }

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/timetable/school/${schoolId}/teacher/${teacherId}/supervisions${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching teacher supervisions:", response.statusText);
      throw new Error("Failed to fetch teacher supervisions");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch teacher supervisions error:", error);
    throw new Error("Failed to fetch teacher supervisions");
  }
}

// Get timetable statistics
export async function getTimetableStats(schoolId: string): Promise<{
  stats: TimetableStats;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/timetable/school/${schoolId}/stats`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching timetable stats:", response.statusText);
      throw new Error("Failed to fetch timetable statistics");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Fetch timetable stats error:", error);
    throw new Error("Failed to fetch timetable statistics");
  }
}

// Create schedule entry
export async function createScheduleEntry(schoolId: string, scheduleData: any): Promise<{
  schedule: ScheduleEntry;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/timetable/school/${schoolId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(scheduleData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error creating schedule entry:", errorData);

      // Check if it's a conflict error
      if (errorData.conflict && errorData.conflictDetails) {
        // Check if it's a schedule conflict (same time slot)
        if (errorData.conflictType === 'same_type' || errorData.conflictType === 'different_type') {
          const scheduleConflictError = new Error(errorData.message || "Schedule conflict detected") as ScheduleConflictError;
          scheduleConflictError.conflict = true;
          scheduleConflictError.available = false;
          scheduleConflictError.conflictType = errorData.conflictType;
          scheduleConflictError.conflictDetails = errorData.conflictDetails;
          throw scheduleConflictError;
        } else {
          // Teacher conflict
          const conflictError = new Error(errorData.message || "Teacher conflict detected") as TeacherConflictError;
          conflictError.conflict = true;
          conflictError.available = false;
          conflictError.conflictDetails = errorData.conflictDetails;
          throw conflictError;
        }
      }

      throw new Error(errorData.message || "Failed to create schedule entry");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create schedule entry error:", error);
    throw error;
  }
}

// Update schedule entry
export async function updateScheduleEntry(schoolId: string, scheduleId: string, scheduleData: any): Promise<{
  schedule: ScheduleEntry;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/timetable/school/${schoolId}/schedule/${scheduleId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(scheduleData),
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error updating schedule entry:", errorData);

      // Check if it's a conflict error
      if (errorData.conflict && errorData.conflictDetails) {
        // Check if it's a schedule conflict (same time slot)
        if (errorData.conflictType === 'same_type' || errorData.conflictType === 'different_type') {
          const scheduleConflictError = new Error(errorData.message || "Schedule conflict detected") as ScheduleConflictError;
          scheduleConflictError.conflict = true;
          scheduleConflictError.available = false;
          scheduleConflictError.conflictType = errorData.conflictType;
          scheduleConflictError.conflictDetails = errorData.conflictDetails;
          throw scheduleConflictError;
        } else {
          // Teacher conflict
          const conflictError = new Error(errorData.message || "Teacher conflict detected") as TeacherConflictError;
          conflictError.conflict = true;
          conflictError.available = false;
          conflictError.conflictDetails = errorData.conflictDetails;
          throw conflictError;
        }
      }

      throw new Error(errorData.message || "Failed to update schedule entry");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update schedule entry error:", error);
    throw error;
  }
}

// Delete schedule entry
export async function deleteScheduleEntry(schoolId: string, scheduleId: string): Promise<{
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/timetable/school/${schoolId}/schedule/${scheduleId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      console.error("Error deleting schedule entry:", errorData);
      throw new Error(errorData.message || "Failed to delete schedule entry");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete schedule entry error:", error);
    throw error;
  }
}
