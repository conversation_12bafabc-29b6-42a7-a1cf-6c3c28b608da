// controllers/schoolController.js
const mongoose = require('mongoose');
const School = require('../models/School'); // Assuming you have a School model
const { ensureUniqueId } = require('../utils/generateId'); 
const { getMonthDateRange } = require('../utils/DateRange');
const getPerformanceData = require('../utils/performanceData');


const testSchoolResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is school' });
}; 

// // Get all schools
const getAllSchools = async (req, res) => {
  try {
    const schools = await School.find();
    res.json(schools);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Create a new school
const createSchool = async (req, res) => {
  try {
    const schoolId = await ensureUniqueId(School, 'school_id', 'SCHL');
    const newSchool = new School({school_id:schoolId, ...req.body});
    await newSchool.save();
    res.status(201).json(newSchool);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Get a school by ID
const getSchoolById = async (req, res) => {
  try {
    const school = await School.findOne({school_id:req.params.id});
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }
    res.json(school);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

const getSchoolBy_id = async (req, res) => {
  try {
    const _id = new mongoose.Types.ObjectId(req.params.id);

    const school = await School.findById(_id);
    if (!school) {
      return res.status(404).json({ message: 'School not found' });
    }
    res.json(school);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// // Update school by ID
const updateSchoolById = async (req, res) => {
  try {
    const updatedSchool = await School.findOneAndUpdate({school_id:req.params.id}, req.body, { new: true });
    if (!updatedSchool) {
      return res.status(404).json({ message: 'School not found' });
    }
    res.json(updatedSchool);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// // Delete school by ID
const deleteSchoolById = async (req, res) => {
  try {
  const deletedSchool = await School.findOneAndDelete({school_id:req.params.id});
    if (!deletedSchool) {
      return res.status(404).json({ message: 'School not found' });
    }
    res.json({ message: 'School deleted successfully' });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete multiple school records by IDs
const deleteMultipleSchools = async (req, res) => {
  const { ids } = req.body; // Expecting an array of school IDs in the request body
  if (!Array.isArray(ids) || ids.length === 0) {
    return res.status(400).json({ message: 'Invalid input: ids should be a non-empty array' });
  }

  try {
    // Delete school records where _id is in the provided array of IDs
    const result = await School.deleteMany({ _id: { $in: ids } });

    if (result.deletedCount === 0) {
      return res.status(404).json({ message: 'No school records found for the provided IDs' });
    }

    res.json({ message: `${result.deletedCount} school records deleted successfully` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Delete ALL school records
const deleteAllSchools = async (req, res) => {
  try {
    // First, count how many schools exist
    const schoolCount = await School.countDocuments();

    if (schoolCount === 0) {
      return res.status(404).json({ message: 'No schools found to delete' });
    }

    // Delete all school records
    const result = await School.deleteMany({});

    res.json({
      message: `All ${result.deletedCount} school records deleted successfully`,
      deletedCount: result.deletedCount
    });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Get total number of schools
const getTotalSchools = async (req, res) => {
  try {
    const totalSchools = await School.countDocuments();
    res.status(200).json({ totalSchools });
  } catch (error) {
    console.error("Error getting total schools:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};

// Get total number of schools created in current month and previous month + percentage change
const getSchoolCountWithChange = async (req, res) => {
  try {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();

    // Date ranges for current and previous month
    const { start: currentStart, end: currentEnd } = getMonthDateRange(currentYear, currentMonth);
    let prevYear = currentYear;
    let prevMonth = currentMonth - 1;
    if (prevMonth < 0) {
      prevMonth = 11;
      prevYear--;
    }
    const { start: prevStart, end: prevEnd } = getMonthDateRange(prevYear, prevMonth);

    // Count schools created in current month
    const currentCount = await School.countDocuments({
      createdAt: { $gte: currentStart, $lt: currentEnd }
    });

    // Count schools created in previous month
    const prevCount = await School.countDocuments({
      createdAt: { $gte: prevStart, $lt: prevEnd }
    });

    // Calculate percentage change
    let percentageChange = null;
    if (prevCount === 0 && currentCount > 0) {
      percentageChange = 100;
    } else if (prevCount === 0 && currentCount === 0) {
      percentageChange = 0;
    } else {
      percentageChange = ((currentCount - prevCount) / prevCount) * 100;
    }

    res.status(200).json({
      totalSchoolsThisMonth: currentCount,
      percentageChange: Number(percentageChange.toFixed(2))
    });
  } catch (error) {
    console.error("Error calculating school count change:", error);
    res.status(500).json({ message: "Internal server error" });
  }
};


const getSchoolsPerformance = async (req, res) => {
  try {
    const data = await getPerformanceData();
    res.status(200).json(data);
  } catch (error) {
    console.error('Error fetching performance data:', error);
    res.status(500).json({ error: 'Failed to fetch performance data' });
  }
};

module.exports = {
  testSchoolResponse,
  getAllSchools,
  createSchool,
  getSchoolById,
  getSchoolBy_id,
  updateSchoolById,
  deleteSchoolById,
  deleteMultipleSchools,
  deleteAllSchools,
  getTotalSchools,
  getSchoolCountWithChange,
  getSchoolsPerformance,
};