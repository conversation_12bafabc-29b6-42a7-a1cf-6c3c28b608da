/**
 * Script d'initialisation des plans de souscription par défaut
 * 
 * Ce script crée les plans Basic, Standard et Custom avec leurs configurations
 * par défaut dans la base de données.
 * 
 * Usage: node src/scripts/initializeSubscriptionPlans.js
 */

const mongoose = require('mongoose');
const SubscriptionPlan = require('../models/SubscriptionPlan');
require('dotenv').config();

async function initializePlans() {
  try {
    // Connexion à MongoDB
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie');

    // Initialiser les plans par défaut
    await SubscriptionPlan.initializeDefaultPlans();
    
    console.log('✅ Plans de souscription initialisés avec succès');

    // Afficher les plans créés
    const plans = await SubscriptionPlan.getActivePlans();
    console.log('\n📋 Plans créés:');
    
    plans.forEach(plan => {
      console.log(`\n🔹 ${plan.display_name} (${plan.plan_name})`);
      console.log(`   Prix: ${plan.price_per_credit > 0 ? plan.price_per_credit + ' FCFA/crédit' : 'Sur mesure'}`);
      console.log(`   Chatbot: ${plan.chatbot_enabled ? 'Oui' : 'Non'}`);
      console.log(`   Fonctionnalités: ${plan.features.length}`);
      console.log(`   Populaire: ${plan.is_popular ? 'Oui' : 'Non'}`);
    });

    console.log('\n🎉 Initialisation terminée avec succès !');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation:', error);
    process.exit(1);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Connexion MongoDB fermée');
    process.exit(0);
  }
}

// Exécuter le script si appelé directement
if (require.main === module) {
  initializePlans();
}

module.exports = { initializePlans };
