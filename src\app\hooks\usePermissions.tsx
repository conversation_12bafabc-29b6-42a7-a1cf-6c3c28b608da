"use client";

import { useState, useEffect } from "react";
import useAuth from "./useAuth";

interface Permission {
  students: {
    view_all_students: boolean;
    add_edit_delete_students: boolean;
    generate_id_cards: boolean;
    generate_report_cards: boolean;
  };
  academic_records: {
    view_grades_assigned_classes: boolean;
    enter_edit_grades_assigned_classes: boolean;
    view_all_school_grades: boolean;
    take_attendance_assigned_classes: boolean;
    view_all_attendance: boolean;
  };
  financials: {
    view_student_fee_balances: boolean;
    record_fee_payments: boolean;
    manage_school_credit_balance: boolean;
    view_financial_reports: boolean;
  };
  staff: {
    view_staff_list: boolean;
    add_edit_delete_staff: boolean;
    manage_staff_permissions: boolean;
    reset_staff_passwords: boolean;
  };
  classes: {
    view_all_classes: boolean;
    add_edit_delete_classes: boolean;
    manage_class_schedules: boolean;
    assign_teachers_to_classes: boolean;
  };
  announcements: {
    view_announcements: boolean;
    create_edit_announcements: boolean;
    delete_announcements: boolean;
    publish_announcements: boolean;
  };
  resources: {
    view_resources: boolean;
    add_edit_delete_resources: boolean;
    manage_resource_categories: boolean;
  };
  reports: {
    generate_student_reports: boolean;
    generate_financial_reports: boolean;
    generate_attendance_reports: boolean;
    export_data: boolean;
  };
}

interface UsePermissionsReturn {
  permissions: Permission | null;
  hasPermission: (module: keyof Permission, action: string) => boolean;
  hasAnyPermission: (module: keyof Permission, actions: string[]) => boolean;
  hasAllPermissions: (module: keyof Permission, actions: string[]) => boolean;
  canAccess: (requiredPermissions: { module: keyof Permission; action: string }[]) => boolean;
  isLoading: boolean;
  userRole: string | null;
  isAdmin: boolean;
  isTeacher: boolean;
  isSchoolAdmin: boolean;
  isSuperAdmin: boolean;
}

export default function usePermissions(): UsePermissionsReturn {
  const { user } = useAuth();
  const [permissions, setPermissions] = useState<Permission | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (user) {
      // For super admin, grant all permissions
      if (user.role === 'super' || user.role === 'admin') {
        const allPermissions: Permission = {
          students: {
            view_all_students: true,
            add_edit_delete_students: true,
            generate_id_cards: true,
            generate_report_cards: true,
          },
          academic_records: {
            view_grades_assigned_classes: true,
            enter_edit_grades_assigned_classes: true,
            view_all_school_grades: true,
            take_attendance_assigned_classes: true,
            view_all_attendance: true,
          },
          financials: {
            view_student_fee_balances: true,
            record_fee_payments: true,
            manage_school_credit_balance: true,
            view_financial_reports: true,
          },
          staff: {
            view_staff_list: true,
            add_edit_delete_staff: true,
            manage_staff_permissions: true,
            reset_staff_passwords: true,
          },
          classes: {
            view_all_classes: true,
            add_edit_delete_classes: true,
            manage_class_schedules: true,
            assign_teachers_to_classes: true,
          },
          announcements: {
            view_announcements: true,
            create_edit_announcements: true,
            delete_announcements: true,
            publish_announcements: true,
          },
          resources: {
            view_resources: true,
            add_edit_delete_resources: true,
            manage_resource_categories: true,
          },
          reports: {
            generate_student_reports: true,
            generate_financial_reports: true,
            generate_attendance_reports: true,
            export_data: true,
          },
        };
        setPermissions(allPermissions);
      } else {
        // For other roles, use permissions from user object or fetch from API
        // This would typically come from the user's staff permissions
        const userPermissions = user.permissions || getDefaultPermissionsForRole(user.role);
        setPermissions(userPermissions);
      }
      setIsLoading(false);
    } else {
      setPermissions(null);
      setIsLoading(false);
    }
  }, [user]);

  const getDefaultPermissionsForRole = (role: string): Permission => {
    const defaultPermissions: Record<string, Permission> = {
      school_admin: {
        students: {
          view_all_students: true,
          add_edit_delete_students: true,
          generate_id_cards: true,
          generate_report_cards: true,
        },
        academic_records: {
          view_grades_assigned_classes: true,
          enter_edit_grades_assigned_classes: true,
          view_all_school_grades: true,
          take_attendance_assigned_classes: true,
          view_all_attendance: true,
        },
        financials: {
          view_student_fee_balances: true,
          record_fee_payments: true,
          manage_school_credit_balance: false,
          view_financial_reports: true,
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: true,
          manage_staff_permissions: true,
          reset_staff_passwords: true,
        },
        classes: {
          view_all_classes: true,
          add_edit_delete_classes: true,
          manage_class_schedules: true,
          assign_teachers_to_classes: true,
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: true,
          delete_announcements: true,
          publish_announcements: true,
        },
        resources: {
          view_resources: true,
          add_edit_delete_resources: true,
          manage_resource_categories: true,
        },
        reports: {
          generate_student_reports: true,
          generate_financial_reports: true,
          generate_attendance_reports: true,
          export_data: true,
        },
      },
      teacher: {
        students: {
          view_all_students: true,
          add_edit_delete_students: false,
          generate_id_cards: false,
          generate_report_cards: true,
        },
        academic_records: {
          view_grades_assigned_classes: true,
          enter_edit_grades_assigned_classes: true,
          view_all_school_grades: false,
          take_attendance_assigned_classes: true,
          view_all_attendance: false,
        },
        financials: {
          view_student_fee_balances: false,
          record_fee_payments: false,
          manage_school_credit_balance: false,
          view_financial_reports: false,
        },
        staff: {
          view_staff_list: true,
          add_edit_delete_staff: false,
          manage_staff_permissions: false,
          reset_staff_passwords: false,
        },
        classes: {
          view_all_classes: true,
          add_edit_delete_classes: false,
          manage_class_schedules: false,
          assign_teachers_to_classes: false,
        },
        announcements: {
          view_announcements: true,
          create_edit_announcements: false,
          delete_announcements: false,
          publish_announcements: false,
        },
        resources: {
          view_resources: true,
          add_edit_delete_resources: true,
          manage_resource_categories: false,
        },
        reports: {
          generate_student_reports: true,
          generate_financial_reports: false,
          generate_attendance_reports: true,
          export_data: false,
        },
      },
    };

    return defaultPermissions[role] || defaultPermissions.teacher;
  };

  const hasPermission = (module: keyof Permission, action: string): boolean => {
    if (!permissions) return false;
    if (user?.role === 'super' || user?.role === 'admin') return true;
    
    const modulePermissions = permissions[module] as Record<string, boolean>;
    return modulePermissions?.[action] || false;
  };

  const hasAnyPermission = (module: keyof Permission, actions: string[]): boolean => {
    return actions.some(action => hasPermission(module, action));
  };

  const hasAllPermissions = (module: keyof Permission, actions: string[]): boolean => {
    return actions.every(action => hasPermission(module, action));
  };

  const canAccess = (requiredPermissions: { module: keyof Permission; action: string }[]): boolean => {
    return requiredPermissions.every(({ module, action }) => hasPermission(module, action));
  };

  return {
    permissions,
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    canAccess,
    isLoading,
    userRole: user?.role || null,
    isAdmin: user?.role === 'admin' || user?.role === 'super',
    isTeacher: user?.role === 'teacher',
    isSchoolAdmin: user?.role === 'school_admin',
    isSuperAdmin: user?.role === 'super',
  };
}
