import { getToken<PERSON>romCookie } from "./UserServices"; 
import { BASE_API_URL } from "./AuthContext";
// const BASE_API_URL = "http://localhost:3002/api"
export interface StaffSchema extends Record<string, any> {
  _id: string;
  user_id: string;
  staff_id?: string;
  firebaseUid?: string;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  school_ids: string[];
  access_codes?: {
    school_id: string;
    access_code: string;
    granted_at: Date;
    granted_by: string;
    is_active: boolean;
  }[];
  avatar?: string;
  is_staff_active: boolean;
  createdAt: Date;
  updatedAt: Date;
  permissions?: StaffPermissionSchema;
}

export interface StaffPermissionSchema {
  _id: string;
  permission_id: string;
  user_id: string;
  school_id: string;
  role_template: 'school_admin' | 'teacher' | 'bursar' | 'dean_of_studies' | 'custom';
  permissions: {
    students: {
      view_all_students: boolean;
      add_edit_delete_students: boolean;
      generate_id_cards: boolean;
      generate_report_cards: boolean;
    };
    academic_records: {
      view_grades_assigned_classes: boolean;
      enter_edit_grades_assigned_classes: boolean;
      view_all_school_grades: boolean;
      take_attendance_assigned_classes: boolean;
      view_all_attendance: boolean;
    };
    financials: {
      view_student_fee_balances: boolean;
      record_fee_payments: boolean;
      manage_school_credit_balance: boolean;
      view_financial_reports: boolean;
    };
    staff: {
      view_staff_list: boolean;
      add_edit_delete_staff: boolean;
      manage_staff_permissions: boolean;
      reset_staff_passwords: boolean;
    };
    classes: {
      view_all_classes: boolean;
      add_edit_delete_classes: boolean;
      manage_class_schedules: boolean;
      assign_teachers_to_classes: boolean;
    };
    announcements: {
      view_announcements: boolean;
      create_edit_announcements: boolean;
      delete_announcements: boolean;
      publish_announcements: boolean;
    };
    resources: {
      view_resources: boolean;
      add_edit_delete_resources: boolean;
      manage_resource_categories: boolean;
    };
    reports: {
      generate_student_reports: boolean;
      generate_financial_reports: boolean;
      generate_attendance_reports: boolean;
      export_data: boolean;
    };
  };
  assigned_classes: {
    class_id: string;
    subjects: string[];
    periods: string[];
  }[];
  is_active: boolean;
  granted_by: string;
  granted_at: Date;
}

export interface StaffCreateSchema {
  first_name: string;
  last_name: string;
  email: string;
  phone?: string;
  role_template: 'school_admin' | 'teacher' | 'bursar' | 'dean_of_studies' | 'custom';
  school_id: string;
  permissions?: any;
  assigned_classes?: {
    class_id: string;
    subjects: string[];
    periods: string[];
  }[];
  is_existing_teacher?: boolean;
  teacher_id?: string;
}

export interface StaffUpdateSchema {
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  role_template?: 'school_admin' | 'teacher' | 'bursar' | 'dean_of_studies' | 'custom';
  permissions?: any;
  assigned_classes?: {
    class_id: string;
    subjects: string[];
    periods: string[];
  }[];
  is_active?: boolean;
  school_id: string;
}

export interface TeacherSearchResult {
  _id: string;
  first_name: string;
  last_name: string;
  name: string;
  email: string;
  phone?: string;
  role: string;
  school_ids: string[];
}

// Get all staff for a specific school
export async function getStaffBySchool(schoolId: string): Promise<StaffSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/get-staff-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching staff by school:", response.statusText);
      throw new Error("Failed to fetch staff by school");
    }

    const data = await response.json();
    return data as StaffSchema[];
  } catch (error) {
    console.error("Fetch error (staff by school):", error);
    throw new Error("Failed to fetch staff by school");
  }
}

// Search for existing teachers
export async function searchTeachers(query: string): Promise<TeacherSearchResult[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/search-teachers?query=${encodeURIComponent(query)}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error searching teachers:", response.statusText);
      throw new Error("Failed to search teachers");
    }

    const data = await response.json();
    return data as TeacherSearchResult[];
  } catch (error) {
    console.error("Search error (teachers):", error);
    throw new Error("Failed to search teachers");
  }
}

// Create new staff member
export async function createStaff(staffData: StaffCreateSchema): Promise<{
  user: StaffSchema;
  permissions: StaffPermissionSchema;
  isNewUser: boolean;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/create-staff`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(staffData),
    });

    if (!response.ok) {
      console.error("Error creating staff:", response.statusText);
      throw new Error("Failed to create staff");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Create error (staff):", error);
    throw new Error("Failed to create staff");
  }
}

// Update staff member
export async function updateStaff(staffId: string, staffData: StaffUpdateSchema): Promise<{
  user: StaffSchema;
  permissions: StaffPermissionSchema;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/update-staff/${staffId}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(staffData),
    });

    if (!response.ok) {
      console.error("Error updating staff:", response.statusText);
      throw new Error("Failed to update staff");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Update error (staff):", error);
    throw new Error("Failed to update staff");
  }
}

// Delete staff member (remove from school)
export async function deleteStaff(staffId: string, schoolId: string): Promise<void> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/delete-staff/${staffId}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ school_id: schoolId }),
    });

    if (!response.ok) {
      console.error("Error deleting staff:", response.statusText);
      throw new Error("Failed to delete staff");
    }
  } catch (error) {
    console.error("Delete error (staff):", error);
    throw new Error("Failed to delete staff");
  }
}

// Reset staff password
export async function resetStaffPassword(staffId: string): Promise<void> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/reset-password/${staffId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error resetting staff password:", response.statusText);
      throw new Error("Failed to reset staff password");
    }
  } catch (error) {
    console.error("Reset password error (staff):", error);
    throw new Error("Failed to reset staff password");
  }
}

// Get staff member by ID
export async function getStaffById(staffId: string): Promise<StaffSchema> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/get-staff/${staffId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching staff by ID:", response.statusText);
      throw new Error("Failed to fetch staff by ID");
    }

    const data = await response.json();
    return data as StaffSchema;
  } catch (error) {
    console.error("Fetch error (staff by ID):", error);
    throw new Error("Failed to fetch staff by ID");
  }
}

// Generate access code for teacher
export async function generateAccessCode(teacherId: string, schoolId: string): Promise<{
  access_code: string;
  message: string;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/generate-access-code`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ teacher_id: teacherId, school_id: schoolId }),
    });

    if (!response.ok) {
      console.error("Error generating access code:", response.statusText);
      throw new Error("Failed to generate access code");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Generate access code error:", error);
    throw new Error("Failed to generate access code");
  }
}

// Test Firebase integration
export async function testFirebaseIntegration(): Promise<{
  message: string;
  firebaseConnected: boolean;
  userCount?: number;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/test-firebase`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error testing Firebase integration:", response.statusText);
      throw new Error("Failed to test Firebase integration");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Firebase test error:", error);
    throw new Error("Failed to test Firebase integration");
  }
}

// Sync staff with Firebase
export async function syncStaffWithFirebase(schoolId: string): Promise<{
  message: string;
  totalStaff: number;
  results: Array<{
    userId: string;
    email: string;
    firebaseUid?: string;
    status: string;
    error?: string;
  }>;
}> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/sync-firebase/${schoolId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error syncing staff with Firebase:", response.statusText);
      throw new Error("Failed to sync staff with Firebase");
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Firebase sync error:", error);
    throw new Error("Failed to sync staff with Firebase");
  }
}

// Smart delete staff (e.g., archive or soft-delete from school)
export async function smartDeleteStaff(payload: {
  user_id: string;
  school_id: string;
}): Promise<{ message: string }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/staff/smart-delete-staff`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      console.error("Error smart-deleting staff:", response.statusText);
      throw new Error("Failed to smart-delete staff");
    }

    const data = await response.json();
    return data as { message: string };
  } catch (error) {
    console.error("Smart delete error (staff):", error);
    throw new Error("Failed to smart-delete staff");
  }
}
