"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Percent, ArrowLeft, Plus, Filter, Download, TrendingUp, Users, BookOpen } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import DataTableFix from "@/components/utils/TableFix";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { SchoolAdminGradesSkeleton } from "@/components/skeletons";
import {
  getGradeRecords,
  getGradeStats,
  createGrade,
  updateGrade,
  deleteGrade,
  deleteMultipleGrades,
  exportGradesPDF,
  exportGradesExcel,
  getAvailableTerms,
  GradeRecord,
  GradeStats,
  GradeTerm,
  GradeSequence
} from "@/app/services/GradeServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getExamTypesBySchoolId} from "@/app/services/ExamTypeServices";
import { getSubjectById } from "@/app/services/SubjectServices";
import { getClassById } from "@/app/services/ClassServices";
import { getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";
import GradeModal from "@/components/modals/GradeModal";
import DeleteConfirmationModal from "@/components/modals/DeleteConfirmationModal";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { useRouter } from "next/navigation";
import Link from "next/link";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface TeacherGradeDetailPageProps {
  params: {
    classId: string;
    subjectId: string;
  };
}

export default function TeacherGradeDetailPage({ params }: TeacherGradeDetailPageProps) {
  const { classId, subjectId } = React.use(params);
  const { user, logout } = useAuth();
  const router = useRouter();
  const { toasts, removeToast, showSuccess, showError } = useToast();
  console.log("classId ", classId);
  // State management
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [navigation, setNavigation] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [gradeRecords, setGradeRecords] = useState<GradeRecord[]>([]);
  const [stats, setStats] = useState<GradeStats | null>(null);
  const [classData, setClassData] = useState<any>(null);
  const [subjectData, setSubjectData] = useState<any>(null);

  // Filter states
  const [selectedTerm, setSelectedTerm] = useState('all');
  const [selectedSequence, setSelectedSequence] = useState('all');
  const [selectedExamType, setSelectedExamType] = useState('all');
  const [availableTerms, setAvailableTerms] = useState<any[]>([]);

  // Modal states
  const [isGradeModalOpen, setIsGradeModalOpen] = useState(false);
  const [gradeToEdit, setGradeToEdit] = useState<GradeRecord | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [gradeToDelete, setGradeToDelete] = useState<GradeRecord | null>(null);
  const [selectedGrades, setSelectedGrades] = useState<GradeRecord[]>([]);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [clearSelection, setClearSelection] = useState(false);

  // Form data
  const [students, setStudents] = useState<any[]>([]);
  const [examTypes, setExamTypes] = useState<any[]>([]);

  useEffect(() => {
    if (user) {
      // Get selected school from localStorage
      const storedSchool = localStorage.getItem("teacher_selected_school");
      if (storedSchool) {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
      } else {
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  // Load available terms
  useEffect(() => {
    const loadTerms = async () => {
      if (!selectedSchool) return;

      try {
        const termsData = await getAvailableTerms(selectedSchool.school_id);
        setAvailableTerms(termsData.terms || []);

        // Auto-select current term if available
        if (termsData.current_term && selectedTerm === 'all') {
          setSelectedTerm(termsData.current_term._id);
        }
      } catch (error) {
        console.error('Error loading terms:', error);
      }
    };

    loadTerms();
  }, [selectedSchool]);

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedSchool || !classId || !subjectId) return;

      try {
        setLoading(true);

        // Get navigation items
        const navItems = await getTeacherNavigationItems(selectedSchool.school_id);
        setNavigation(navItems);

        // Fetch all required data in parallel
        const [
          classResponse,
          subjectResponse,
          studentsResponse,
          examTypesResponse,
          termsResponse
        ] = await Promise.all([
          getClassById(classId),
          getSubjectById(subjectId),
          getStudentsByClassAndSchool(classId, selectedSchool.school_id),
          getExamTypesBySchoolId(selectedSchool.school_id),
          getAvailableTerms(selectedSchool.school_id)
        ]);

        setClassData(classResponse);
        setSubjectData(subjectResponse);
        setStudents(studentsResponse);
        setExamTypes(examTypesResponse || []);
        setAvailableTerms(termsResponse.terms || []);

        // Set default term to current term
        const currentTerm = termsResponse.terms?.find((term: any) => term.is_current);
        if (currentTerm) {
          setSelectedTerm(currentTerm._id);
        }

        // Fetch initial grades
        await fetchGrades();

      } catch (error) {
        console.error("Error fetching data:", error);
        showError("Error", "Failed to load page data");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedSchool, classId, subjectId]);

  const fetchGrades = async () => {
    if (!selectedSchool) return;

    try {
      // Build filters
      const filters: any = {
        class_id: classId,
        subject_id: subjectId
      };

      // Enhanced term filtering
      if (selectedTerm !== 'all') {
        filters.term_id = selectedTerm;
      }

      // Sequence filtering
      if (selectedSequence !== 'all') {
        filters.sequence_number = parseInt(selectedSequence);
      }

      // Exam type filtering
      if (selectedExamType !== 'all') {
        filters.exam_type_id = selectedExamType;
      }

      // Fetch grades and stats
      const [gradesResponse, statsResponse] = await Promise.all([
        getGradeRecords(selectedSchool.school_id, filters),
        getGradeStats(selectedSchool.school_id, filters)
      ]);

      setGradeRecords(gradesResponse.grade_records || []);
      setStats(statsResponse.stats);
    } catch (error) {
      console.error("Error fetching grades:", error);
      showError("Error", "Failed to load grades");
    }
  };

  // Refetch grades when filters change
  useEffect(() => {
    if (selectedSchool && classId && subjectId) {
      fetchGrades();
    }
  }, [selectedTerm, selectedSequence, selectedExamType]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const handleBack = () => {
    router.push(`/teacher-dashboard/grades/class/${classId}`);
  };

  // CRUD Functions
  const handleCreateGrade = () => {
    setGradeToEdit(null);
    setIsGradeModalOpen(true);
  };

  const handleEditGrade = (grade: GradeRecord) => {
    setGradeToEdit(grade);
    setIsGradeModalOpen(true);
  };

  const handleDeleteGrade = (grade: GradeRecord) => {
    setGradeToDelete(grade);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  const handleDeleteMultiple = () => {
    setDeleteType("multiple");
    setIsDeleteModalOpen(true);
  };

  const handleSelectionChange = (selectedRows: GradeRecord[]) => {
    setSelectedGrades(selectedRows);
  };

  const handleGradeSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      if (gradeToEdit) {
        // Update existing grade - include school_id as required by backend
        await updateGrade(gradeToEdit._id, {
          ...data,
          school_id: selectedSchool?.school_id
        });
        showSuccess("Success", "Grade updated successfully");
      } else {
        // Create new grade - ensure class and subject are set
        await createGrade({ 
          ...data, 
          school_id: selectedSchool?.school_id,
          class_id: classId,
          subject_id: subjectId
        });
        showSuccess("Success", "Grade created successfully");
      }

      // Refresh grades list
      await fetchGrades();
      setIsGradeModalOpen(false);
      setGradeToEdit(null);
    } catch (error: any) {
      showError("Error", error.message || "Failed to save grade");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteConfirm = async () => {
    setIsSubmitting(true);
    try {
      if (deleteType === "single" && gradeToDelete) {
        await deleteGrade(gradeToDelete._id);
        showSuccess("Success", "Grade deleted successfully");
      } else if (deleteType === "multiple" && selectedGrades.length > 0) {
        const gradeIds = selectedGrades.map(grade => grade._id);
        await deleteMultipleGrades(gradeIds);
        showSuccess("Success", `${gradeIds.length} grades deleted successfully`);
        setClearSelection(true);
      }

      // Refresh grades list
      await fetchGrades();
      setIsDeleteModalOpen(false);
      setGradeToDelete(null);
    } catch (error: any) {
      showError("Error", error.message || "Failed to delete grade(s)");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Export functions
  const handleExportPDF = async () => {
    if (!selectedSchool) return;

    try {
      const filters = {
        class_id: classId,
        subject_id: subjectId,
        ...(selectedTerm !== 'all' && { term_id: selectedTerm }),
        ...(selectedSequence !== 'all' && { sequence_number: parseInt(selectedSequence) }),
        ...(selectedExamType !== 'all' && { exam_type_id: selectedExamType })
      };

      const blob = await exportGradesPDF(selectedSchool.school_id, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to PDF successfully");
    } catch (error: any) {
      showError("Error", error.message || "Failed to export PDF");
    }
  };

  const handleExportExcel = async () => {
    if (!selectedSchool) return;

    try {
      const filters = {
        class_id: classId,
        subject_id: subjectId,
        ...(selectedTerm !== 'all' && { term_id: selectedTerm }),
        ...(selectedSequence !== 'all' && { sequence_number: parseInt(selectedSequence) }),
        ...(selectedExamType !== 'all' && { exam_type_id: selectedExamType })
      };

      const blob = await exportGradesExcel(selectedSchool.school_id, filters);

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `grades_${classData?.name}_${subjectData?.name}_${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("Success", "Grades exported to Excel successfully");
    } catch (error: any) {
      showError("Error", error.message || "Failed to export Excel");
    }
  };

  const getGradeColor = (grade: string) => {
    const splitGrade = grade.split("/")[0];
    switch (splitGrade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'D+':
      case 'D':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'E':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  // Table columns with enhanced term and sequence information
  const columns = [
    { header: "Student", accessor: (row: GradeRecord) => row.student_name },
    {
      header: "Term",
      accessor: (row: GradeRecord) => (
        <div className="text-sm">
          <div className="font-medium">{row.term}</div>
          {row.sequence_name && (
            <div className="text-gray-500 dark:text-gray-400 text-xs">
              {row.sequence_name}
            </div>
          )}
        </div>
      )
    },
    {
      header: "Exam Type",
      accessor: (row: GradeRecord) => row.exam_type || (
        <span className="text-gray-400 italic text-sm">No exam type</span>
      )
    },
    { header: "Score (/20)", accessor: (row: GradeRecord) => `${row.score}/20` },
    { header: "Grade", accessor: (row: GradeRecord) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(row.grade)}`}>
          {row.grade}
        </span>
      )
    },
    { header: "Comments", accessor: (row: GradeRecord) => row.comments || "-" },
    {
      header: "Date",
      accessor: (row: GradeRecord) => new Date(row.date_entered).toLocaleDateString()
    }
  ];

  const actions = [
    {
      label: "Edit",
      onClick: handleEditGrade
    },
    {
      label: "Delete",
      onClick: handleDeleteGrade
    }
  ];

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <SchoolAdminGradesSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Breadcrumb */}
          <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-300">
            <Link
              href="/teacher-dashboard/grades"
              className="hover:text-teal transition-colors"
            >
              Grades
            </Link>
            <span>/</span>
            <Link
              href={`/teacher-dashboard/grades/class/${classId}`}
              className="hover:text-teal transition-colors"
            >
              {classData?.name || "Class"}
            </Link>
            <span>/</span>
            <span className="text-foreground font-medium">
              {subjectData?.name || "Subject"}
            </span>
          </div>

          {/* Header */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              >
                <ArrowLeft className="h-5 w-5 text-foreground" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-foreground">
                  {classData?.name} - {subjectData?.name}
                </h1>
                <p className="text-foreground/60">Manage grades for this class and subject</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleExportPDF}
                className="px-4 py-2 text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>PDF</span>
              </button>
              <button
                onClick={handleExportExcel}
                className="px-4 py-2 text-foreground/70 hover:text-foreground hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-colors flex items-center space-x-2"
              >
                <Download className="h-4 w-4" />
                <span>Excel</span>
              </button>
              <button
                onClick={handleCreateGrade}
                className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors flex items-center space-x-2"
              >
                <Plus className="h-4 w-4" />
                <span>Add Grade</span>
              </button>
            </div>
          </div>

          {/* Stats Cards */}
          {stats && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="bg-widget rounded-lg border border-stroke p-6"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-blue-500/10 rounded-lg flex items-center justify-center">
                    <Users className="h-6 w-6 text-blue-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-foreground/60">Total Grades</p>
                    <p className="text-2xl font-bold text-foreground">{stats.totalGrades}</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
                className="bg-widget rounded-lg border border-stroke p-6"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-green-500/10 rounded-lg flex items-center justify-center">
                    <TrendingUp className="h-6 w-6 text-green-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-foreground/60">Average Score</p>
                    <p className="text-2xl font-bold text-foreground">{stats?.averageScore?.toFixed(1)}/20</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
                className="bg-widget rounded-lg border border-stroke p-6"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-purple-500/10 rounded-lg flex items-center justify-center">
                    <Percent className="h-6 w-6 text-purple-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-foreground/60">Pass Rate</p>
                    <p className="text-2xl font-bold text-foreground">{stats.passRate.toFixed(0)}%</p>
                  </div>
                </div>
              </motion.div>

              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="bg-widget rounded-lg border border-stroke p-6"
              >
                <div className="flex items-center">
                  <div className="w-12 h-12 bg-orange-500/10 rounded-lg flex items-center justify-center">
                    <BookOpen className="h-6 w-6 text-orange-500" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-foreground/60">Highest Score</p>
                    <p className="text-2xl font-bold text-foreground">{stats.highestScore}/20</p>
                  </div>
                </div>
              </motion.div>
            </div>
          )}

          {/* Enhanced Filters with Terms and Sequences */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Term Filter */}
            <div className="flex-1">
              <select
                value={selectedTerm}
                onChange={(e) => {
                  setSelectedTerm(e.target.value);
                  // Reset sequence when term changes
                  setSelectedSequence('all');
                }}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              >
                <option value="all">All Terms</option>
                {availableTerms.map((term: any) => (
                  <option key={term._id} value={term._id}>
                    {term.name} ({term.academic_year})
                    {term.is_current && " - Current"}
                  </option>
                ))}
              </select>
            </div>

            {/* Sequence Filter */}
            <div className="flex-1">
              <select
                value={selectedSequence}
                onChange={(e) => setSelectedSequence(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
                disabled={selectedTerm === 'all'}
              >
                <option value="all">All Sequences</option>
                {selectedTerm !== 'all' &&
                  availableTerms
                    .find((term: any) => term._id === selectedTerm)
                    ?.sequences?.map((sequence: any) => (
                      <option key={sequence.sequence_number} value={sequence.sequence_number}>
                        {sequence.sequence_name}
                      </option>
                    ))
                }
              </select>
            </div>

            {/* Exam Type Filter */}
            <div className="flex-1">
              <select
                value={selectedExamType}
                onChange={(e) => setSelectedExamType(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent"
              >
                <option value="all">All Exam Types</option>
                {examTypes.map((type: any) => (
                  <option key={type._id} value={type._id}>
                    {type.type}
                  </option>
                ))}
              </select>
            </div>
          </div>

          {/* Grades Table */}
          <div className="bg-widget rounded-lg border border-stroke">
            <Suspense fallback={<CircularLoader size={24} color="teal" />}>
              <DataTableFix<GradeRecord>
                data={gradeRecords}
                columns={columns}
                actions={actions}
                defaultItemsPerPage={15}
                onSelectionChange={handleSelectionChange}
                handleDeleteMultiple={handleDeleteMultiple}
                clearSelection={clearSelection}
                onSelectionCleared={() => setClearSelection(false)}
              />
            </Suspense>
          </div>

          <ToastContainer toasts={toasts} onClose={removeToast} />
        </div>

        {/* Grade Modal */}
        <GradeModal
          isOpen={isGradeModalOpen}
          onClose={() => {
            setIsGradeModalOpen(false);
            setGradeToEdit(null);
          }}
          onSubmit={handleGradeSubmit}
          grade={gradeToEdit}
          students={students}
          subjects={[subjectData].filter(Boolean)}
          examTypes={examTypes}
          loading={isSubmitting}
        />

        {/* Delete Confirmation Modal */}
        <DeleteConfirmationModal
          isOpen={isDeleteModalOpen}
          onClose={() => {
            setIsDeleteModalOpen(false);
            setGradeToDelete(null);
          }}
          onConfirm={handleDeleteConfirm}
          title={deleteType === "single" ? "Delete Grade" : "Delete Multiple Grades"}
          message={
            deleteType === "single"
              ? `Are you sure you want to delete this grade for ${gradeToDelete?.student_name}?`
              : `Are you sure you want to delete ${selectedGrades.length} selected grades?`
          }
          loading={isSubmitting}
        />
      </TeacherLayout>
    </ProtectedRoute>
  );
}
