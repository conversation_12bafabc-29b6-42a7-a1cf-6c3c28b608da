import { useState, useCallback } from 'react';

interface UseTableSelectionProps<T> {
  onSelectionChange?: (selection: T[]) => void;
}

export function useTableSelection<T>({ onSelectionChange }: UseTableSelectionProps<T> = {}) {
  const [selectedItems, setSelectedItems] = useState<T[]>([]);
  const [clearSelection, setClearSelection] = useState(false);

  // Handle selection change from DataTable
  const handleSelectionChange = useCallback((selection: T[]) => {
    setSelectedItems(selection);
    onSelectionChange?.(selection);
  }, [onSelectionChange]);

  // Clear selection (triggers DataTable to clear its internal state)
  const clearTableSelection = useCallback(() => {
    setClearSelection(true);
    // Reset the trigger after a brief moment
    setTimeout(() => setClearSelection(false), 100);
  }, []);

  // Callback when selection is actually cleared in DataTable
  const onSelectionCleared = useCallback(() => {
    setSelectedItems([]);
  }, []);

  return {
    selectedItems,
    clearSelection,
    handleSelectionChange,
    clearTableSelection,
    onSelectionCleared,
    hasSelection: selectedItems.length > 0,
    selectionCount: selectedItems.length
  };
}
