"use client";

import React, { useState } from "react";
import { <PERSON><PERSON><PERSON>riangle, X } from "lucide-react";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import CircularLoader from "@/components/widgets/CircularLoader";
import { motion } from "framer-motion";

interface DeleteStaffModalProps {
  staffName: string;
  staffNames?: string[]; // <-- Add this line
  numberOfItems?: number;
  onClose: () => void;
  onDelete: (password: string) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

export default function DeleteStaffModal({
  staffName,
  staffNames, // <-- Add this line
  numberOfItems, // <-- Add this line
  onClose,
  onDelete,
  submitStatus,
  isSubmitting,
}: DeleteStaffModalProps) {
  const [password, setPassword] = useState("");
  const [showPasswordInput, setShowPasswordInput] = useState(false);

  const handleDelete = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!password) {
      alert("Please enter your password to confirm removal.");
      return;
    }
    await onDelete(password);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md mx-4 p-6 relative">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-foreground">Remove Staff Member</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X size={20} />
          </button>
        </div>

        {submitStatus ? (
          <SubmissionFeedback
            status={submitStatus}
            message={
              submitStatus === "success"
                ? "Staff member has been removed successfully!"
                : "There was an error trying to remove the staff member. Try again and if this persists, contact support."
            }
          />
        ) : (
          <div className="space-y-4">
            {!showPasswordInput ? (
              // First confirmation step
              <div>
                <div className="flex items-center space-x-3">
                  <div className="flex-shrink-0">
                    <AlertTriangle className="h-8 w-8 text-red-500" />
                  </div>
                  <div>
                    <p className="text-sm text-foreground">
                      {numberOfItems && numberOfItems > 1
                        ? `Are you sure you want to remove ${numberOfItems} staff members?`
                        : `Are you sure you want to remove the staff member:`}
                    </p>
                    {numberOfItems && numberOfItems > 1 ? (
                      <ul className="text-sm text-foreground mt-2 max-h-32 overflow-y-auto list-disc pl-5">
                        {staffNames?.map((name, idx) => (
                          <li key={idx}>{name}</li>
                        ))}
                      </ul>
                    ) : (
                      <p className="font-medium text-foreground mt-1">
                        {staffName || "No staff selected"}
                      </p>
                    )}
                    <p className="text-sm text-foreground/60 mt-2">
                      This will remove their access to this school. This action cannot be undone.
                    </p>
                  </div>
                </div>

                <div className="flex justify-end space-x-3 pt-4">
                  <button
                    type="button"
                    onClick={onClose}
                    className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    disabled={isSubmitting}
                  >
                    Cancel
                  </button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    onClick={() => setShowPasswordInput(true)}
                    disabled={isSubmitting}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    Continue
                  </motion.button>
                </div>
              </div>
            ) : (
              // Password confirmation step
              <form onSubmit={handleDelete}>
                <div className="mb-4">
                  <p className="text-sm text-foreground mb-4">
                    Please enter your password to confirm the removal of:
                  </p>
                  <p className="font-medium text-foreground mt-1">
                    {numberOfItems && numberOfItems > 1
                      ? null // or show a summary, e.g. `${numberOfItems} staff members`
                      : `"${staffName}"`}
                  </p>

                  <input
                    type="password"
                    placeholder="Enter your password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-red-300 dark:border-red-500 rounded-md text-sm text-foreground bg-widget focus:outline-none focus:ring-2 focus:ring-red-500"
                    required
                    disabled={isSubmitting}
                    autoFocus
                  />
                </div>

                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setShowPasswordInput(false)}
                    className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                    disabled={isSubmitting}
                  >
                    Back
                  </button>
                  <motion.button
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    type="submit"
                    disabled={isSubmitting || !password}
                    className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {isSubmitting && <CircularLoader size={16} color="white" />}
                    <span>Remove Staff</span>
                  </motion.button>
                </div>
              </form>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
