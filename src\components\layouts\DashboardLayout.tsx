import React, { useEffect } from 'react';
import ProtectedRoute from '../auth/ProtectedRoute';
import useAuthGuard from '@/app/hooks/useAuthGuard';
import { Loader2 } from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  requiredRoles?: string[];
}

const LoadingSpinner = () => (
  <div className="min-h-screen flex items-center justify-center bg-gray-50">
    <div className="text-center">
      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
      <p className="text-gray-600">Vérification de l'authentification...</p>
    </div>
  </div>
);

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ 
  children, 
  requiredRoles = [] 
}) => {
  // Utiliser le guard d'authentification
  const { isAuthenticated, loading, user } = useAuthGuard();

  // Afficher le spinner pendant le chargement
  if (loading) {
    return <LoadingSpinner />;
  }

  return (
    <ProtectedRoute 
      requiredRoles={requiredRoles}
      fallback={<LoadingSpinner />}
    >
      <div className="min-h-screen bg-gray-50">
        {/* Header avec info utilisateur */}
        <div className="bg-white shadow-sm border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-3">
              <div className="flex items-center">
                <h1 className="text-lg font-semibold text-gray-900">
                  Dashboard
                </h1>
              </div>
              {user && (
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    Connecté en tant que: <strong>{user.email}</strong>
                  </span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {user.role}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Contenu principal */}
        <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
          {children}
        </main>
      </div>
    </ProtectedRoute>
  );
};

export default DashboardLayout;
