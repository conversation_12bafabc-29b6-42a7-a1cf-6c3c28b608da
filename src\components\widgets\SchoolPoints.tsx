"use client";

import React, { useState, useEffect } from "react";
import { Coins, TrendingUp, TrendingDown } from "lucide-react";
import { motion } from "framer-motion";
import { getSchoolCredits } from "@/app/services/SchoolServices";
import useAuth from "@/app/hooks/useAuth";

interface SchoolPointsProps {
  className?: string;
}

export default function SchoolPoints({ className = "" }: SchoolPointsProps) {
  const { user } = useAuth();
  const [credits, setCredits] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Don't show credits for teachers
  if (user?.role === "teacher" || user?.role === "parent" || user?.role === "super") {
    return null;
  }

  // Get school ID from user
  const schoolId = user?.school_ids?.[0] || user?.school_id;

  useEffect(() => {
    const fetchCredits = async () => {
      if (!schoolId) {
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        console.log("schoolId", schoolId);
        const schoolCredits = await getSchoolCredits(schoolId as string);
        setCredits(schoolCredits);
        setError(null);
      } catch (err) {
        console.error("Error fetching school credits:", err);
        setError("Failed to load credits");
        setCredits(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCredits();
  }, [schoolId]);

  // Format number with commas
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  // Get color based on credit amount
  const getCreditColor = () => {
    if (credits >= 1000) return "text-green-600 dark:text-green-400";
    if (credits >= 500) return "text-blue-600 dark:text-blue-400";
    if (credits >= 100) return "text-yellow-600 dark:text-yellow-400";
    return "text-red-600 dark:text-red-400";
  };

  // Get icon based on credit amount
  const getCreditIcon = () => {
    if (credits >= 500) return <TrendingUp className="h-4 w-4" />;
    if (credits >= 100) return <Coins className="h-4 w-4" />;
    return <TrendingDown className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className={`flex items-center space-x-2 ${className}`}>
        <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
        <div className="hidden sm:block">
          <div className="w-16 h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center space-x-2 text-red-500 ${className}`}>
        <Coins className="h-5 w-5" />
        <span className="hidden sm:block text-sm">Error</span>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`flex items-center space-x-2 ${className}`}
    >
      {/* Mobile View - Icon only */}
      <div className="sm:hidden">
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.95 }}
          className="relative p-2 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg shadow-md"
        >
          <Coins className="h-5 w-5 text-white" />
          <div className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
            {credits > 999 ? '999+' : credits}
          </div>
        </motion.div>
      </div>

      {/* Desktop View - Full display */}
      <div className="hidden sm:flex items-center space-x-3">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-3 py-2 rounded-lg border border-yellow-200 dark:border-yellow-800 shadow-sm"
        >
          <div className="flex items-center space-x-1">
            <div className="p-1.5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-md">
              <Coins className="h-4 w-4 text-white" />
            </div>
            {getCreditIcon()}
          </div>
          
          <div className="flex flex-col">
            <div className="flex items-center space-x-1">
              <span className={`font-bold text-lg ${getCreditColor()}`}>
                {formatNumber(credits)}
              </span>
            <span className="text-xs text-foreground/50">
               Credits
            </span>
            </div>

          </div>
        </motion.div>
      </div>

      {/* Tablet View - Compact */}
      <div className="hidden xs:flex sm:hidden items-center space-x-2">
        <motion.div
          whileHover={{ scale: 1.05 }}
          className="flex items-center space-x-2 bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 px-2 py-1.5 rounded-lg border border-yellow-200 dark:border-yellow-800"
        >
          <div className="p-1 bg-gradient-to-r from-yellow-400 to-orange-500 rounded">
            <Coins className="h-3 w-3 text-white" />
          </div>
          <span className={`font-semibold text-sm ${getCreditColor()}`}>
            {formatNumber(credits)}
          </span>
        </motion.div>
      </div>
    </motion.div>
  );
}
