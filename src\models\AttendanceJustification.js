const mongoose = require('mongoose');

const AttendanceJustificationSchema = new mongoose.Schema(
    {
        attendance_id: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "Attendance",
            required: true,
            unique: true, // One justification per attendance record
        },
        submitted_by: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User", // Could be a parent or staff
            required: true,
        },
        justification_type: {
            type: String,
            enum: ["Text", "File", "TextAndFile"],
            required: true,
        },

        text: {
            type: String,
            default: null,
        },
        file_url: {
            type: String, // e.g., S3 or local path
            default: null,
        },
        status: {
            type: String,
            enum: ["Pending", "Accepted", "Rejected"],
            default: "Pending",
        },
        reviewed_by: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "User", // or "User"
        },
        review_comment: {
            type: String,
        },
    },
    {
        timestamps: true, // For createdAt / updatedAt
    }
);

const AttendanceJustification = mongoose.models.AttendanceJustification || mongoose.model("AttendanceJustification", AttendanceJustificationSchema);

module.exports = AttendanceJustification;
