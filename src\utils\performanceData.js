const School = require('../models/School');
const Grade = require('../models/Grade');
const CreditTransaction = require('../models/CreditTransaction');

const getPerformanceData = async () => {
  const schools = await School.find();

  const results = await Promise.all(
    schools.map(async (school) => {
      // Get total credits bought
      const creditAgg = await CreditTransaction.aggregate([
        { $match: { school_id: school._id } },
        { $group: { _id: null, totalCredits: { $sum: "$credit" } } }
      ]);

      const totalCredits = creditAgg[0]?.totalCredits || 0;

      // Get average score
      const gradeAgg = await Grade.aggregate([ 
        { $match: { school_id: school._id } },
        { $group: { _id: null, averageScore: { $avg: "$score" } } }
      ]);

      // Normalize average score from /20 to /100
      let averageScore = gradeAgg[0]?.averageScore || 0;
      averageScore = (averageScore / 20) * 100;

      return {
        id: school.school_id,
        schoolName: school.name,
        metrics: {
          "Total number Of credits ever bought": totalCredits,
          "Overall Average Grade": Math.round(averageScore),
        },
      };
    })
  );

  return results;
};

module.exports = getPerformanceData;
