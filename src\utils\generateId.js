const crypto = require('crypto');

/**
 * Generate a unique ID with a given prefix
 * @param {string} prefix - The prefix for the ID (e.g., 'LOG', 'USR', 'SCH')
 * @param {mongoose.Model} model - The mongoose model to check for uniqueness
 * @param {string} field - The field name to check for uniqueness
 * @param {number} length - The length of the random part (default: 8)
 * @returns {Promise<string>} - A unique ID
 */
async function ensureUniqueId(prefix, model, field, length = 8) {
  let attempts = 0;
  const maxAttempts = 10;
  
  while (attempts < maxAttempts) {
    try {
      // Generate random string
      const randomBytes = crypto.randomBytes(Math.ceil(length / 2));
      const randomString = randomBytes.toString('hex').substring(0, length).toUpperCase();
      
      // Create the ID with prefix
      const id = `${prefix}_${randomString}`;
      
      // Check if this ID already exists
      const existing = await model.findOne({ [field]: id }).exec();
      
      if (!existing) {
        return id;
      }
      
      attempts++;
    } catch (error) {
      console.error('Error generating unique ID:', error);
      attempts++;
    }
  }
  
  // Fallback: use timestamp + random if all attempts failed
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(4).toString('hex').toUpperCase();
  return `${prefix}_${timestamp}_${random}`;
}

/**
 * Generate a simple random ID without uniqueness check
 * @param {string} prefix - The prefix for the ID
 * @param {number} length - The length of the random part (default: 8)
 * @returns {string} - A random ID
 */
function generateRandomId(prefix, length = 8) {
  const randomBytes = crypto.randomBytes(Math.ceil(length / 2));
  const randomString = randomBytes.toString('hex').substring(0, length).toUpperCase();
  return `${prefix}_${randomString}`;
}

/**
 * Generate a timestamp-based ID
 * @param {string} prefix - The prefix for the ID
 * @returns {string} - A timestamp-based ID
 */
function generateTimestampId(prefix) {
  const timestamp = Date.now().toString(36);
  const random = crypto.randomBytes(2).toString('hex').toUpperCase();
  return `${prefix}_${timestamp}_${random}`;
}

module.exports = {
  ensureUniqueId,
  generateRandomId,
  generateTimestampId
};
