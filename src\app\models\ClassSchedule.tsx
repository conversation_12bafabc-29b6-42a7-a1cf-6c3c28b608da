export type ScheduleType = 'Normal' | 'Exam' | 'Event';
export type DayOfWeek =
  | 'Monday'
  | 'Tuesday'
  | 'Wednesday'
  | 'Thursday'
  | 'Friday'
  | 'Saturday'
  | 'Sunday';

export interface ClassScheduleSchema extends Record<string, unknown> {
  _id: string;
  school_id: string;            // MongoDB ObjectId as string
  class_id: string;             // MongoDB ObjectId as string
  subject_id: string;           // MongoDB ObjectId as string
  schedule_type: ScheduleType;
  period_id: string;            // MongoDB ObjectId as string
  teacher_id?: string;          // MongoDB ObjectId as string (optional)
  day_of_week: DayOfWeek;
  createdAt?: string;           // Auto-generated timestamp
  updatedAt?: string;           // Auto-generated timestamp
}

export interface ClassScheduleCreateSchema extends Record<string, unknown> {
  school_id: string;            // Required
  class_id: string;             // Required
  subject_id: string;           // Required
  schedule_type: ScheduleType; // Required
  period_id: string;            // Required
  day_of_week: DayOfWeek;       // Required
  teacher_id?: string;          // Optional
}

export interface ClassScheduleUpdateSchema extends Record<string, unknown> {
  _id: string;                  // Required for identifying the schedule
  school_id?: string;
  class_id?: string;
  subject_id?: string;
  schedule_type?: ScheduleType;
  period_id?: string;
  day_of_week?: DayOfWeek;
  teacher_id?: string;
}

export interface ClassScheduleDeleteSchema extends Record<string, unknown> {
  _id: string;                  // Required MongoDB ID to delete
}

export interface PopulatedClassSchedule extends Record<string, unknown> {
  _id: string;
  school_id: {
    _id: string;
    name: string;
  };
  class_id: {
    _id: string;
    name: string;
    class_code: string;
  };
  subject_id: {
    _id: string;
    name: string;
    subject_code: string;
  };
  schedule_type: ScheduleType;
  period_id: {
    _id: string;
    period_number: number;
    start_time: string; // HH:mm:ss
    end_time: string;
  };
  teacher_id?: {
    _id: string;
    name: string;
    email?: string;
  };
  day_of_week: DayOfWeek;
  createdAt?: string;
  updatedAt?: string;
}
