# Système de Souscription par Crédits - Scholarify

## 🎯 Vue d'ensemble

Le système de souscription par crédits de Scholarify permet aux écoles d'acheter des crédits pour utiliser les fonctionnalités de la plateforme. Chaque action consomme des crédits selon un modèle de tarification transparent.

## 💰 Modèle de Tarification

### Prix de Base
- **1 crédit = 3000 FCFA**
- **1 étudiant créé = 1 crédit**
- **1 message chatbot = 1 crédit** (plan Standard uniquement)

### Plans Disponibles

#### 🌱 Plan Basic (3000 FCFA/crédit)
- Gestion complète des étudiants
- Gestion des classes et emplois du temps
- Suivi des présences et notes
- Rapports de base
- Support par email

#### ⭐ Plan Standard (3000 FCFA/crédit + chatbot)
- Toutes les fonctionnalités du plan Basic
- **Chatbot IA** (1 crédit/message)
- Rapports avancés
- Support prioritaire
- Mises à jour en avant-première

#### 👑 Plan Custom (Sur mesure)
- Toutes les fonctionnalités Standard
- Développement sur mesure
- Intégrations personnalisées
- Support dédié 24/7
- Tarification négociée

## 🏗️ Architecture Technique

### Modèles de Données

#### SchoolSubscription
```javascript
{
  school_id: ObjectId,
  plan_type: 'basic' | 'standard' | 'custom',
  status: 'active' | 'expired' | 'suspended' | 'trial',
  credits_balance: Number,
  credits_purchased: Number,
  credits_used: Number,
  features: [String],
  // ... autres champs
}
```

#### SubscriptionPlan
```javascript
{
  plan_name: 'basic' | 'standard' | 'custom',
  display_name: String,
  price_per_credit: Number,
  chatbot_enabled: Boolean,
  features: [FeatureSchema],
  benefits: [String],
  // ... autres champs
}
```

#### CreditPurchase
```javascript
{
  school_id: ObjectId,
  credits_purchased: Number,
  total_amount: Number,
  payment_status: 'pending' | 'completed' | 'failed',
  transaction_id: String,
  // ... autres champs
}
```

#### CreditUsage
```javascript
{
  school_id: ObjectId,
  usage_type: 'student_creation' | 'chatbot_message',
  credits_used: Number,
  description: String,
  balance_before: Number,
  balance_after: Number,
  // ... autres champs
}
```

### API Endpoints

#### Souscriptions d'École
- `GET /school-subscription/:school_id` - Obtenir la souscription
- `PUT /school-subscription/:school_id` - Mettre à jour la souscription
- `GET /school-subscription/:school_id/stats` - Statistiques d'utilisation
- `POST /school-subscription/:school_id/credits/deduct` - Déduire des crédits

#### Achat de Crédits
- `POST /credit-purchase/initiate` - Initier un achat
- `POST /credit-purchase/confirm` - Confirmer un paiement
- `GET /credit-purchase/school/:school_id/history` - Historique des achats

#### Plans de Souscription
- `GET /subscription-plans` - Obtenir tous les plans
- `GET /subscription-plans/compare/all` - Comparer les plans
- `GET /subscription-plans/pricing/calculate` - Calculer un prix

## 🖥️ Interface Utilisateur

### Page /pricing
- Présentation des 3 plans
- Calculateur de coûts interactif
- Comparaison détaillée des fonctionnalités
- FAQ complète
- Appels à l'action pour chaque plan

### Page /school-admin/buy-credit
- Vue d'ensemble de la souscription actuelle
- Solde de crédits en temps réel
- Statistiques d'utilisation
- Modal d'achat de crédits
- Historique des transactions

### Composants Clés
- `PricingCard` - Carte de présentation d'un plan
- `PricingCalculator` - Calculateur de coûts
- `CreditPurchaseModal` - Modal d'achat de crédits
- `PricingFAQ` - Questions fréquentes

## 🔄 Flux de Fonctionnement

### 1. Inscription d'une École
1. L'école s'inscrit sur la plateforme
2. Une souscription Basic est créée automatiquement
3. 5 crédits gratuits sont offerts
4. L'école peut commencer à utiliser la plateforme

### 2. Utilisation des Crédits
1. **Création d'étudiant** : 1 crédit déduit automatiquement
2. **Message chatbot** : 1 crédit déduit par message (plan Standard)
3. Vérification du solde avant chaque action
4. Blocage si solde insuffisant

### 3. Achat de Crédits
1. L'admin clique sur "Acheter des crédits"
2. Sélection du nombre de crédits
3. Saisie des informations de facturation
4. Redirection vers Fapshi pour le paiement
5. Confirmation et ajout des crédits au solde

### 4. Suivi et Rapports
1. Enregistrement de chaque utilisation
2. Génération de statistiques détaillées
3. Alertes de solde faible
4. Historique complet des transactions

## 🔧 Installation et Configuration

### 1. Initialiser les Plans
```bash
node src/scripts/initializeSubscriptionPlans.js
```

### 2. Créer des Souscriptions pour les Écoles Existantes
```bash
# Pour toutes les écoles
node src/scripts/createSchoolSubscription.js

# Pour une école spécifique
node src/scripts/createSchoolSubscription.js <school_id>
```

### 3. Variables d'Environnement
```env
FAPSHI_API_KEY=your_fapshi_api_key
FAPSHI_SECRET=your_fapshi_secret
FRONTEND_URL=http://localhost:3000
```

## 📊 Métriques et Suivi

### Métriques Clés
- **Taux de conversion** : Visiteurs → Utilisateurs payants
- **ARPU** (Average Revenue Per User) : Revenus moyens par école
- **Churn Rate** : Taux d'abandon des écoles
- **Utilisation moyenne** : Crédits utilisés par école/mois

### Rapports Disponibles
- Utilisation quotidienne/mensuelle des crédits
- Répartition par type d'usage (étudiants vs chatbot)
- Efficacité d'utilisation par école
- Prévisions de consommation

## 🚀 Fonctionnalités Avancées

### Système d'Alertes
- Alerte à 10 crédits restants (configurable)
- Notification par email des achats
- Rappels de recharge automatiques

### Recommandations Intelligentes
- Analyse de l'utilisation pour suggérer le plan optimal
- Prédictions de consommation future
- Optimisation des coûts

### Intégration Paiement
- Support Fapshi (Orange Money, MTN Mobile Money)
- Webhooks pour confirmation automatique
- Gestion des échecs de paiement

## 🔒 Sécurité et Conformité

### Sécurité des Données
- Chiffrement SSL pour toutes les transactions
- Validation stricte des entrées utilisateur
- Logs d'audit pour toutes les opérations

### Conformité
- Respect des normes de protection des données
- Transparence totale sur l'utilisation des crédits
- Possibilité d'export des données

## 📈 Évolutions Futures

### Fonctionnalités Prévues
- **Crédits partagés** : Partage entre écoles d'un même groupe
- **Abonnements mensuels** : Alternative aux crédits prépayés
- **API publique** : Intégration avec des systèmes tiers
- **Programme de fidélité** : Réductions pour les gros utilisateurs

### Optimisations Techniques
- Cache Redis pour les vérifications de solde
- Traitement asynchrone des déductions
- Analytics en temps réel
- Notifications push mobiles

## 🆘 Support et Maintenance

### Monitoring
- Surveillance des soldes de crédits
- Alertes sur les échecs de paiement
- Métriques de performance en temps réel

### Support Client
- Documentation complète
- Chat support intégré
- Assistance à la migration
- Formation utilisateur

---

**Note** : Ce système a été conçu pour être évolutif et transparent, permettant aux écoles de contrôler précisément leurs coûts tout en bénéficiant d'une plateforme moderne et complète.
