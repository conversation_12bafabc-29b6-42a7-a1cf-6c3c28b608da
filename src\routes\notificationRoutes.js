const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

// Test route
router.get('/test', notificationController.testNotificationResponse);

// GET user notifications
router.get('/user/notifications',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher', 'parent']),
  notificationController.getUserNotifications
);

// GET notification statistics
router.get('/user/stats',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher', 'parent']),
  notificationController.getNotificationStats
);

// PUT mark notification as read
router.put('/mark-read/:notificationId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher', 'parent']),
  notificationController.markNotificationAsRead
);

// PUT mark all notifications as read
router.put('/mark-all-read',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher', 'parent']),
  notificationController.markAllNotificationsAsRead
);

// DELETE notification
router.delete('/delete/:notificationId',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'bursar', 'dean_of_studies', 'teacher', 'parent']),
  notificationController.deleteNotification
);

// POST create notification (admin only)
router.post('/create',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin']),
  notificationController.createNotification
);

module.exports = router;
