"use client";

import React, { useState, useEffect } from "react";
import { X, Calendar, Clock, BookOpen, Users, Check, AlertCircle } from "lucide-react";

interface TeacherAttendanceModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  attendance?: any;
  isEditing?: boolean;
  classInfo: {
    _id: string;
    name: string;
    level: string;
    section: string;
  };
  subjects: any[];
  periods: any[];
  schedules: any[];
  students: any[];
  loading?: boolean;
}

// Days of the week
const DAYS_OF_WEEK = [
  'Monday',
  'Tuesday', 
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

export default function TeacherAttendanceModal({
  isOpen,
  onClose,
  onSubmit,
  attendance,
  isEditing = false,
  classInfo,
  subjects,
  periods,
  schedules,
  students,
  loading = false
}: TeacherAttendanceModalProps) {
  
  // Form state
  const [formData, setFormData] = useState({
    day_of_week: "",
    period_id: "",
    subject_id: "",
    date: new Date().toISOString().split('T')[0],
    academic_year: new Date().toString()
  });

  // Student attendance state
  const [studentAttendance, setStudentAttendance] = useState<Record<string, string>>({});
  
  // UI states
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitting, setSubmitting] = useState(false);
  const [bulkStatus, setBulkStatus] = useState<'Present' | 'Absent' | 'Late' | 'Excused'>('Present');

  // Initialize form data
  console.log("editing schedule data", schedules," and attendence ",attendance)
  useEffect(() => {
    if (isOpen) {
      if (isEditing && attendance) {
        console.log("🔍 Initializing modal for editing:", attendance);

        // For editing, extract data from attendance record
        let scheduleData = null;
        let dayOfWeek = "";
        let periodId = "";
        let subjectId = "";
        
        // If attendance has schedule_id, find the corresponding schedule
        if (attendance.schedule_id) {
          scheduleData = schedules.find(s => s._id === attendance.schedule_id);

          if (scheduleData) {
            dayOfWeek = scheduleData.day || ""; // Use 'day' not 'day_of_week'
            periodId = scheduleData.period_id || ""; // Use period_id not schedule._id
            subjectId = scheduleData.subject_id || "";
          }
        }
        
        // If no schedule found, try to extract from attendance directly
        if (!scheduleData) {
          dayOfWeek = attendance.day_of_week || "";
          periodId = attendance.period_id || "";
          subjectId = attendance.subject_id || "";
          
          // Try to find by names if IDs not available
          if (!subjectId && attendance.subject_name) {
            const foundSubject = subjects.find(s => s.name === attendance.subject_name);
            subjectId = foundSubject?._id || "";
          }
          
          // Try to find a schedule that matches
          if (subjectId && attendance.period_number) {
            const matchingSchedule = schedules.find(s => {
              const period = periods.find(p => p._id === s.period_id);
              return s.subject_id === subjectId &&
                     period?.period_number === attendance.period_number;
            });

            if (matchingSchedule) {
              periodId = matchingSchedule.period_id || ""; // Use period_id not schedule._id
              dayOfWeek = matchingSchedule.day || ""; // Use 'day' not 'day_of_week'
            }
          }
        }

        // Format date properly
        let formattedDate = new Date().toISOString().split('T')[0];
        if (attendance.date) {
          const dateObj = new Date(attendance.date);
          if (!isNaN(dateObj.getTime())) {
            formattedDate = dateObj.toISOString().split('T')[0];
          }
        }

        console.log("🔍 Extracted data for form:", {
          dayOfWeek,
          periodId,
          subjectId,
          formattedDate,
          scheduleData
        });

        setFormData({
          day_of_week: dayOfWeek,
          period_id: periodId,
          subject_id: subjectId,
          date: formattedDate,
          academic_year: attendance.academic_year || "2024-2025"
        });
        
        // Initialize student attendance from existing data
        const attendanceMap: Record<string, string> = {};
        
        if (attendance.students && Array.isArray(attendance.students)) {
          // Multiple students (bulk attendance)
          attendance.students.forEach((student: any) => {
            attendanceMap[student.student_id] = student.status;
          });
        } else if (attendance.student_id && attendance.status) {
          // Single student attendance
          attendanceMap[attendance.student_id] = attendance.status;
        }
        
        setStudentAttendance(attendanceMap);
        
      } else {
        // Reset form for new attendance
        const today = new Date();
        const dayOfWeek = today.toLocaleDateString('en-US', { weekday: 'long' });
        
        setFormData({
          day_of_week: dayOfWeek,
          period_id: "",
          subject_id: "",
          date: new Date().toISOString().split('T')[0],
          academic_year: `${new Date().getFullYear() } - ${(new Date().getFullYear() + 1)}`
        });
        setStudentAttendance({});
      }
      setErrors({});
    }
  }, [isOpen, isEditing, attendance, schedules, subjects, periods]);

  // Get periods for selected day and subject
  const getAvailablePeriods = () => {
    if (!formData.day_of_week || !formData.subject_id) {
      console.log("Cannot get periods: missing day_of_week or subject_id", {
        day_of_week: formData.day_of_week,
        subject_id: formData.subject_id
      });
      return [];
    }

    const availablePeriods = schedules.filter(schedule =>
      schedule.day === formData.day_of_week &&
      schedule.subject_id === formData.subject_id
    );

    console.log("Available periods for", {
      day: formData.day_of_week,
      subject: formData.subject_id,
      periods: availablePeriods
    });

    return availablePeriods;
  };

  // Get subjects for selected day
  const getAvailableSubjects = () => {
    if (!formData.day_of_week) {
      console.log("Cannot get subjects: no day selected");
      return subjects;
    }

    const daySchedules = schedules.filter(s => s.day === formData.day_of_week);
    const subjectIds = [...new Set(daySchedules.map(s => s.subject_id))];
    const availableSubjects = subjects.filter(subject => subjectIds.includes(subject._id));
    console.log("modal subject ",subjectIds, "subjects ", subjects)
    console.log("Available subjects for", formData.day_of_week, ":", {
      totalSubjects: subjects.length,
      availableSubjects: availableSubjects.length,
      subjectNames: availableSubjects.map(s => s.name)
    });

    return availableSubjects;
  };

  // Get period details for display
  const getPeriodDisplayInfo = (schedule: any) => {
    const period = periods.find(p => p._id === schedule.period_id);
    if (!period) {
      return {
        display: "Unknown Period",
        number: 0,
        time: ""
      };
    }

    const timeRange = period.start_time && period.end_time
      ? `${period.start_time.slice(0,5)}-${period.end_time.slice(0,5)}`
      : "";

    return {
      display: `Period ${period.period_number}${timeRange ? ` (${timeRange})` : ""}`,
      number: period.period_number,
      time: timeRange
    };
  };

  // Handle input changes
  const handleInputChange = (field: string, value: string) => {
    console.log(`🔄 Field changed: ${field} -> ${value}`);

    setFormData(prev => ({ ...prev, [field]: value }));

    // Clear related fields when parent changes (only for new attendance)
    if (!isEditing) {
      if (field === 'day_of_week') {
        console.log("📅 Day changed, clearing subject and period selections");
        setFormData(prev => ({ ...prev, period_id: "", subject_id: "" }));
        setStudentAttendance({});
      } else if (field === 'subject_id') {
        console.log("📚 Subject changed, clearing period selection");
        setFormData(prev => ({ ...prev, period_id: "" }));
      }
    }

    // Clear errors
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }

    // Log available options after change
    if (field === 'day_of_week' && value) {
      setTimeout(() => {
        const availableSubjects = getAvailableSubjects();
        console.log(`📚 Available subjects for ${value}:`, availableSubjects.map(s => s.name));
      }, 0);
    } else if (field === 'subject_id' && value) {
      setTimeout(() => {
        const availablePeriods = getAvailablePeriods();
        console.log(`⏰ Available periods for subject:`, availablePeriods.length);
      }, 0);
    }
  };

  // Handle student attendance change
  const handleStudentAttendanceChange = (studentId: string, status: string) => {
    setStudentAttendance(prev => ({
      ...prev,
      [studentId]: status
    }));
  };

  // Apply bulk status to all students
  const applyBulkStatus = () => {
    const newAttendance: Record<string, string> = {};
    
    students.forEach(student => {
      newAttendance[student._id] = bulkStatus;
    });
    
    setStudentAttendance(newAttendance);
  };

  // Validation
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.day_of_week) {
      newErrors.day_of_week = "Please select a day";
    }
    
    if (!formData.period_id) {
      newErrors.period_id = "Please select a period";
    }
    
    if (!formData.subject_id) {
      newErrors.subject_id = "Please select a subject";
    }

    if (!formData.date) {
      newErrors.date = "Please select a date";
    }

    if (!formData.academic_year) {
      newErrors.academic_year = "Please enter academic year";
    }

    // Check if at least one student has attendance marked
    const hasAttendance = Object.keys(studentAttendance).length > 0;
    if (!hasAttendance) {
      newErrors.students = "Please mark attendance for at least one student";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setSubmitting(true);
    
    try {
      console.log("🔍 Form submission data:", formData);
      console.log("🔍 Available schedules:", schedules);

      // The period_id should now be the actual period_id from the schedule
      let actualPeriodId = formData.period_id;

      // Validate that we have a valid period_id
      if (!actualPeriodId) {
        console.error("❌ No period_id provided!");
        throw new Error("Please select a period");
      }

      // Verify the period_id exists in our periods array
      const selectedPeriod = periods.find(p => p._id === actualPeriodId);
      if (!selectedPeriod) {
        console.error("❌ Invalid period_id:", actualPeriodId);
        throw new Error("Invalid period selected");
      }

      console.log("✅ Using period_id:", actualPeriodId);
      console.log("✅ Period details:", selectedPeriod);

      if (isEditing && attendance) {
        // For editing, we update the attendance record
        const studentEntries = Object.entries(studentAttendance);
        
        if (studentEntries.length === 1) {
          // Single student update
          const [studentId, status] = studentEntries[0];
          const updateData = {
            status: status,
            date: formData.date,
            academic_year: formData.academic_year
          };
          
          await onSubmit(updateData);
        } else {
          // Multiple students - treat as new attendance creation
          const attendanceData = {
            day_of_week: formData.day_of_week,
            class_id: classInfo._id,
            period_id: actualPeriodId,
            subject_id: formData.subject_id,
            date: formData.date,
            academic_year: formData.academic_year,
            students: Object.entries(studentAttendance).map(([studentId, status]) => ({
              student_id: studentId,
              status: status
            }))
          };
          
          await onSubmit(attendanceData);
        }
      } else {
        // Creating new attendance
        const attendanceData = {
          day_of_week: formData.day_of_week,
          class_id: classInfo._id,
          period_id: actualPeriodId,
          subject_id: formData.subject_id,
          date: formData.date,
          academic_year: formData.academic_year,
          students: Object.entries(studentAttendance).map(([studentId, status]) => ({
            student_id: studentId,
            status: status
          }))
        };

        console.log("📤 Submitting attendance data:", attendanceData);
        await onSubmit(attendanceData);
      }
      
      onClose();
    } catch (error) {
      console.error("Error submitting attendance:", error);
      setErrors({ submit: "Failed to save attendance. Please try again." });
    } finally {
      setSubmitting(false);
    }
  };

  // Helper functions
  const getSelectedPeriodName = () => {
    const selectedSchedule = schedules.find(s => s._id === formData.period_id);
    if (selectedSchedule) {
      const period = periods.find(p => p._id === selectedSchedule.period_id);
      return period ? `Period ${period.period_number} (${period.start_time?.slice(0,5)}-${period.end_time?.slice(0,5)})` : "Unknown Period";
    }
    return "";
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(s => s._id === formData.subject_id);
    return selectedSubject?.name || "";
  };

  // For editing individual attendance, show only the specific student
  const studentsToShow = isEditing && attendance && attendance.student_id 
    ? students.filter(student => student._id === attendance.student_id)
    : students;

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div className="bg-widget rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-stroke">
          <div>
            <h2 className="text-xl font-semibold text-foreground">
              {isEditing ? "Edit Attendance" : "Mark Attendance"}
            </h2>
            <p className="text-sm text-foreground/60 mt-1">
              {classInfo.name} - {classInfo.level}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-md transition-colors"
            disabled={submitting}
          >
            <X className="h-5 w-5 text-foreground/60" />
          </button>
        </div>

        <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            {/* Day, Period and Subject Selection */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Day Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Day <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.day_of_week}
                  onChange={(e) => handleInputChange("day_of_week", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground ${
                    errors.day_of_week
                      ? "border-red-500"
                      : "border-stroke"
                  } ${isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={isEditing || submitting}
                >
                  <option value="">Select day</option>
                  {DAYS_OF_WEEK.map((day) => (
                    <option key={day} value={day}>
                      {day}
                    </option>
                  ))}
                </select>
                {errors.day_of_week && (
                  <p className="mt-1 text-sm text-red-500">{errors.day_of_week}</p>
                )}
              </div>

              {/* Subject Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Subject <span className="text-red-500">*</span>
                  {!formData.day_of_week && (
                    <span className="text-xs text-foreground/60 ml-2">(Select day first)</span>
                  )}
                </label>
                <select
                  value={formData.subject_id}
                  onChange={(e) => handleInputChange("subject_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground ${
                    errors.subject_id
                      ? "border-red-500"
                      : "border-stroke"
                  } ${!formData.day_of_week || isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={!formData.day_of_week || isEditing || submitting}
                >
                  <option value="">
                    {formData.day_of_week
                      ? `Select subject (${getAvailableSubjects().length} available)`
                      : "Select day first"}
                  </option>
                  {getAvailableSubjects().map((subject) => (
                    <option key={subject._id} value={subject._id}>
                      {subject.name}
                    </option>
                  ))}
                </select>
                {errors.subject_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.subject_id}</p>
                )}
                {formData.day_of_week && getAvailableSubjects().length === 0 && (
                  <p className="mt-1 text-sm text-yellow-600">
                    No subjects available for {formData.day_of_week}
                  </p>
                )}
              </div>

              {/* Period Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Period <span className="text-red-500">*</span>
                  {(!formData.day_of_week || !formData.subject_id) && (
                    <span className="text-xs text-foreground/60 ml-2">(Select day and subject first)</span>
                  )}
                </label>
                <select
                  value={formData.period_id}
                  onChange={(e) => handleInputChange("period_id", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground ${
                    errors.period_id
                      ? "border-red-500"
                      : "border-stroke"
                  } ${!formData.day_of_week || !formData.subject_id || isEditing ? 'opacity-50 cursor-not-allowed' : ''}`}
                  disabled={!formData.day_of_week || !formData.subject_id || isEditing || submitting}
                >
                  <option value="">
                    {formData.day_of_week && formData.subject_id
                      ? `Select period (${getAvailablePeriods().length} available)`
                      : "Complete previous selections first"}
                  </option>
                  {getAvailablePeriods().map((schedule, index) => {
                    const periodInfo = getPeriodDisplayInfo(schedule);
                    // Use period_id as the value since that's what we need for the backend
                    const scheduleKey = schedule.period_id || `schedule-${index}`;
                    return (
                      <option key={scheduleKey} value={schedule.period_id}>
                        {periodInfo.display}
                      </option>
                    );
                  })}
                </select>
                {errors.period_id && (
                  <p className="mt-1 text-sm text-red-500">{errors.period_id}</p>
                )}
                {formData.day_of_week && formData.subject_id && getAvailablePeriods().length === 0 && (
                  <p className="mt-1 text-sm text-yellow-600">
                    No periods available for this subject on {formData.day_of_week}
                  </p>
                )}
              </div>
            </div>

            {/* Date and Academic Year */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Date <span className="text-red-500">*</span>
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => handleInputChange("date", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground ${
                    errors.date
                      ? "border-red-500"
                      : "border-stroke"
                  }`}
                  disabled={submitting}
                />
                {errors.date && (
                  <p className="mt-1 text-sm text-red-500">{errors.date}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Academic Year <span className="text-red-500">*</span>
                </label>
                <input
                  type="text"
                  value={formData.academic_year}
                  onChange={(e) => handleInputChange("academic_year", e.target.value)}
                  placeholder="e.g., 2024-2025"
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground ${
                    errors.academic_year
                      ? "border-red-500"
                      : "border-stroke"
                  }`}
                  disabled={submitting}
                />
                {errors.academic_year && (
                  <p className="mt-1 text-sm text-red-500">{errors.academic_year}</p>
                )}
              </div>
            </div>

            {/* Students Attendance Section */}
            {formData.day_of_week && formData.period_id && formData.subject_id && (
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
                  <h4 className="text-lg font-medium text-foreground">
                    {isEditing ? "Edit Student Attendance" : `Students Attendance (${studentsToShow.length} students)`}
                  </h4>

                  {/* Bulk Actions - Hide when editing individual attendance */}
                  {!isEditing && (
                    <div className="flex items-center space-x-3">
                      <select
                        value={bulkStatus}
                        onChange={(e) => setBulkStatus(e.target.value as any)}
                        className="px-3 py-1 text-sm border border-stroke rounded bg-widget text-foreground focus:outline-none focus:ring-2 focus:ring-teal"
                        disabled={submitting}
                      >
                        <option value="Present">Present</option>
                        <option value="Absent">Absent</option>
                        <option value="Late">Late</option>
                        <option value="Excused">Excused</option>
                      </select>
                      <button
                        type="button"
                        onClick={applyBulkStatus}
                        className="px-3 py-1 text-sm bg-teal text-white rounded hover:bg-teal-600 transition-colors"
                        disabled={studentsToShow.length === 0 || submitting}
                      >
                        Apply to All
                      </button>
                    </div>
                  )}
                </div>

                {studentsToShow.length > 0 ? (
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {studentsToShow.map((student) => (
                      <div
                        key={student._id}
                        className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 border border-stroke rounded-lg space-y-3 sm:space-y-0"
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-teal/10 rounded-full flex items-center justify-center">
                            <Users className="h-5 w-5 text-teal" />
                          </div>
                          <div>
                            <h5 className="font-medium text-foreground">
                              {student.first_name} {student.last_name}
                            </h5>
                            <p className="text-sm text-foreground/60">
                              Roll: {student.roll_number || "N/A"}
                            </p>
                          </div>
                        </div>

                        <div className="flex flex-wrap gap-2">
                          {['Present', 'Absent', 'Late', 'Excused'].map((status) => (
                            <label
                              key={status}
                              className={`flex items-center space-x-2 px-3 py-2 rounded-md border cursor-pointer transition-colors ${
                                studentAttendance[student._id] === status
                                  ? status === 'Present'
                                    ? 'bg-green-100 border-green-300 text-green-800 dark:bg-green-900/30 dark:border-green-600 dark:text-green-300'
                                    : status === 'Absent'
                                    ? 'bg-red-100 border-red-300 text-red-800 dark:bg-red-900/30 dark:border-red-600 dark:text-red-300'
                                    : status === 'Late'
                                    ? 'bg-yellow-100 border-yellow-300 text-yellow-800 dark:bg-yellow-900/30 dark:border-yellow-600 dark:text-yellow-300'
                                    : 'bg-blue-100 border-blue-300 text-blue-800 dark:bg-blue-900/30 dark:border-blue-600 dark:text-blue-300'
                                  : 'bg-widget border-stroke text-foreground hover:bg-gray-50 dark:hover:bg-gray-800'
                              }`}
                            >
                              <input
                                type="radio"
                                name={`attendance-${student._id}`}
                                value={status}
                                checked={studentAttendance[student._id] === status}
                                onChange={(e) => handleStudentAttendanceChange(student._id, e.target.value)}
                                className="sr-only"
                                disabled={submitting}
                              />
                              <span className="text-sm font-medium">{status}</span>
                              {studentAttendance[student._id] === status && (
                                <Check className="h-4 w-4" />
                              )}
                            </label>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Users className="h-12 w-12 text-foreground/30 mx-auto mb-3" />
                    <p className="text-foreground/60">No students found for this class</p>
                  </div>
                )}

                {errors.students && (
                  <p className="text-sm text-red-500 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    {errors.students}
                  </p>
                )}
              </div>
            )}

            {errors.submit && (
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                <p className="text-sm text-red-600 dark:text-red-400 flex items-center">
                  <AlertCircle className="h-4 w-4 mr-2" />
                  {errors.submit}
                </p>
              </div>
            )}
          </form>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-stroke bg-gray-50 dark:bg-gray-800/50">
          <button
            type="button"
            onClick={onClose}
            className="px-4 py-2 text-foreground/60 hover:text-foreground transition-colors"
            disabled={submitting}
          >
            Cancel
          </button>
          <button
            onClick={handleSubmit}
            disabled={submitting || loading}
            className="flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {submitting ? (
              <>
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Check className="h-4 w-4" />
                <span>{isEditing ? "Update Attendance" : "Mark Attendance"}</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
