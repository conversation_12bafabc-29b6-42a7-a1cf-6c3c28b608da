import { getTokenFromCookie } from "./UserServices";


const BASE_API_URL = process.env.NEXT_PUBLIC_API_URL || "https://scolarify.onrender.com/api";

export interface ActivityLogEntry {
  _id: string;
  log_id: string;
  user_id: {
    _id: string;
    first_name: string;
    last_name: string;
    email: string;
    role: string;
  };
  school_id: {
    _id: string;
    name: string;
  };
  action: string;
  target_type: string;
  target_id: string;
  target_name: string;
  details: Record<string, any>;
  ip_address: string;
  user_agent: string;
  createdAt: string;
  updatedAt: string;
}

export interface ActivityLogFilters {
  user_id?: string;
  school_id?: string;
  action?: string;
  target_type?: string;
  limit?: number;
}

// Get recent activities for a user/school
export async function getRecentActivities(filters: ActivityLogFilters = {}): Promise<ActivityLogEntry[]> {
  const token = getTokenFromCookie("idToken");

  try {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });

    const queryString = queryParams.toString();
    const url = `${BASE_API_URL}/activity-log/recent${queryString ? `?${queryString}` : ''}`;

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching recent activities:", response.statusText);
      throw new Error("Failed to fetch recent activities");
    }

    const data = await response.json();
    return data.activities || [];
  } catch (error) {
    console.error("Fetch recent activities error:", error);
    throw new Error("Failed to fetch recent activities");
  }
}

// Get teacher's recent activities (filtered by teacher's actions)
export async function getTeacherRecentActivities(schoolId: string, teacherId: string, limit: number = 10): Promise<ActivityLogEntry[]> {
  return getRecentActivities({
    school_id: schoolId,
    user_id: teacherId,
    limit
  });
}

// Format activity description for display
export function formatActivityDescription(activity: ActivityLogEntry): string {
  const actionDescriptions: Record<string, string> = {
    // Schedule/Timetable
    'schedule_created': `Created schedule entry for ${activity.target_name}`,
    'schedule_updated': `Updated schedule entry for ${activity.target_name}`,
    'schedule_deleted': `Deleted schedule entry for ${activity.target_name}`,

    // Exams
    'exam_created': `Created exam schedule for ${activity.target_name}`,
    'exam_updated': `Updated exam schedule for ${activity.target_name}`,
    'exam_deleted': `Deleted exam schedule for ${activity.target_name}`,
    'exam_supervisor_assigned': `Assigned exam supervisor for ${activity.target_name}`,

    // Grades
    'grade_created': `Added grade for ${activity.target_name}`,
    'grade_updated': `Updated grade for ${activity.target_name}`,
    'grade_deleted': `Deleted grade for ${activity.target_name}`,

    // Attendance
    'attendance_marked': `Marked attendance for ${activity.target_name}`,
    'attendance_updated': `Updated attendance for ${activity.target_name}`,

    // Students
    'student_created': `Added student ${activity.target_name}`,
    'student_updated': `Updated student ${activity.target_name}`,
    'student_deleted': `Removed student ${activity.target_name}`,

    // Classes
    'class_created': `Created class ${activity.target_name}`,
    'class_updated': `Updated class ${activity.target_name}`,
    'class_deleted': `Deleted class ${activity.target_name}`,

    // Announcements
    'announcement_created': `Created announcement: ${activity.target_name}`,
    'announcement_updated': `Updated announcement: ${activity.target_name}`,
    'announcement_deleted': `Deleted announcement: ${activity.target_name}`,
  };

  return actionDescriptions[activity.action] || `Performed action: ${activity.action}`;
}

// Get activity icon based on action type
export function getActivityIcon(action: string): string {
  const iconMap: Record<string, string> = {
    // Schedule/Timetable
    'schedule_created': 'Calendar',
    'schedule_updated': 'Calendar',
    'schedule_deleted': 'Calendar',

    // Exams
    'exam_created': 'Eye',
    'exam_updated': 'Eye',
    'exam_deleted': 'Eye',
    'exam_supervisor_assigned': 'Eye',

    // Grades
    'grade_created': 'BookOpen',
    'grade_updated': 'BookOpen',
    'grade_deleted': 'BookOpen',

    // Attendance
    'attendance_marked': 'ClipboardList',
    'attendance_updated': 'ClipboardList',

    // Students
    'student_created': 'Users',
    'student_updated': 'Users',
    'student_deleted': 'Users',

    // Classes
    'class_created': 'School',
    'class_updated': 'School',
    'class_deleted': 'School',

    // Announcements
    'announcement_created': 'Megaphone',
    'announcement_updated': 'Megaphone',
    'announcement_deleted': 'Megaphone',
  };

  return iconMap[action] || 'Activity';
}

// Get activity color based on action type
export function getActivityColor(action: string): string {
  if (action.includes('created')) return 'text-green-600 dark:text-green-400';
  if (action.includes('updated')) return 'text-blue-600 dark:text-blue-400';
  if (action.includes('deleted')) return 'text-red-600 dark:text-red-400';
  if (action.includes('exam')) return 'text-orange-600 dark:text-orange-400';
  return 'text-gray-600 dark:text-gray-400';
}

// Format time ago
export function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return 'Just now';
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
  if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
  
  return date.toLocaleDateString();
}
