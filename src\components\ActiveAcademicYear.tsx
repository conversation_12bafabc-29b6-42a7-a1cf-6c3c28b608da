import { CalendarDays, RefreshCcw } from 'lucide-react';
import { useAcademicYearContext } from '@/context/AcademicYearContext';

const AcademicYearCard = () => {
  const { currentAcademicYear, yearLoading, refreshAcademicYear } = useAcademicYearContext();

  return (
    <div className="p-3 rounded border dark:border-gray-700 text-sm flex items-center justify-between gap-2">
      <div className="flex items-center gap-1 text-gray-700 dark:text-gray-200">
        <CalendarDays className="w-4 h-4" />
        {yearLoading ? 'Loading...' : currentAcademicYear || 'N/A'}
        <span>Academic Year</span>
      </div>
      <button onClick={refreshAcademicYear} title="Refresh">
        <RefreshCcw className="w-4 h-4 text-teal dark:text-blue-400 hover:opacity-80 transition" />
      </button>
    </div>
  );
};

export default AcademicYearCard;
