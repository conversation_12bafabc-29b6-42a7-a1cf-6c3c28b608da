// Utility to check if environment variables are properly loaded
export function checkEnvironmentVariables() {
  const requiredEnvVars = {
    NEXT_PUBLIC_BASE_API_URL: process.env.NEXT_PUBLIC_BASE_API_URL,
  };

  console.log("Environment Variables Check:");
  console.log("============================");
  
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    console.log(`${key}:`, value || "❌ MISSING");
  });

  console.log("============================");
  
  return requiredEnvVars;
}

// Export for use in components
export const ENV = {
  API_URL: process.env.NEXT_PUBLIC_BASE_API_URL || "https://scolarify.onrender.com/api",
};