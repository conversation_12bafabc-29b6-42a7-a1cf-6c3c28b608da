import React from 'react';
import DataTableWithBulkActions from '@/components/enhanced/DataTableWithBulkActions';
import { ClassSchema } from '@/app/models/ClassModel';
import { deleteMultipleClasses, deleteAllClasses } from '@/app/services/ClassServices';

interface ClassesTableWithBulkActionsProps {
  classes: ClassSchema[];
  setClasses: React.Dispatch<React.SetStateAction<ClassSchema[]>>;
  columns: any[];
  actions: any[];
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  onSelectionChange?: (selection: ClassSchema[]) => void;
  onSuccess?: (message: string) => void;
  onError?: (message: string) => void;
}

const ClassesTableWithBulkActions: React.FC<ClassesTableWithBulkActionsProps> = ({
  classes,
  setClasses,
  columns,
  actions,
  loading = false,
  onLoadingChange,
  onSelectionChange,
  onSuccess,
  onError
}) => {
  return (
    <DataTableWithBulkActions
      columns={columns}
      data={classes}
      actions={actions}
      defaultItemsPerPage={5}
      loading={loading}
      onLoadingChange={onLoadingChange}
      onSelectionChange={onSelectionChange}
      idAccessor="class_id" // Classes use class_id instead of _id
      enableBulkActions={true}
      deleteMultipleService={deleteMultipleClasses}
      deleteAllService={deleteAllClasses}
      itemName="class"
      setItems={setClasses}
      onSuccess={onSuccess}
      onError={onError}
    />
  );
};

export default ClassesTableWithBulkActions;
