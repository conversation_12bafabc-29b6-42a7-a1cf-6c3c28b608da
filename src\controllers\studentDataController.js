const mongoose = require("mongoose");
const Student = require("../models/Student");
const School = require("../models/School");
const ClassModel = require("../models/Class");
const Grade = require("../models/Grade");
const Attendance = require("../models/Attendance");
const Discipline = require("../models/Discipline");
const Announcement = require("../models/Announcement");
const SchoolResource = require("../models/SchoolResources");
const ClassSchedule = require("../models/ClassSchedule");
const Fee = require("../models/Fees");
const FeePayment = require("../models/FeePayment");

/*
 GET /student-data/:studentId
 
  Aggregates all of a student’s dashboard data,
  looked up by either their `student_id` or by Mongo `_id`.
 */
async function getStudentData(req, res) {
  try {
    const { studentId } = req.params;

    // build a “find” filter that tries both
    const filter = {
      $or: [{ student_id: studentId }],
    };

      if (mongoose.isValidObjectId(studentId)) {
      filter.$or.push({ _id: studentId });
    }

    // Fetch the student
    const student = await Student.findOne(filter)
      .populate("class_id", "name class_code")
      .populate("class_level", "name")
      .lean();

    if (!student) {
      return res.status(404).json({ message: "Student not found" });
    }


    // School
    const school = await School.findById(student.school_id, "name address").lean();

    // Class (already populated, but refetch if you like)
    const classInfo = student.class_id
      ? await ClassModel.findById(student.class_id._id).lean()
      : null;

    // Grades
    const grades = await Grade.find({ student_id: student._id })
      .populate("subject_id", "name") 
      .populate("exam_type", "type")
      .lean();

    // Attendance
    const attendance = await Attendance.find({
      student_id: student._id,
    })
      // .populate("subject_id", "name")
      .populate("schedule_id", "name")
      .lean();

    // Discipline
    const discipline = await Discipline.find({
      student_id: student._id,
    }).lean();

    // Announcements
    const announcements = await Announcement.find({
      school_id: school._id,
      is_published: true,
    })
      .sort({ published_at: -1 })
      .populate("author_id", "first_name last_name")
      .lean();

    // Resources
    const resources = await SchoolResource.find({
      school_id: school._id,
    }).lean();

    // Schedule
    const schedule = await ClassSchedule.find({
      school_id: school._id,
      class_id: student.class_id._id,
    })
      .populate("period_id", "start_time end_time")
      .populate("subject_id", "name")
      .populate("teacher_id", "first_name last_name")
      .lean();

    // Fee definitions
    const feeDefinitions = await Fee.find({ school_id: school._id }).lean();

    // Payments
    const feePayments = await FeePayment.find({
      student_id: student._id,
    }).lean();

    // Assemble response
    return res.json({
      student,
      school,
      class: classInfo,
      grades,
      attendance,
      discipline,
      announcements,
      resources,
      schedule,
      feeDefinitions,
      feePayments,
    });
  } catch (err) {
    console.error("Error in getStudentData:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
}

module.exports = { getStudentData };
