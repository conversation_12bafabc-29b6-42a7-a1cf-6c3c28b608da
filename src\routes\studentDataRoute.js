const express = require("express");
const { getStudentData } = require("../controllers/studentDataController");
const {
  authenticate,
  authorize,
  checkSubscription,
} = require("../middleware/middleware");

const router = express.Router();

router.get(
  "/student-data/:studentId",
  authenticate,
  checkSubscription,
  authorize(["admin", "super", "teacher", "parent"]),
  getStudentData
);

module.exports = router;
