
"use client";

import React, { useState } from "react";
import { X, AlertTriangle, UserX } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import CircularLoader from '@/components/widgets/CircularLoader'; // Assuming this path is correct in your project

interface UnassignConfirmModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => Promise<void>; // This function will be called when the user confirms the unassign action
  title: string;
  message: string;
  itemName?: string; // Optional: name of the single item being unassigned (e.g., "John Do<PERSON>")
  itemCount?: number; // Optional: count of items if type is "multiple" (e.g., 3)
  type?: "single" | "multiple"; // Optional: to indicate if it's a single or multiple unassign operation
  loading?: boolean; // External loading state to disable buttons and show spinner during API calls
}

/**
 * UnassignConfirmModal component displays a confirmation dialog for unassigning items.
 * It supports both single and multiple item unassignment confirmations.
 *
 * @param {UnassignConfirmModalProps} props - The properties for the component.
 * @param {boolean} props.isOpen - Controls the visibility of the modal.
 * @param {Function} props.onClose - Callback function to close the modal.
 * @param {Function} props.onConfirm - Async callback function to execute the unassign logic.
 * @param {string} props.title - The title displayed in the modal header.
 * @param {string} props.message - The main message asking for confirmation.
 * @param {string} [props.itemName] - The name of the item if a single item is being unassigned.
 * @param {number} [props.itemCount] - The count of items if multiple items are being unassigned.
 * @param {'single' | 'multiple'} [props.type='single'] - Specifies if the modal is for single or multiple items.
 * @param {boolean} [props.loading=false] - Indicates if an unassign operation is currently in progress.
 */
export default function UnassignConfirmModal({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemName,
  itemCount,
  type = "single",
  loading = false,
}: UnassignConfirmModalProps) {

  // The 'loading' prop from the parent `EnrollmentPage` controls the UI state
  // directly for all action buttons within this modal.

  if (!isOpen) return null; // Don't render anything if the modal is not open

  return (
    // AnimatePresence allows for exit animations when the component is removed from the DOM
    <AnimatePresence>
      {isOpen && ( // Ensure AnimatePresence correctly detects component presence/absence
        <motion.div
          initial={{ opacity: 0 }} // Initial state for the overlay
          animate={{ opacity: 1 }} // Animation to visible state
          exit={{ opacity: 0 }}    // Animation to hidden state when closing
          className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 font-inter"
        >
          {/* Main modal content container */}
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }} // Initial state for the modal box
            animate={{ opacity: 1, scale: 1 }}     // Animation to visible state
            exit={{ opacity: 0, scale: 0.95 }}      // Animation to hidden state when closing
            className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md text-gray-900 dark:text-gray-100"
          >
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                {/* Icon for unassign action (red background for emphasis) */}
                <div className="w-10 h-10 bg-red-500 rounded-lg flex items-center justify-center">
                  <UserX className="h-5 w-5 text-white" /> {/* Lucide icon for unassign user */}
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                    {title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    This action might be irreversible
                  </p>
                </div>
              </div>
              {/* Close button (X icon) */}
              <button
                onClick={onClose} // Calls the onClose prop from the parent
                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
                disabled={loading} // Disable if an operation is in progress
              >
                <X className="h-5 w-5" /> {/* Lucide X icon */}
              </button>
            </div>

            {/* Modal Content Area */}
            <div className="p-6">
              <div>
                <div className="mb-4">
                  <p className="text-gray-700 dark:text-gray-300 mb-3">
                    {message}
                  </p>

                  {/* Display item name/count if provided */}
                  {itemName && (
                    <div className="p-3 bg-gray-50 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600">
                      <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                        {type === "multiple" ? `${itemCount} items selected` : itemName}
                      </p>
                    </div>
                  )}
                </div>

                {/* Warning specifically for multiple unassign operations */}
                {type === "multiple" && (
                  <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
                    <div className="flex items-center space-x-2">
                      <AlertTriangle className="h-4 w-4 text-red-500" /> {/* Warning icon */}
                      <p className="text-sm text-red-700 dark:text-red-300">
                        This will unassign multiple items permanently!
                      </p>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex justify-end space-x-3">
                  {/* Cancel button */}
                  <button
                    type="button"
                    onClick={onClose} // Calls the onClose prop from the parent
                    className="px-4 py-2 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors rounded-md"
                    disabled={loading} // Disable if an operation is in progress
                  >
                    Cancel
                  </button>
                  {/* Confirm Unassign button */}
                  <button
                    onClick={onConfirm} // Calls the onConfirm prop from the parent
                    className="flex items-center space-x-2 px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={loading} // Disable if an operation is in progress
                  >
                    {loading && <CircularLoader size={16} color="white" />} {/* Show loader when loading */}
                    {!loading && <UserX className="h-4 w-4" />} {/* Show icon when not loading */}
                    <span>
                      {loading
                        ? "Unassigning..." // Text when loading
                        : type === "multiple"
                          ? `Unassign ${itemCount || 0} Items` // Text for multiple unassign
                          : "Unassign" // Default text for single unassign
                      }
                    </span>
                  </button>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}