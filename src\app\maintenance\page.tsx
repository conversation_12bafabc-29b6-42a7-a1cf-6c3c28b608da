"use client";

import { useEffect, useState } from "react";
import MaintenanceMode from "@/components/MaintenanceMode";
import { getSystemSettings } from "@/app/services/SystemSettingsService";

export default function MaintenancePage() {
    const [maintenanceMessage, setMaintenanceMessage] = useState(
        "We are currently performing scheduled maintenance. Please check back soon."
    );
    const [platformName, setPlatformName] = useState("Scholarify");
    const [supportEmail, setSupportEmail] = useState("<EMAIL>");

    useEffect(() => {
        const fetchSettings = async () => {
            try {
                const settings = await getSystemSettings();
                setMaintenanceMessage(settings.maintenanceMessage || "We are currently performing scheduled maintenance. Please check back soon.");
                setPlatformName(settings.platformName || "Scholarify");
                setSupportEmail(settings.supportEmail || "<EMAIL>");
            } catch (error) {
                console.error("Error fetching maintenance settings:", error);
                // Use default values if API call fails
            }
        };

        fetchSettings();
    }, []);

    return (
        <MaintenanceMode 
            message={maintenanceMessage}
            platformName={platformName}
            supportEmail={supportEmail}
        />
    );
}