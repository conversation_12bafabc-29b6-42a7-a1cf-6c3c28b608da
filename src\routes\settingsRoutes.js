const express = require('express');
const settingsController = require('../controllers/SettingsController');
const { authenticate, authorize } = require('../middleware/middleware');

const router = express.Router();

router.get('/test', settingsController.testSettingsResponse);

// Get settings — accessible by admin, super, maybe support role
router.get('/get-settings', authenticate, authorize(['super']), settingsController.getSettings);

// Create settings — restricted to admin and super
router.post('/create-settings', authenticate, authorize(['super']), settingsController.createSettings);

// Update entire settings — restricted to admin and super
router.put('/update-settings', authenticate, authorize(['super']), settingsController.updateSettings);

// Update a specific section of settings (general, financials, notifications, security)
router.patch('/patch-settings/:section', authenticate, authorize(['super']), settingsController.updateSettingsSection);

// Delete all settings — restricted to super only (dangerous!)
router.delete('/delete-settings', authenticate, authorize(['super']), settingsController.deleteSettings);

module.exports = router;
