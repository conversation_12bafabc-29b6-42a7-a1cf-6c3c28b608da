"use client";

import React, { useState, useEffect } from "react";
import { <PERSON><PERSON><PERSON>, Bar, XAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON>lt<PERSON>, ResponsiveC<PERSON>r, CartesianGrid } from "recharts";
import { Filter, TrendingUp } from "lucide-react";
import { getTopClassesByGrades, ClassGradeAverage, GradeFilters } from "@/app/services/ClassServices";
import { getAvailableTerms, GradeTerm } from "@/app/services/GradeServices";
import CircularLoader from "@/components/widgets/CircularLoader";

interface TopClassesChartProps {
  schoolId: string;
}

const TopClassesChart: React.FC<TopClassesChartProps> = ({ schoolId }) => {
  const [data, setData] = useState<ClassGradeAverage[]>([]);
  const [terms, setTerms] = useState<GradeTerm[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFilters] = useState<GradeFilters>({});
  const [showFilters, setShowFilters] = useState(false);

  // Fetch available terms on component mount
  useEffect(() => {
    const fetchTerms = async () => {
      if (!schoolId) return;

      try {
        const termsData = await getAvailableTerms(schoolId);
        setTerms(termsData.terms);

        // Set current term as default filter if available
        if (termsData.current_term) {
          setFilters({ term_id: termsData.current_term._id });
        }
      } catch (err) {
        console.error("Error fetching terms:", err);
      }
    };

    fetchTerms();
  }, [schoolId]);

  // Fetch class averages when filters change
  useEffect(() => {
    const fetchClassAverages = async () => {
      if (!schoolId) return;

      setLoading(true);
      setError(null);

      try {
        const classAverages = await getTopClassesByGrades(schoolId, filters);
        setData(classAverages);
      } catch (err) {
        console.error("Error fetching class averages:", err);
        setError("Failed to load class averages");
      } finally {
        setLoading(false);
      }
    };

    fetchClassAverages();
  }, [schoolId, filters]);

  // Handle filter changes
  const handleTermChange = (termId: string) => {
    const selectedTerm = terms.find(term => term._id === termId);
    setFilters(prev => ({
      ...prev,
      term_id: termId,
      sequence_number: undefined // Reset sequence when term changes
    }));
  };

  const handleSequenceChange = (sequenceNumber: number) => {
    setFilters(prev => ({
      ...prev,
      sequence_number: sequenceNumber
    }));
  };

  const clearFilters = () => {
    setFilters({});
  };

  // Get selected term for sequence options
  const selectedTerm = terms.find(term => term._id === filters.term_id);

  if (loading) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <CircularLoader size={32} color="teal" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">{error}</p>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="rounded-lg border border-stroke bg-widget p-4 h-[400px] flex items-center justify-center">
        <p className="text-foreground/60">No classes data available</p>
      </div>
    );
  }

  // Transform data for the chart
  const chartData = data.map(item => ({
    name: item.class_name,
    average: Math.round(item.average_grade * 100) / 100, // Round to 2 decimal places
    students: item.student_count,
  }));

  return (
    <div className="rounded-lg border border-stroke bg-widget p-4">
      <div className="mb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-[#17B890]" />
            <div>
              <h3 className="text-lg font-semibold text-foreground">Top Classes by Average Grades</h3>
              <p className="text-sm text-foreground/60">Performance overview (Scale: 0-20)</p>
            </div>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 rounded-md transition-colors"
          >
            <Filter className="h-4 w-4" />
            Filters
          </button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Term Filter */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">Term</label>
                <select
                  value={filters.term_id || ''}
                  onChange={(e) => handleTermChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-foreground"
                >
                  <option value="">All Terms</option>
                  {terms.map(term => (
                    <option key={term._id} value={term._id}>
                      {term.name} ({term.academic_year})
                    </option>
                  ))}
                </select>
              </div>

              {/* Sequence Filter */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">Sequence</label>
                <select
                  value={filters.sequence_number || ''}
                  onChange={(e) => handleSequenceChange(parseInt(e.target.value))}
                  disabled={!selectedTerm}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-foreground disabled:opacity-50"
                >
                  <option value="">All Sequences</option>
                  {selectedTerm?.sequences.map(sequence => (
                    <option key={sequence.sequence_number} value={sequence.sequence_number}>
                      {sequence.sequence_name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <button
                  onClick={clearFilters}
                  className="px-4 py-2 text-sm bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 text-foreground rounded-md transition-colors"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
      
      <div className="h-[350px]">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{
              top: 20,
              right: 30,
              left: 20,
              bottom: 60,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="var(--stroke)" opacity={0.3} />
            <XAxis
              dataKey="name"
              axisLine={false}
              tick={{ fontSize: 11, fill: "var(--foreground)" }}
              tickLine={false}
              angle={-45}
              textAnchor="end"
              height={60}
            />
            <YAxis
              domain={[0, 20]}
              ticks={[0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20]}
              tick={{ fontSize: 12, fill: "var(--foreground)" }}
              axisLine={false}
              tickLine={false}
              label={{
                value: 'Average Grade (/20)',
                angle: -90,
                position: 'insideLeft',
                style: { textAnchor: 'middle', fill: "var(--foreground)", fontSize: '12px' }
              }}
            />
            <Tooltip
              contentStyle={{
                backgroundColor: "var(--widget)",
                border: "1px solid var(--stroke)",
                borderRadius: "8px",
                fontSize: "12px",
                color: "var(--foreground)",
              }}
              labelStyle={{
                color: "var(--foreground)",
              }}
              formatter={(value: any, name: string) => {
                if (name === 'average') {
                  return [`${value}/20`, 'Average Grade'];
                }
                return [value, name];
              }}
            />
            <Bar
              dataKey="average"
              fill="#17B890"
              name="Average Grade"
              radius={[4, 4, 0, 0]}
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
      
      <div className="mt-4 grid grid-cols-1 sm:grid-cols-2 gap-4">
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#17B890] rounded"></div>
          <span className="text-sm text-foreground/70">Number of Students</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-3 h-3 bg-[#0E9B6D] rounded"></div>
          <span className="text-sm text-foreground/70">Average Grade</span>
        </div>
      </div>
    </div>
  );
};

export default TopClassesChart;
