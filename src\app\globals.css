@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Scrollbar Styles */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(156 163 175);
  border-radius: 4px;
  border: 1px solid transparent;
  background-clip: content-box;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
  background-clip: content-box;
}

/* Dark mode scrollbar */
.dark .custom-scrollbar {
  scrollbar-color: rgb(75 85 99) transparent;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(75 85 99);
  background-clip: content-box;
}

.dark .custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(107 114 128);
  background-clip: content-box;
}

/* Scrollbar for modal content */
.modal-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgb(156 163 175 / 0.5) transparent;
}

.modal-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.modal-scrollbar::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 3px;
}

.modal-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(156 163 175 / 0.5);
  border-radius: 3px;
}

.modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(156 163 175 / 0.8);
}

.dark .modal-scrollbar::-webkit-scrollbar-thumb {
  background: rgb(75 85 99 / 0.5);
}

.dark .modal-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgb(75 85 99 / 0.8);
}
@plugin "daisyui";

@import url('https://fonts.googleapis.com/css2?family=Bricolage+Grotesque:wght@400;700&display=swap');
@import '../styles/formStyle.css';

:root {
  --background: #F5F6FA;
  --widget:#f6faf6;;
  --foreground: #1E3D59;
  --stroke : #2D3436;
  --teal: #17B890; /* teal do not modify this color*/
  --tealdarker: #0E9B6D; /* teal-600 do not modify this color*/
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #f3f4f6;
    --foreground: #1E3D59;
    --stroke : #636E72;
  } 
}
html.dark {
  --background: #111827;
  --foreground: #f3f4f6;
  --stroke: #f3f4f6;
  --widget:#111827;;
}

body {
  color: var(--foreground);
  background: var(--background);
  font-family: 'Bricolage Grotesque', sans-serif;
}
.bg-tealdarker {
  background-color: var(--tealdarker);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }
}

/* Thin scrollbar */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: teal #e5e7eb;
}

/* For WebKit (Chrome, Edge) */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #e5e7eb;
  border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: teal;
  border-radius: 10px;
  border: 1px solid #e5e7eb;
}
/* Subtle Scrollbar */
.subtle-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #d1d5db transparent; /* thumb color, transparent track */
}

/* WebKit (Chrome, Safari, Edge) */
.subtle-scrollbar::-webkit-scrollbar {
  width: 3px;

}

.subtle-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.subtle-scrollbar::-webkit-scrollbar-thumb {
  background-color: #d1d5db; /* very light gray */
  border-radius: 6px;
  border: 2px solid transparent; /* creates padding effect */
}
.bg-widget{
  background:var(--widget) ;
}
.bg-forground{
  background:var(--foreground) ;
}
.bg-glassy {

  backdrop-filter: blur(10px); /* Apply blur effect to the background */
  border-radius: 5px; /* Optional: Add rounded corners */
}
/* Remove default blue outline and replace with teal */
:focus-visible {
  outline: none !important;
}

/* Apply teal outline to all form elements on focus */
select:focus, input:focus, textarea:focus, input[type="checkbox"]:focus {
  outline: 2px solid #14b8a6 !important; /* Teal outline */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Apply teal background and border when checkbox is checked */
input[type="checkbox"]:checked {
  background-color: #14b8a6 !important; /* Teal background when checked */
  border-color: #14b8a6 !important; /* Teal border */
}

/* Remove blue outline from select elements */
select:focus {
  border-color: #14b8a6 !important; /* Teal border */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Apply teal color on hover and focus */
input[type="checkbox"]:checked {
  background-color: #14b8a6 !important;
  border-color: #14b8a6 !important;
}

input[type="checkbox"]:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

/* Theme transition animations */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Enhanced dark mode glow effects */
.dark .shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.1);
}

.dark .shadow-xl {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.1);
}

/* Theme-aware background patterns */
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(156, 163, 175, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(156, 163, 175, 0.1) 1px, transparent 1px);
  background-size: 20px 20px;
}

.dark .bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px);
}

/* Teal background for options in the select */
select option:checked {
  background-color: #14b8a6 !important;
  color: white !important;
}

/* Remove default blue outline from form inputs */
input:focus, textarea:focus, select:focus {
  outline: none !important;
  border-color: #14b8a6 !important; /* Teal border */
  box-shadow: 0 0 0 2px rgba(20, 184, 166, 0.3) !important; /* Teal shadow */
}

@keyframes pulseBar {
  0% {
    transform: scaleY(0.5);
  }
  50% {
    transform: scaleY(1.5);
  }
  100% {
    transform: scaleY(0.5);
  }
}

.animate-pulseBar {
  animation: pulseBar 1.5s ease-in-out infinite;
}



@keyframes border-glow {
  0% {
    border-color: #cbd5e0; /* gray-300 */
    box-shadow: 0 0 0px rgba(52, 183, 65, 0); /* no glow */
    transform: scale(1);
  }
  50% {
    border-color: #34b741; /* green glow */
    box-shadow: 0 0 12px rgba(52, 183, 65, 0.6), 0 0 20px rgba(52, 183, 65, 0.3);
    transform: scale(1.01);
  }
  100% {
    border-color: #cbd5e0; /* back to neutral */
    box-shadow: 0 0 0px rgba(52, 183, 65, 0);
    transform: scale(1);
  }
}

.animate-border-glow-today {
  animation: border-glow 2.5s ease-in-out infinite;
}
