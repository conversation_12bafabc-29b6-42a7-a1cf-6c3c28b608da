"use client";

import React, { useState } from "react";
import { X, AlertTriangle } from "lucide-react";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import { motion } from "framer-motion";
import CircularLoader from "@/components/widgets/CircularLoader";

interface BulkDeleteModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (password: string) => void;
  title: string;
  message: string;
  itemCount: number;
  itemType: string; // e.g., "schools", "users", etc.
  isDeleteAll?: boolean; // true for "delete all", false for "delete selected"
  submitStatus?: "success" | "failure" | null;
  isSubmitting?: boolean;
  requirePassword?: boolean; // Whether password confirmation is required
}

const BulkDeleteModal: React.FC<BulkDeleteModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  itemCount,
  itemType,
  isDeleteAll = false,
  submitStatus = null,
  isSubmitting = false,
  requirePassword = true,
}) => {
  const [password, setPassword] = useState("");
  const [showConfirmation, setShowConfirmation] = useState(false);

  if (!isOpen) return null;

  const handleFirstConfirm = () => {
    if (requirePassword) {
      setShowConfirmation(true);
    } else {
      onConfirm("");
    }
  };

  const handleFinalConfirm = (e: React.FormEvent) => {
    e.preventDefault();
    if (requirePassword && !password) {
      alert("Please enter your password to confirm deletion.");
      return;
    }
    onConfirm(password);
  };

  const handleClose = () => {
    setPassword("");
    setShowConfirmation(false);
    onClose(); // Actually close the modal
  };

  const getWarningLevel = () => {
    if (isDeleteAll) return "extreme";
    if (itemCount > 10) return "high";
    if (itemCount > 5) return "medium";
    return "low";
  };

  const warningLevel = getWarningLevel();
  const warningColors = {
    low: "border-yellow-300 bg-yellow-50 text-yellow-800",
    medium: "border-orange-300 bg-orange-50 text-orange-800",
    high: "border-red-300 bg-red-50 text-red-800",
    extreme: "border-red-500 bg-red-100 text-red-900"
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4"
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${warningColors[warningLevel]}`}>
              <AlertTriangle size={20} />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              {title}
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            disabled={isSubmitting}
          >
            <X size={20} />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {!showConfirmation ? (
            // First confirmation step
            <div>
              <div className={`p-4 rounded-lg border-2 ${warningColors[warningLevel]} mb-4`}>
                <p className="font-medium mb-2">
                  {isDeleteAll ? "⚠️ DANGER: Delete All Operation" : "⚠️ Bulk Delete Operation"}
                </p>
                <p className="text-sm">
                  {message}
                </p>
              </div>

              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  You are about to delete <strong className="text-red-600">{itemCount}</strong> {itemType}.
                </p>
                {isDeleteAll && (
                  <p className="text-sm text-red-600 font-medium mt-2">
                    This will delete ALL {itemType} in the system and cannot be undone!
                  </p>
                )}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleClose}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  onClick={handleFirstConfirm}
                  className={`px-4 py-2 text-white rounded-md ${isDeleteAll
                      ? "bg-red-700 hover:bg-red-800"
                      : "bg-red-500 hover:bg-red-600"
                    }`}
                  disabled={isSubmitting}
                >
                  {requirePassword ? "Continue" : "Delete"}
                </button>
              </div>
            </div>
          ) : (
            // Password confirmation step
            <form onSubmit={handleFinalConfirm}>
              <div className="mb-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                  Please enter your password to confirm this {isDeleteAll ? "destructive" : "bulk"} operation:
                </p>

                <input
                  type="password"
                  placeholder="Enter your password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-red-300 dark:border-red-500 rounded-md text-sm text-foreground dark:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500"
                  required
                  disabled={isSubmitting}
                  autoFocus
                />
              </div>

              {submitStatus && (
                <div className="mb-4">
                  <SubmissionFeedback
                    status={submitStatus}
                    message={
                      submitStatus === "success"
                        ? "Bulk delete was successful!"
                        : submitStatus === "failure"
                          ? "Bulk delete failed. Please try again."
                          : ""
                    }
                  />
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowConfirmation(false)}
                  className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500"
                  disabled={isSubmitting}
                >
                  Back
                </button>
                <button
                  type="submit"
                  className={`px-4 py-2 text-white rounded-md flex items-center space-x-2 ${isDeleteAll
                      ? "bg-red-700 hover:bg-red-800"
                      : "bg-red-500 hover:bg-red-600"
                    }`}
                  disabled={isSubmitting || !password}
                >
                  {isSubmitting && <CircularLoader size={16} color="white" />}
                  <span>{isSubmitting ? "Deleting..." : `Delete ${itemCount} ${itemType}`}</span>
                </button>
              </div>
            </form>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default BulkDeleteModal;
