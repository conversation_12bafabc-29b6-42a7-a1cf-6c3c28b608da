// School Admin Skeletons
export { default as SchoolAdminGradesSkeleton } from './SchoolAdminGradesSkeleton';
export { default as SchoolAdminTimetableSkeleton } from './SchoolAdminTimetableSkeleton';
export { default as SchoolAdminTeacherAssignmentSkeleton } from './SchoolAdminTeacherAssignmentSkeleton';
export { default as SchoolAdminPeriodsSkeleton } from './SchoolAdminPeriodsSkeleton';
export { default as SchoolAdminAttendanceSkeleton } from './SchoolAdminAttendanceSkeleton';
export { default as SchoolAdminDashboardSkeleton } from './SchoolAdminDashboardSkeleton';

// Teacher Dashboard Skeletons
export { default as TeacherDashboardSkeleton } from './TeacherDashboardSkeleton';
export { default as TeacherStudentsSkeleton } from './TeacherStudentsSkeleton';
export { default as TeacherClassesSkeleton } from './TeacherClassesSkeleton';
export { default as TeacherTimetableSkeleton } from './TeacherTimetableSkeleton';
export { default as TeacherResourcesSkeleton } from './TeacherResourcesSkeleton';
export { default as TeacherGradesSkeleton } from './TeacherGradesSkeleton';
export { default as TeacherAttendanceSkeleton } from './TeacherAttendanceSkeleton';
export { default as TeacherClassAttendanceSkeleton } from './TeacherClassAttendanceSkeleton';

// Individual skeleton components for reuse
export {
  GradeCardSkeleton,
  GradeStatsCardSkeleton,
  GradeFiltersSkeleton,
  GradeTableSkeleton
} from './SchoolAdminGradesSkeleton';

export {
  TimetableCellSkeleton,
  TimetableGridSkeleton,
  TimetableFiltersSkeleton,
  TimetableStatsCardSkeleton,
  ClassScheduleListSkeleton
} from './SchoolAdminTimetableSkeleton';

export {
  AssignmentCardSkeleton,
  AssignmentFiltersSkeleton,
  AssignmentStatsCardSkeleton,
  AssignmentTableSkeleton,
  TeacherListSkeleton,
  ClassAssignmentOverviewSkeleton
} from './SchoolAdminTeacherAssignmentSkeleton';

export {
  PeriodCardSkeleton,
  PeriodTimelineSkeleton,
  PeriodStatsCardSkeleton,
  PeriodTableSkeleton,
  PeriodFormSkeleton,
  PeriodConflictsSkeleton,
  PeriodUsageChartSkeleton
} from './SchoolAdminPeriodsSkeleton';

export {
  StudentAttendanceCardSkeleton,
  AttendanceCalendarSkeleton,
  AttendanceStatsCardSkeleton,
  AttendanceFiltersSkeleton,
  AttendanceTableSkeleton,
  AttendanceChartSkeleton,
  ClassAttendanceOverviewSkeleton,
  RecentAttendanceActivitiesSkeleton
} from './SchoolAdminAttendanceSkeleton';

export {
  StudentCardSkeleton,
  ClassSectionSkeleton,
  HeaderStatsSkeleton,
  PageHeaderSkeleton
} from './TeacherStudentsSkeleton';

export {
  DashboardStatsCardSkeleton,
  DashboardChartSkeleton,
  DashboardActivitiesSkeleton,
  DashboardQuickActionsSkeleton,
  DashboardAnnouncementsSkeleton
} from './SchoolAdminDashboardSkeleton';

export {
  ClassGradesSkeleton,
  SubjectGradesSkeleton
} from './GradesSkeleton';

export { TermsSkeleton } from './TermsSkeleton';

// New CRUD Page Skeletons
export { default as ExamTypeSkeleton } from './ExamTypeSkeleton';
export { default as DisciplineSkeleton } from './DisciplineSkeleton';
export { default as ResourcesSkeleton } from './ResourcesSkeleton';
export { default as FeeTypeSkeleton } from './FeeTypeSkeleton';
