"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import { Loader2 } from "lucide-react"; // Icon spinner

interface SidebarButtonProps {
  icon: any;
  name: string;
  href: string;
  disabled?: boolean;
  isSubItem?: boolean;
}

const SidebarButton = ({
  icon: Icon,
  name,
  href,
  disabled = false,
  isSubItem = false
}: SidebarButtonProps) => {
  const pathname = usePathname();
  const isActive = pathname.startsWith(href);
  const [loading, setLoading] = useState(false);

  const handleClick = () => {
    if (disabled) return;
    setLoading(true);
    // Let Next.js handle the route transition
    // Optionally you can reset loading after delay if needed
    setTimeout(() => setLoading(false), 1000);
  };

  if (disabled) {
    return (
      <div className={`text-sm flex items-center w-full px-4 py-2 rounded-lg transition-all opacity-50 cursor-not-allowed
                     ${isSubItem ? "text-xs px-3 py-1.5" : ""}`}>
        <Icon className={`mr-2 ${isSubItem ? "w-4 h-4" : "w-5 h-5"}`} />
        <span>{name}</span>
      </div>
    );
  }

  return (
    <Link
      href={href}
      onClick={handleClick}
      className={`text-sm flex items-center w-full px-4 py-2 rounded-lg transition-all
                 hover:text-background hover:bg-foreground hover:shadow-lg
                 ${isActive ? "bg-foreground text-background shadow-lg" : "text-foreground"}
                 ${isSubItem ? "text-xs px-3 py-1.5 ml-2" : ""}`}
    >
      {loading ? (
        <Loader2 className={`animate-spin mr-2 ${isSubItem ? "w-4 h-4" : "w-5 h-5"}`} />
      ) : (
        <Icon className={`mr-2 ${isSubItem ? "w-4 h-4" : "w-5 h-5"}`} />
      )}
      <span>{name}</span>
    </Link>
  );
};

export default SidebarButton;
