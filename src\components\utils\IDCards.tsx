import React from "react";
import { Document, Page, Text, View, StyleSheet, Image } from "@react-pdf/renderer";
import { StudentSchema } from "@/app/models/StudentModel";

const styles = StyleSheet.create({
    page: {
        padding: 20,
        flexDirection: "row",
        flexWrap: "wrap",
        justifyContent: "space-between",
    },
    card: {
        width: 210,
        height: 330,
        backgroundColor: "#ffffff",
        borderRadius: 12,
        border: "1pt solid #ccc",
        padding: 12,
        marginBottom: 16,
        display: "flex",
        flexDirection: "column",
        justifyContent: "space-between",
        alignItems: "center",
    },
    republic: {
        textAlign: "center",
        fontSize: 8,
        fontWeight: "bold",
        color: "#1E3D59",
        marginBottom: 2,
    },
    motto: {
        textAlign: "center",
        fontSize: 7,
        fontStyle: "italic",
        color: "#1E3D59",
        marginBottom: 6,
    },
    schoolHeader: {
        textAlign: "center",
        marginBottom: 4,
    },
    schoolName: {
        fontSize: 12,
        fontWeight: "bold",
        color: "#1E3D59",
    },
    cardTitle: {
        fontSize: 10,
        fontWeight: "bold",
        marginVertical: 6,
        textAlign: "center",
    },
    photoContainer: {
        width: 140,
        height: 140,
        borderRadius: 20,
        overflow: "hidden",
        alignSelf: "center",
        marginBottom: 8,
    },
    photo: {
        width: "100%",
        height: "100%",
    },
    infoGrid: {
        flexDirection: "row",
        justifyContent: "space-between",
        flexWrap: "wrap",
    },
    infoColumn: {
        width: "32%",
        paddingRight: 4,
    },
    infoLabel: {
        fontSize: 7,
        fontWeight: "bold",
        color: "#1E3D59",
    },
    infoValue: {
        fontSize: 8,
        marginBottom: 3,
        flexWrap: "wrap",
        wordBreak: "break-word",
        maxWidth: "100%",
        lineHeight: 1.2,
    },
    qrLogoColumn: {
        width: "35%",
        alignItems: "center",
        justifyContent: "center",
    },
    qr: {
        width: 30,
        height: 30,
        marginBottom: 6,
    },
    schoolLogo: {
        width: 30,
        height: 30,
        objectFit: "contain",
    },
    footer: {
        fontSize: 6,
        color: "#555",
        textAlign: "center",
        borderTopWidth: 0.3,
        borderTopColor: "#eee",
        marginTop: 6,
        paddingTop: 4,
    },
});

interface IDCardsPDFProps {
    students: StudentSchema[];
    schoolName: string;
    year: string;
}

const formatDOB = (dob?: string | null) => {
    if (!dob) return "N/A";
    const date = new Date(dob);
    return date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "long",
        year: "numeric",
    });
};

const IDCardsPDF: React.FC<IDCardsPDFProps> = ({ students, schoolName, year }) => {
    const chunks: StudentSchema[][] = [];
    for (let i = 0; i < students.length; i += 4) {
        chunks.push(students.slice(i, i + 4));
    }

    return (
        <Document>
            {chunks.map((chunk, pageIndex) => (
                <Page key={pageIndex} size="A4" style={styles.page}>
                    {chunk.map((student) => {
                        const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
                            student.student_id || "N/A"
                        )}&size=60x60`;

                        return (
                            <View key={student._id} style={styles.card}>
                                <Text style={styles.republic}>REPUBLIC OF CAMEROON</Text>
                                <Text style={styles.motto}>Peace - Work - Fatherland</Text>

                                <View style={styles.schoolHeader}>
                                    <Text style={styles.schoolName}>{schoolName || "SCHOOL NAME"}</Text>
                                </View>

                                <Text style={styles.cardTitle}>STUDENT ID CARD</Text>

                                <View style={styles.photoContainer}>
                                    {student.photoUrl ? (
                                        <Image src={String(student.photoUrl)} style={styles.photo} />
                                    ) : (
                                        <Image
                                            src={`${process.env.NEXT_PUBLIC_BASE_URL || ""}/assets/images/student.jpg`}
                                            style={styles.photo}
                                        />
                                    )}
                                </View>

                                <View style={styles.infoGrid}>
                                    <View style={styles.infoColumn}>
                                        <Text style={styles.infoLabel}>Name</Text>
                                        <Text style={styles.infoValue}>
                                            {student.first_name || ""} {student.last_name || ""}
                                        </Text>

                                        <Text style={styles.infoLabel}>School</Text>
                                        <Text style={styles.infoValue}>{schoolName || "N/A"}</Text>

                                        <Text style={styles.infoLabel}>Student ID</Text>
                                        <Text style={styles.infoValue}>{student.student_id || "N/A"}</Text>
                                    </View>

                                    <View style={styles.infoColumn}>
                                        <Text style={styles.infoLabel}>Gender</Text>
                                        <Text style={styles.infoValue}>{student.gender || "N/A"}</Text>

                                        <Text style={styles.infoLabel}>Nationality</Text>
                                        <Text style={styles.infoValue}>{student.nationality || "N/A"}</Text>

                                        <Text style={styles.infoLabel}>Class</Text>
                                        <Text style={styles.infoValue}>
                                            {typeof student.class_name === "string" ? student.class_name : "N/A"}
                                        </Text>

                                        <Text style={styles.infoLabel}>Date Of Birth</Text>
                                        <Text style={styles.infoValue}>
                                            {formatDOB(
                                                student.date_of_birth instanceof Date
                                                    ? student.date_of_birth.toISOString()
                                                    : student.date_of_birth
                                            )}
                                        </Text>

                                        <Text style={styles.infoLabel}>Academic Year</Text>
                                        <Text style={styles.infoValue}>{year || "N/A"}</Text>
                                    </View>

                                    <View style={styles.qrLogoColumn}>
                                        <Image src={qrCodeURL} style={styles.qr} />
                                        <Image src="/assets/images/school-logo.jpg" style={styles.schoolLogo} />
                                    </View>
                                </View>
                            </View>
                        );
                    })}
                </Page>
            ))}
        </Document>
    );
};

export default IDCardsPDF;
