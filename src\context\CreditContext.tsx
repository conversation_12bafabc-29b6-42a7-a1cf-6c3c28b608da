"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import useAuth from '@/app/hooks/useAuth';
import { getSchoolCredits } from '@/app/services/SchoolServices';
import { getSchoolSubscription } from '@/app/services/SubscriptionServices';
import { SchoolSubscriptionSchema } from '@/app/models/SchoolSubscriptionModel';

interface CreditContextType {
  // Crédits d'école existants (paiements étudiants)
  schoolCredits: number;
  schoolCreditsLoading: boolean;
  
  // Crédits de souscription (nouveau système)
  subscriptionCredits: number;
  subscriptionCreditsLoading: boolean;
  subscription: SchoolSubscriptionSchema | null;
  
  // Actions
  refreshCredits: () => void;
  refreshSubscriptionCredits: () => void;
  
  // État global
  loading: boolean;
  error: string | null;
}

const CreditContext = createContext<CreditContextType | undefined>(undefined);

interface CreditProviderProps {
  children: ReactNode;
}

export function CreditProvider({ children }: CreditProviderProps) {
  const { user } = useAuth();
  
  // États pour les crédits d'école existants
  const [schoolCredits, setSchoolCredits] = useState<number>(0);
  const [schoolCreditsLoading, setSchoolCreditsLoading] = useState(false);
  
  // États pour les crédits de souscription
  const [subscriptionCredits, setSubscriptionCredits] = useState<number>(0);
  const [subscriptionCreditsLoading, setSubscriptionCreditsLoading] = useState(false);
  const [subscription, setSubscription] = useState<SchoolSubscriptionSchema | null>(null);
  
  // État global
  const [error, setError] = useState<string | null>(null);

  const schoolId = user?.school_ids?.[0];

  // Fonction pour récupérer les crédits d'école existants
  const fetchSchoolCredits = async () => {
    if (!schoolId) return;
    
    try {
      setSchoolCreditsLoading(true);
      setError(null);
      const credits = await getSchoolCredits(schoolId);
      setSchoolCredits(credits);
    } catch (err) {
      console.error('Error fetching school credits:', err);
      setError('Erreur lors du chargement des crédits d\'école');
    } finally {
      setSchoolCreditsLoading(false);
    }
  };

  // Fonction pour récupérer les crédits de souscription
  const fetchSubscriptionCredits = async () => {
    if (!schoolId) return;
    
    try {
      setSubscriptionCreditsLoading(true);
      setError(null);
      const response = await getSchoolSubscription(schoolId);
      setSubscription(response.subscription);
      setSubscriptionCredits(response.subscription.credits_balance);
    } catch (err) {
      console.error('Error fetching subscription credits:', err);
      // Ne pas afficher d'erreur si le système de souscription n'est pas encore configuré
      setSubscriptionCredits(0);
      setSubscription(null);
    } finally {
      setSubscriptionCreditsLoading(false);
    }
  };

  // Fonction pour rafraîchir tous les crédits
  const refreshCredits = () => {
    fetchSchoolCredits();
    fetchSubscriptionCredits();
  };

  // Fonction pour rafraîchir uniquement les crédits de souscription
  const refreshSubscriptionCredits = () => {
    fetchSubscriptionCredits();
  };

  // Charger les données au montage et quand l'utilisateur change
  useEffect(() => {
    if (schoolId) {
      refreshCredits();
    }
  }, [schoolId]);

  const loading = schoolCreditsLoading || subscriptionCreditsLoading;

  const value: CreditContextType = {
    schoolCredits,
    schoolCreditsLoading,
    subscriptionCredits,
    subscriptionCreditsLoading,
    subscription,
    refreshCredits,
    refreshSubscriptionCredits,
    loading,
    error
  };

  return (
    <CreditContext.Provider value={value}>
      {children}
    </CreditContext.Provider>
  );
}

// Hook pour utiliser le contexte
export function useCreditContext(): CreditContextType {
  const context = useContext(CreditContext);
  if (context === undefined) {
    throw new Error('useCreditContext must be used within a CreditProvider');
  }
  return context;
}

// Hook pour les crédits d'école (compatibilité avec l'existant)
export function useSchoolCredits() {
  const { schoolCredits, schoolCreditsLoading, refreshCredits, error } = useCreditContext();
  return {
    credits: schoolCredits,
    loading: schoolCreditsLoading,
    refresh: refreshCredits,
    error
  };
}

// Hook pour les crédits de souscription
export function useSubscriptionCredits() {
  const { 
    subscriptionCredits, 
    subscriptionCreditsLoading, 
    subscription,
    refreshSubscriptionCredits, 
    error 
  } = useCreditContext();
  
  return {
    credits: subscriptionCredits,
    loading: subscriptionCreditsLoading,
    subscription,
    refresh: refreshSubscriptionCredits,
    error,
    hasCredits: (amount: number = 1) => subscriptionCredits >= amount,
    isLowBalance: subscription ? subscriptionCredits <= subscription.low_credit_threshold : false
  };
}
