import { getTokenFromCookie } from "./UserServices";

const BASE_API_URL = process.env.BASE_API_URL || "https://scolarify.onrender.com/api";

/**
 * Test service to verify teacher assignment functionality
 */

// Test function to check if teacher assignments are working
export async function testTeacherAssignments(schoolId: string) {
  const token = getTokenFromCookie("idToken");
  
  console.log("🧪 Testing Teacher Assignments...");
  console.log("School ID:", schoolId);
  console.log("Token:", token ? "✅ Present" : "❌ Missing");

  try {
    // Test 1: Get teacher assignments
    console.log("\n📋 Test 1: Getting teacher assignments...");
    const assignmentsResponse = await fetch(`${BASE_API_URL}/teacher/assignments/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("Response status:", assignmentsResponse.status);
    
    if (!assignmentsResponse.ok) {
      const errorText = await assignmentsResponse.text();
      console.error("❌ Assignments request failed:", errorText);
      return {
        success: false,
        error: `Assignments request failed: ${assignmentsResponse.status} - ${errorText}`
      };
    }

    const assignmentsData = await assignmentsResponse.json();
    console.log("✅ Assignments data:", assignmentsData);

    // Test 2: Get teacher students
    console.log("\n👥 Test 2: Getting teacher students...");
    const studentsResponse = await fetch(`${BASE_API_URL}/teacher/students/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("Students response status:", studentsResponse.status);
    
    if (!studentsResponse.ok) {
      const errorText = await studentsResponse.text();
      console.error("❌ Students request failed:", errorText);
      return {
        success: false,
        error: `Students request failed: ${studentsResponse.status} - ${errorText}`
      };
    }

    const studentsData = await studentsResponse.json();
    console.log("✅ Students data:", studentsData);

    // Test 3: Get teacher schedule
    console.log("\n📅 Test 3: Getting teacher schedule...");
    const scheduleResponse = await fetch(`${BASE_API_URL}/teacher/schedule/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("Schedule response status:", scheduleResponse.status);
    
    if (!scheduleResponse.ok) {
      const errorText = await scheduleResponse.text();
      console.error("❌ Schedule request failed:", errorText);
      return {
        success: false,
        error: `Schedule request failed: ${scheduleResponse.status} - ${errorText}`
      };
    }

    const scheduleData = await scheduleResponse.json();
    console.log("✅ Schedule data:", scheduleData);

    // Summary
    console.log("\n📊 Test Summary:");
    console.log("- Assigned Classes:", assignmentsData.assigned_classes?.length || 0);
    console.log("- Assigned Subjects:", assignmentsData.assigned_subjects?.length || 0);
    console.log("- Students:", studentsData.students?.length || 0);
    console.log("- Schedule Entries:", scheduleData.schedule?.length || 0);
    console.log("- Permissions:", assignmentsData.permissions ? "✅ Present" : "❌ Missing");

    return {
      success: true,
      data: {
        assignments: assignmentsData,
        students: studentsData,
        schedule: scheduleData
      },
      summary: {
        assignedClasses: assignmentsData.assigned_classes?.length || 0,
        assignedSubjects: assignmentsData.assigned_subjects?.length || 0,
        students: studentsData.students?.length || 0,
        scheduleEntries: scheduleData.schedule?.length || 0,
        hasPermissions: !!assignmentsData.permissions
      }
    };

  } catch (error) {
    console.error("❌ Test failed with error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// Test function to sync teacher assignments (for admins)
export async function testSyncTeacherAssignments(schoolId: string, teacherId: string) {
  const token = getTokenFromCookie("idToken");
  
  console.log("🔄 Testing Teacher Assignment Sync...");
  console.log("School ID:", schoolId);
  console.log("Teacher ID:", teacherId);

  try {
    const response = await fetch(`${BASE_API_URL}/teacher/sync/${schoolId}/${teacherId}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    console.log("Sync response status:", response.status);
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error("❌ Sync request failed:", errorText);
      return {
        success: false,
        error: `Sync request failed: ${response.status} - ${errorText}`
      };
    }

    const syncData = await response.json();
    console.log("✅ Sync result:", syncData);

    return {
      success: true,
      data: syncData
    };

  } catch (error) {
    console.error("❌ Sync test failed with error:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// Helper function to check API connectivity
export async function testAPIConnectivity() {
  console.log("🌐 Testing API Connectivity...");
  console.log("Base URL:", BASE_API_URL);

  try {
    // Simple health check - try to access a basic endpoint
    const response = await fetch(`${BASE_API_URL}/user/list`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    console.log("API Response status:", response.status);
    
    if (response.status === 401) {
      console.log("✅ API is reachable (401 = authentication required, which is expected)");
      return { success: true, message: "API is reachable" };
    } else if (response.ok) {
      console.log("✅ API is reachable and responding");
      return { success: true, message: "API is fully accessible" };
    } else {
      console.log("⚠️ API responded with status:", response.status);
      return { success: true, message: `API responded with status ${response.status}` };
    }

  } catch (error) {
    console.error("❌ API connectivity test failed:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error"
    };
  }
}

// Export all test functions
export const TeacherAssignmentTests = {
  testTeacherAssignments,
  testSyncTeacherAssignments,
  testAPIConnectivity
};
