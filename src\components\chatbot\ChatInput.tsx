'use client';

import React, { useState, useRef, useEffect } from 'react';
import { Send } from 'lucide-react';

interface ChatInputProps {
  onSend: (message: string) => void;
  disabled?: boolean;
}

export default function ChatInput({ onSend, disabled }: ChatInputProps) {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !disabled) {
      onSend(message);
      setMessage('');
      if (textareaRef.current) {
        textareaRef.current.style.height = 'auto';
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const adjustTextareaHeight = () => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${Math.min(textareaRef.current.scrollHeight, 120)}px`;
    }
  };

  useEffect(() => {
    adjustTextareaHeight();
  }, [message]);

  return (
    <form onSubmit={handleSubmit} className="p-2 sm:p-4 border-t dark:border-gray-700 bg-white dark:bg-gray-800">
      <div className="flex items-end space-x-2">
        <div className="flex-1 relative">
          <textarea
            ref={textareaRef}
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Écrivez votre message..."
            className="w-full resize-none rounded-lg border border-gray-200 dark:border-gray-600 
              bg-white dark:bg-gray-800 p-2 sm:p-3 pr-10 sm:pr-12 text-sm sm:text-base 
              focus:outline-none focus:ring-2 focus:ring-teal/50 max-h-32 
              text-gray-900 dark:text-gray-100 scrollbar-none"
            rows={1}
            disabled={disabled}
          />
          <div className="absolute right-1 bottom-1 sm:right-2 sm:bottom-2">
            <button
              type="submit"
              disabled={!message.trim() || disabled}
              className={`p-1.5 sm:p-2 rounded-full transition-colors ${
                message.trim() && !disabled
                  ? 'text-teal hover:bg-teal-50 dark:hover:bg-teal-900/20'
                  : 'text-gray-400 cursor-not-allowed'
              }`}
            >
              <Send className="w-4 h-4 sm:w-5 sm:h-5" />
            </button>
          </div>
        </div>
      </div>
      <p className="text-[10px] sm:text-xs text-gray-500 dark:text-gray-400 mt-1 sm:mt-2 px-1">
        Appuyez sur Entrée pour envoyer, Maj+Entrée pour un saut de ligne
      </p>
    </form>
  );
} 