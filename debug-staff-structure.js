const mongoose = require('mongoose');
const User = require('./src/models/User');
const StaffPermission = require('./src/models/StaffPermission');

// Connect to MongoDB
mongoose.connect('mongodb://localhost:27017/scholarify', {
  useNewUrlParser: true,
  useUnifiedTopology: true,
});

async function debugStaffStructure() {
  try {
    console.log('🔍 Debugging Staff Data Structure...\n');

    // Find a school with staff
    const staffMembers = await User.find({
      role: { $in: ['school_admin', 'teacher', 'bursar', 'dean_of_studies'] }
    })
    .populate('school_ids', 'name')
    .select('-password -temp_password -password_reset_token')
    .limit(1);

    if (staffMembers.length === 0) {
      console.log('❌ No staff members found');
      return;
    }

    const staff = staffMembers[0];
    console.log('✅ Found staff member:', {
      _id: staff._id,
      name: `${staff.first_name} ${staff.last_name}`,
      email: staff.email,
      role: staff.role,
      school_ids: staff.school_ids
    });

    // Get permissions for this staff member
    const schoolId = staff.school_ids[0]._id;
    const permissions = await StaffPermission.findOne({
      user_id: staff._id,
      school_id: schoolId
    });

    console.log('\n📋 Staff Permissions Structure:');
    if (permissions) {
      console.log('✅ Permissions found:', {
        _id: permissions._id,
        role_template: permissions.role_template,
        permissions_keys: Object.keys(permissions.permissions || {}),
        assigned_classes: permissions.assigned_classes?.length || 0
      });

      console.log('\n🔍 Full permissions object structure:');
      console.log('permissions.role_template:', permissions.role_template);
      console.log('permissions.permissions:', typeof permissions.permissions);
      console.log('permissions.assigned_classes:', Array.isArray(permissions.assigned_classes));
    } else {
      console.log('❌ No permissions found for this staff member');
    }

    // Simulate the getStaffBySchool response structure
    console.log('\n📊 Simulated getStaffBySchool response:');
    const staffWithPermissions = {
      ...staff.toObject(),
      permissions: permissions || null
    };

    console.log('Final structure:');
    console.log('staff.permissions?.role_template:', staffWithPermissions.permissions?.role_template);
    console.log('staff.permissions?.permissions:', typeof staffWithPermissions.permissions?.permissions);
    console.log('staff.permissions?.assigned_classes:', Array.isArray(staffWithPermissions.permissions?.assigned_classes));

    // Test the frontend logic
    console.log('\n🧪 Testing frontend logic:');
    const frontendRoleTemplate = staffWithPermissions.permissions?.role_template || "custom";
    console.log('Frontend would set role_template to:', frontendRoleTemplate);

    if (frontendRoleTemplate === "custom") {
      console.log('🚨 PROBLEM: Role is being set to "custom" instead of actual role!');
    } else {
      console.log('✅ Role is correctly set to:', frontendRoleTemplate);
    }

  } catch (error) {
    console.error('❌ Error debugging staff structure:', error);
  } finally {
    mongoose.connection.close();
  }
}

debugStaffStructure();
