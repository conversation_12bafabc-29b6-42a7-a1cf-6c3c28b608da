const express = require('express');
const feeController = require('../controllers/FeesController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// Test route for Fee
router.get('/test', feeController.testFeeResponse);

// GET all fees
router.get('/get-fees', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.getAllFees);

// GET a fee by ID
router.get('/get-fee/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.getFeeById);

// GET fees by school ID
router.get('/get-fees-by-school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.getFeesBySchoolId);

// POST to create a new fee record
router.post('/create-fee', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.createFee);

// PUT to update a fee by ID
router.put('/update-fee/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.updateFeeById);

// DELETE to remove a fee by ID
router.delete('/delete-fee/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.deleteFeeById);

// DELETE multiple fees by IDs
router.delete('/delete-fees', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'bursar']), feeController.deleteMultipleFees);


module.exports = router;
