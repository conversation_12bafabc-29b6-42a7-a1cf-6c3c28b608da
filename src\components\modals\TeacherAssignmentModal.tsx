"use client";

import { useState, useEffect } from "react";
import { X, Save, Loader2, Search, ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { getTimetable } from "@/app/services/TimetableServices";

interface TeacherAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  assignment?: any;
  classes: any[];
  subjects: any[];
  teachers: any[];
  periods: any[];
  loading?: boolean;
}

const DAYS = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
const SCHEDULE_TYPES = ['Normal', 'Exam', 'Event'];

export default function TeacherAssignmentModal({
  isOpen,
  onClose,
  onSubmit,
  assignment,
  classes,
  subjects,
  teachers,
  periods,
  loading = false
}: TeacherAssignmentModalProps) {
  const [formData, setFormData] = useState({
    class_id: '',
    subject_id: '',
    teacher_id: '',
    period_id: '',
    day_of_week: '',
    schedule_type: 'Normal'
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Search states
  const [classSearch, setClassSearch] = useState('');
  const [subjectSearch, setSubjectSearch] = useState('');
  const [teacherSearch, setTeacherSearch] = useState('');
  const [showClassDropdown, setShowClassDropdown] = useState(false);
  const [showSubjectDropdown, setShowSubjectDropdown] = useState(false);
  const [showTeacherDropdown, setShowTeacherDropdown] = useState(false);

  // Assigned periods for selected class
  const [assignedPeriods, setAssignedPeriods] = useState<{[key: string]: string[]}>({});

  // Reset form when modal opens/closes or assignment changes
  useEffect(() => {
    if (isOpen) {
      if (assignment) {
        // Edit mode - populate form with assignment data
        // Extract IDs from objects if they exist, or use direct IDs
        const classId = typeof assignment.class_id === 'object' ? assignment.class_id._id : assignment.class_id;
        const subjectId = typeof assignment.subject_id === 'object' ? assignment.subject_id._id : assignment.subject_id;
        const teacherId = typeof assignment.teacher_id === 'object' ? assignment.teacher_id._id : assignment.teacher_id;

        // For period, we might need to find by period_number if the ID doesn't match
        let periodId = typeof assignment.period_id === 'object' ? assignment.period_id._id : assignment.period_id;

        // If we have period_number but no direct period_id, find the period by number
        if (!periodId && assignment.period_number) {
          const matchingPeriod = periods.find(p => p.period_number === assignment.period_number);
          periodId = matchingPeriod?._id || '';
        }

        console.log('Assignment data for editing:', {
          assignment,
          extractedIds: { classId, subjectId, teacherId, periodId }
        });

        setFormData({
          class_id: classId || '',
          subject_id: subjectId || '',
          teacher_id: teacherId || '',
          period_id: periodId || '',
          day_of_week: assignment.day_of_week || '',
          schedule_type: assignment.schedule_type || 'Normal'
        });
      } else {
        // Create mode - reset form
        setFormData({
          class_id: '',
          subject_id: '',
          teacher_id: '',
          period_id: '',
          day_of_week: '',
          schedule_type: 'Normal'
        });
      }
      setErrors({});
      // Reset search states
      setClassSearch('');
      setSubjectSearch('');
      setTeacherSearch('');
      setShowClassDropdown(false);
      setShowSubjectDropdown(false);
      setShowTeacherDropdown(false);
    }
  }, [isOpen, assignment]);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.relative')) {
        setShowClassDropdown(false);
        setShowSubjectDropdown(false);
        setShowTeacherDropdown(false);
      }
    };

    if (showClassDropdown || showSubjectDropdown || showTeacherDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [showClassDropdown, showSubjectDropdown, showTeacherDropdown]);

  // Fetch assigned periods when class or day changes
  useEffect(() => {
    const fetchAssignedPeriods = async () => {
      if (formData.class_id && formData.day_of_week) {
        try {
          // Extract school_id from the URL or props (assuming it's available)
          const schoolId = window.location.pathname.split('/')[2]; // Assuming URL structure /school-admin/{schoolId}/...

          const response = await getTimetable(schoolId, {
            class_id: formData.class_id,
            day_of_week: formData.day_of_week
          });

          // Extract assigned period IDs for this class and day
          const assignedPeriodIds = response.schedule_records
            .filter((record: any) => record._id !== assignment?._id) // Exclude current assignment when editing
            .map((record: any) => record.period_id);

          setAssignedPeriods(prev => ({
            ...prev,
            [`${formData.class_id}_${formData.day_of_week}`]: assignedPeriodIds
          }));
        } catch (error) {
          console.error('Error fetching assigned periods:', error);
        }
      }
    };

    fetchAssignedPeriods();
  }, [formData.class_id, formData.day_of_week, assignment?._id]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  // Filter functions
  const filteredClasses = classes.filter(cls =>
    cls.name.toLowerCase().includes(classSearch.toLowerCase())
  );

  const filteredSubjects = subjects.filter(subject =>
    subject.name.toLowerCase().includes(subjectSearch.toLowerCase())
  );

  const filteredTeachers = teachers.filter(teacher => {
    const teacherName = teacher.first_name && teacher.last_name ?
      `${teacher.first_name} ${teacher.last_name}` :
      teacher.name || 'Unknown Teacher';
    return teacherName.toLowerCase().includes(teacherSearch.toLowerCase());
  });

  // Get selected item names for display
  const getSelectedClassName = () => {
    const selectedClass = classes.find(cls => cls._id === formData.class_id);
    return selectedClass ? selectedClass.name : '';
  };

  const getSelectedSubjectName = () => {
    const selectedSubject = subjects.find(subject => subject._id === formData.subject_id);
    return selectedSubject ? selectedSubject.name : '';
  };

  const getSelectedTeacherName = () => {
    const selectedTeacher = teachers.find(teacher => teacher._id === formData.teacher_id);
    if (!selectedTeacher) return '';
    return selectedTeacher.first_name && selectedTeacher.last_name ?
      `${selectedTeacher.first_name} ${selectedTeacher.last_name}` :
      selectedTeacher.name || 'Unknown Teacher';
  };

  // Check if a period is already assigned for the selected class and day
  const isPeriodAssigned = (periodId: string) => {
    if (!formData.class_id || !formData.day_of_week) return false;
    const key = `${formData.class_id}_${formData.day_of_week}`;
    return assignedPeriods[key]?.includes(periodId) || false;
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.class_id) newErrors.class_id = 'Class is required';
    if (!formData.subject_id) newErrors.subject_id = 'Subject is required';
    if (!formData.teacher_id) newErrors.teacher_id = 'Teacher is required';
    if (!formData.period_id) newErrors.period_id = 'Period is required';
    if (!formData.day_of_week) newErrors.day_of_week = 'Day of week is required';
    if (!formData.schedule_type) newErrors.schedule_type = 'Schedule type is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(formData);
      onClose();
    } catch (error) {
      console.error('Error submitting assignment:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting && !loading) {
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-widget rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-foreground">
              {assignment ? 'Edit Teacher Assignment' : 'New Teacher Assignment'}
            </h2>
            <button
              onClick={handleClose}
              disabled={isSubmitting || loading}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-800 rounded-lg transition-colors disabled:opacity-50"
            >
              <X className="h-5 w-5 text-foreground" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Class Selection with Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-foreground mb-2">
                  Class <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.class_id ? getSelectedClassName() : classSearch}
                    onChange={(e) => {
                      setClassSearch(e.target.value);
                      if (formData.class_id) {
                        handleInputChange('class_id', '');
                      }
                      setShowClassDropdown(true);
                    }}
                    onFocus={() => setShowClassDropdown(true)}
                    placeholder="Search and select a class"
                    className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                      errors.class_id ? 'border-red-500' : 'border-stroke'
                    }`}
                    disabled={isSubmitting || loading}
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50" />

                  {showClassDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredClasses.length > 0 ? (
                        filteredClasses.map((classItem) => (
                          <div
                            key={classItem._id}
                            onClick={() => {
                              handleInputChange('class_id', classItem._id);
                              setClassSearch('');
                              setShowClassDropdown(false);
                            }}
                            className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground"
                          >
                            {classItem.name}
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-foreground/60">No classes found</div>
                      )}
                    </div>
                  )}
                </div>
                {errors.class_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.class_id}</p>
                )}
              </div>

              {/* Subject Selection with Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-foreground mb-2">
                  Subject <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.subject_id ? getSelectedSubjectName() : subjectSearch}
                    onChange={(e) => {
                      setSubjectSearch(e.target.value);
                      if (formData.subject_id) {
                        handleInputChange('subject_id', '');
                      }
                      setShowSubjectDropdown(true);
                    }}
                    onFocus={() => setShowSubjectDropdown(true)}
                    placeholder="Search and select a subject"
                    className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                      errors.subject_id ? 'border-red-500' : 'border-stroke'
                    }`}
                    disabled={isSubmitting || loading}
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50" />

                  {showSubjectDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredSubjects.length > 0 ? (
                        filteredSubjects.map((subject) => (
                          <div
                            key={subject._id}
                            onClick={() => {
                              handleInputChange('subject_id', subject._id);
                              setSubjectSearch('');
                              setShowSubjectDropdown(false);
                            }}
                            className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground"
                          >
                            {subject.name}
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-foreground/60">No subjects found</div>
                      )}
                    </div>
                  )}
                </div>
                {errors.subject_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.subject_id}</p>
                )}
              </div>

              {/* Teacher Selection with Search */}
              <div className="relative">
                <label className="block text-sm font-medium text-foreground mb-2">
                  Teacher <span className="text-red-500">*</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    value={formData.teacher_id ? getSelectedTeacherName() : teacherSearch}
                    onChange={(e) => {
                      setTeacherSearch(e.target.value);
                      if (formData.teacher_id) {
                        handleInputChange('teacher_id', '');
                      }
                      setShowTeacherDropdown(true);
                    }}
                    onFocus={() => setShowTeacherDropdown(true)}
                    placeholder="Search and select a teacher"
                    className={`w-full px-3 py-2 pr-10 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                      errors.teacher_id ? 'border-red-500' : 'border-stroke'
                    }`}
                    disabled={isSubmitting || loading}
                  />
                  <Search className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-foreground/50" />

                  {showTeacherDropdown && (
                    <div className="absolute z-10 w-full mt-1 bg-widget border border-stroke rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {filteredTeachers.length > 0 ? (
                        filteredTeachers.map((teacher) => (
                          <div
                            key={teacher._id}
                            onClick={() => {
                              handleInputChange('teacher_id', teacher._id);
                              setTeacherSearch('');
                              setShowTeacherDropdown(false);
                            }}
                            className="px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-800 cursor-pointer text-foreground"
                          >
                            {teacher.first_name && teacher.last_name ?
                              `${teacher.first_name} ${teacher.last_name}` :
                              teacher.name || 'Unknown Teacher'}
                          </div>
                        ))
                      ) : (
                        <div className="px-3 py-2 text-foreground/60">No teachers found</div>
                      )}
                    </div>
                  )}
                </div>
                {errors.teacher_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.teacher_id}</p>
                )}
              </div>

              {/* Period Selection with Assignment Badges */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Period <span className="text-red-500">*</span>
                  {formData.class_id && formData.day_of_week && (
                    <span className="text-xs text-foreground/60 ml-2">
                      (Red badges indicate already assigned periods)
                    </span>
                  )}
                </label>
                <select
                  value={formData.period_id}
                  onChange={(e) => handleInputChange('period_id', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.period_id ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a period</option>
                  {periods.map((period) => {
                    const isAssigned = isPeriodAssigned(period._id);
                    return (
                      <option
                        key={period._id}
                        value={period._id}
                        style={{
                          backgroundColor: isAssigned ? '#fee2e2' : 'inherit',
                          color: isAssigned ? '#dc2626' : 'inherit'
                        }}
                      >
                        Period {period.period_number} ({period.start_time.slice(0, 5)} - {period.end_time.slice(0, 5)})
                        {isAssigned ? ' ⚠️ ASSIGNED' : ''}
                      </option>
                    );
                  })}
                </select>

                {/* Visual indicators below the select */}
                {formData.class_id && formData.day_of_week && (
                  <div className="mt-2 flex flex-wrap gap-1">
                    {periods.map((period) => {
                      const isAssigned = isPeriodAssigned(period._id);
                      const isSelected = formData.period_id === period._id;
                      return (
                        <span
                          key={period._id}
                          className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            isSelected
                              ? 'bg-teal text-white'
                              : isAssigned
                                ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300'
                                : 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300'
                          }`}
                        >
                          P{period.period_number}
                          {isSelected && ' ✓'}
                          {isAssigned && !isSelected && ' ⚠️'}
                        </span>
                      );
                    })}
                  </div>
                )}

                {errors.period_id && (
                  <p className="text-red-500 text-sm mt-1">{errors.period_id}</p>
                )}
              </div>

              {/* Day Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Day of Week <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.day_of_week}
                  onChange={(e) => handleInputChange('day_of_week', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.day_of_week ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  <option value="">Select a day</option>
                  {DAYS.map((day) => (
                    <option key={day} value={day}>
                      {day}
                    </option>
                  ))}
                </select>
                {errors.day_of_week && (
                  <p className="text-red-500 text-sm mt-1">{errors.day_of_week}</p>
                )}
              </div>

              {/* Schedule Type Selection */}
              <div>
                <label className="block text-sm font-medium text-foreground mb-2">
                  Schedule Type <span className="text-red-500">*</span>
                </label>
                <select
                  value={formData.schedule_type}
                  onChange={(e) => handleInputChange('schedule_type', e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal bg-widget text-foreground ${
                    errors.schedule_type ? 'border-red-500' : 'border-stroke'
                  }`}
                  disabled={isSubmitting || loading}
                >
                  {SCHEDULE_TYPES.map((type) => (
                    <option key={type} value={type}>
                      {type}
                    </option>
                  ))}
                </select>
                {errors.schedule_type && (
                  <p className="text-red-500 text-sm mt-1">{errors.schedule_type}</p>
                )}
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-end space-x-4 pt-6 border-t border-stroke">
              <button
                type="button"
                onClick={handleClose}
                disabled={isSubmitting || loading}
                className="px-4 py-2 text-foreground/70 hover:text-foreground transition-colors disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting || loading}
                className="flex items-center space-x-2 px-6 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors disabled:opacity-50"
              >
                {(isSubmitting || loading) && <Loader2 className="h-4 w-4 animate-spin" />}
                <Save className="h-4 w-4" />
                <span>{assignment ? 'Update Assignment' : 'Create Assignment'}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
}
