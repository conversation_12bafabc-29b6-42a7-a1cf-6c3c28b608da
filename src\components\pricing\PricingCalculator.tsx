"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Calculator, Users, MessageCircle, TrendingUp, Info } from 'lucide-react';
import { SubscriptionPlanSchema } from '@/app/models/SchoolSubscriptionModel';

interface PricingCalculatorProps {
  plans: SubscriptionPlanSchema[];
}

interface CalculationResult {
  plan_name: string;
  monthly_cost: number;
  yearly_cost: number;
  credits_needed: number;
  breakdown: {
    students: number;
    chatbot_messages: number;
    total_credits: number;
  };
}

export default function PricingCalculator({ plans }: PricingCalculatorProps) {
  const [studentsCount, setStudentsCount] = useState(50);
  const [chatbotMessages, setChatbotMessages] = useState(0);
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [calculations, setCalculations] = useState<CalculationResult[]>([]);

  useEffect(() => {
    calculateCosts();
  }, [studentsCount, chatbotMessages, plans]);

  const calculateCosts = () => {
    const results: CalculationResult[] = plans.map(plan => {
      // Calcul des crédits nécessaires
      let creditsForStudents = studentsCount; // 1 crédit par étudiant
      let creditsForChatbot = plan.chatbot_enabled ? chatbotMessages : 0;
      let totalCredits = creditsForStudents + creditsForChatbot;

      // Coût mensuel et annuel
      let monthlyCost = totalCredits * plan.price_per_credit;
      let yearlyCost = monthlyCost * 12;

      // Réduction annuelle (exemple: 20% de réduction)
      if (plan.plan_name !== 'custom') {
        yearlyCost = yearlyCost * 0.8; // 20% de réduction
      }

      return {
        plan_name: plan.plan_name,
        monthly_cost: monthlyCost,
        yearly_cost: yearlyCost,
        credits_needed: totalCredits,
        breakdown: {
          students: creditsForStudents,
          chatbot_messages: creditsForChatbot,
          total_credits: totalCredits
        }
      };
    });

    setCalculations(results);
  };

  const getPlanDisplayName = (planName: string) => {
    const plan = plans.find(p => p.plan_name === planName);
    return plan?.display_name || planName;
  };

  const getPlanColor = (planName: string) => {
    switch (planName) {
      case 'basic': return 'text-green-600 bg-green-50 border-green-200';
      case 'standard': return 'text-blue-600 bg-blue-50 border-blue-200';
      case 'custom': return 'text-purple-600 bg-purple-50 border-purple-200';
      default: return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
      <div className="flex items-center mb-6">
        <Calculator className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
        <h3 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
          Calculateur de coûts
        </h3>
      </div>

      {/* Input Controls */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        {/* Students Count */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <Users className="h-4 w-4 inline mr-2" />
            Nombre d'étudiants
          </label>
          <div className="relative">
            <input
              type="range"
              min="1"
              max="1000"
              value={studentsCount}
              onChange={(e) => setStudentsCount(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>1</span>
              <span>1000</span>
            </div>
          </div>
          <div className="mt-2 text-center">
            <span className="text-2xl font-bold text-blue-600">{studentsCount}</span>
            <span className="text-gray-600 ml-1">étudiants</span>
          </div>
        </div>

        {/* Chatbot Messages */}
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            <MessageCircle className="h-4 w-4 inline mr-2" />
            Messages chatbot/mois
          </label>
          <div className="relative">
            <input
              type="range"
              min="0"
              max="500"
              value={chatbotMessages}
              onChange={(e) => setChatbotMessages(parseInt(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>0</span>
              <span>500</span>
            </div>
          </div>
          <div className="mt-2 text-center">
            <span className="text-2xl font-bold text-purple-600">{chatbotMessages}</span>
            <span className="text-gray-600 ml-1">messages</span>
          </div>
        </div>
      </div>

      {/* Calculation Results */}
      <div className="space-y-4">
        <h4 className="text-lg font-semibold text-gray-900 mb-4">
          Estimation des coûts par plan :
        </h4>

        {calculations.map((calc, index) => {
          const plan = plans.find(p => p.plan_name === calc.plan_name);
          if (!plan) return null;

          return (
            <motion.div
              key={calc.plan_name}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.1 }}
              className={`border-2 rounded-lg p-6 ${getPlanColor(calc.plan_name)} ${
                plan.is_popular ? 'ring-2 ring-blue-300' : ''
              }`}
            >
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h5 className="text-lg font-semibold">
                    {getPlanDisplayName(calc.plan_name)}
                    {plan.is_popular && (
                      <span className="ml-2 px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                        Populaire
                      </span>
                    )}
                  </h5>
                  {calc.plan_name === 'custom' ? (
                    <p className="text-sm opacity-75">Tarification personnalisée</p>
                  ) : (
                    <p className="text-sm opacity-75">
                      {plan.price_per_credit.toLocaleString()} FCFA/crédit
                    </p>
                  )}
                </div>
                
                {calc.plan_name !== 'custom' && (
                  <div className="text-right">
                    <div className="text-2xl font-bold">
                      {calc.monthly_cost.toLocaleString()} FCFA
                    </div>
                    <div className="text-sm opacity-75">par mois</div>
                  </div>
                )}
              </div>

              {calc.plan_name !== 'custom' && (
                <>
                  {/* Breakdown */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="text-center p-3 bg-white bg-opacity-50 rounded-lg">
                      <div className="text-lg font-semibold">{calc.breakdown.students}</div>
                      <div className="text-xs opacity-75">Crédits étudiants</div>
                    </div>
                    
                    {plan.chatbot_enabled && (
                      <div className="text-center p-3 bg-white bg-opacity-50 rounded-lg">
                        <div className="text-lg font-semibold">{calc.breakdown.chatbot_messages}</div>
                        <div className="text-xs opacity-75">Crédits chatbot</div>
                      </div>
                    )}
                    
                    <div className="text-center p-3 bg-white bg-opacity-50 rounded-lg">
                      <div className="text-lg font-semibold">{calc.breakdown.total_credits}</div>
                      <div className="text-xs opacity-75">Total crédits</div>
                    </div>
                  </div>

                  {/* Annual Savings */}
                  <div className="flex items-center justify-between p-3 bg-white bg-opacity-50 rounded-lg">
                    <div className="flex items-center">
                      <TrendingUp className="h-4 w-4 mr-2" />
                      <span className="text-sm font-medium">Coût annuel (avec réduction)</span>
                    </div>
                    <div className="text-right">
                      <div className="font-semibold">
                        {calc.yearly_cost.toLocaleString()} FCFA
                      </div>
                      <div className="text-xs text-green-600">
                        Économie: {(calc.monthly_cost * 12 - calc.yearly_cost).toLocaleString()} FCFA
                      </div>
                    </div>
                  </div>
                </>
              )}

              {/* Custom Plan Message */}
              {calc.plan_name === 'custom' && (
                <div className="flex items-start p-3 bg-white bg-opacity-50 rounded-lg">
                  <Info className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm">
                    <p className="font-medium mb-1">Tarification sur mesure</p>
                    <p className="opacity-75">
                      Contactez-nous pour obtenir un devis personnalisé basé sur vos besoins spécifiques.
                      Nous proposons des tarifs préférentiels pour les gros volumes.
                    </p>
                  </div>
                </div>
              )}
            </motion.div>
          );
        })}
      </div>

      {/* Additional Info */}
      <div className="mt-8 p-4 bg-blue-50 rounded-lg">
        <div className="flex items-start">
          <Info className="h-5 w-5 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
          <div className="text-sm text-blue-800">
            <p className="font-medium mb-2">Notes importantes :</p>
            <ul className="space-y-1 text-blue-700">
              <li>• 1 crédit = 1 étudiant créé dans le système</li>
              <li>• Les messages chatbot ne sont disponibles qu'avec les plans Standard et Custom</li>
              <li>• Les crédits n'expirent jamais une fois achetés</li>
              <li>• Réduction de 20% sur les paiements annuels</li>
              <li>• 5 crédits gratuits offerts à l'inscription</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
}
