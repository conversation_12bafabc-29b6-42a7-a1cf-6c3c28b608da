"use client";

import { useState, useEffect } from "react";
import { BASE_API_URL } from "@/app/services/AuthContext";
import { getTokenFromCookie } from "@/app/services/UserServices";

export default function ApiDebug() {
    const [debugInfo, setDebugInfo] = useState<any>(null);
    const [showDebug, setShowDebug] = useState(false);

    useEffect(() => {
        const token = getTokenFromCookie("idToken");
        setDebugInfo({
            baseApiUrl: BASE_API_URL,
            hasToken: !!token,
            tokenPrefix: token ? token.substring(0, 20) + "..." : "No token",
            userAgent: navigator.userAgent,
            windowLocation: window.location.href,
        });
    }, []);

    if (!showDebug) {
        return (
            <button
                onClick={() => setShowDebug(true)}
                className="fixed bottom-4 right-4 bg-blue-500 text-white px-3 py-1 rounded text-sm opacity-50 hover:opacity-100"
            >
                Debug API
            </button>
        );
    }

    return (
        <div className="fixed bottom-4 right-4 bg-white dark:bg-gray-800 border rounded-lg p-4 shadow-lg max-w-md text-sm">
            <div className="flex justify-between items-center mb-2">
                <h3 className="font-bold">API Debug Info</h3>
                <button
                    onClick={() => setShowDebug(false)}
                    className="text-gray-500 hover:text-gray-700"
                >
                    ×
                </button>
            </div>

            {debugInfo && (
                <div className="space-y-2">
                    <div>
                        <strong>API URL:</strong> {debugInfo.baseApiUrl}
                    </div>
                    <div>
                        <strong>Has Token:</strong>{" "}
                        {debugInfo.hasToken ? "Yes" : "No"}
                    </div>
                    <div>
                        <strong>Token:</strong> {debugInfo.tokenPrefix}
                    </div>
                    <div>
                        <strong>Location:</strong> {debugInfo.windowLocation}
                    </div>
                    <button
                        onClick={() => {
                            fetch(
                                `${BASE_API_URL}/academic-years/get-academic-years`,
                                {
                                    method: "GET",
                                    headers: {
                                        "Content-Type": "application/json",
                                        Authorization: `Bearer ${getTokenFromCookie(
                                            "idToken"
                                        )}`,
                                    },
                                }
                            )
                                .then((response) => {
                                    console.log("Test API Response:", response);
                                    return response.json();
                                })
                                .then((data) => {
                                    console.log("Test API Data:", data);
                                    alert("Check console for API response");
                                })
                                .catch((error) => {
                                    console.error("Test API Error:", error);
                                    alert("API Test Failed - Check console");
                                });
                        }}
                        className="bg-blue-500 text-white px-2 py-1 rounded text-xs"
                    >
                        Test API
                    </button>
                </div>
            )}
        </div>
    );
}
