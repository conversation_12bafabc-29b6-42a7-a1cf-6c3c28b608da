const mongoose = require('mongoose');

const CreditTransactionSchema = new mongoose.Schema({
    school_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'School',
        required: true
    },
    academicYear_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'AcademicYear',
        required: true
    },
    payment_method: {
        type: String,
        enum: ['manual', 'automatic', 'gift'],
        required: true
    },
    amountPaid: {
        type: Number,
        required: true,
        default: 0
    },
    credit: {
        type: Number,
        required: true,
    },
    paidAt: {
        type: Date,
        default: Date.now
    }
}, {
    timestamps: true
});

const CreditTransaction = mongoose.models.CreditTransaction || mongoose.model('CreditTransaction', CreditTransactionSchema);

module.exports = CreditTransaction;
