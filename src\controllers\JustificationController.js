const AttendanceJustification = require('../models/AttendanceJustification');
const Attendance = require('../models/Attendance');
const Student = require('../models/Student');
const User = require('../models/User');

// Create a new justification
const createJustification = async (req, res) => {
  try {
    const { attendance_id, text } = req.body;
    const file_url = req.file ? req.file.path : null;
    const submitted_by = req.user._id;

    if (!attendance_id) {
      return res.status(400).json({ message: 'attendance_id is required' });
    }

    // Validate that at least one justification (text or file) is provided
    if ((!text || text.trim() === '') && !file_url) {
      return res.status(400).json({ message: 'Either text or file must be provided as justification' });
    }

    // Check attendance exists
    const attendance = await Attendance.findById(attendance_id);
    if (!attendance) {
      return res.status(404).json({ message: 'Attendance record not found' });
    }

    // Check if justification already exists for this attendance (unique constraint)
    const existing = await AttendanceJustification.findOne({ attendance_id });
    if (existing) {
      return res.status(409).json({ message: 'Justification for this attendance already exists' });
    }

    const justification = new AttendanceJustification({
      attendance_id,
      submitted_by,
      justification_type: (text && file_url) ? "TextAndFile" : text ? "Text" : "File", // optional but informative
      text: text ? text.trim() : null,
      file_url: file_url || null,
    });

    await justification.save();

    res.status(201).json({ message: 'Justification created successfully', justification });
  } catch (err) {
    console.error('Create Justification error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get justification by ID with populated data
const getJustificationById = async (req, res) => {
  try {
    const { id } = req.params;

    const justification = await AttendanceJustification.findById(id)
      .populate({
        path: 'attendance_id',
        populate: [
          { path: 'student_id', select: 'student_id first_name last_name school_id' },
          { path: 'school_id', select: 'name' },
          { path: 'schedule_id' }
        ]
      })
      .populate('submitted_by', 'name role email')
      .populate('reviewed_by', 'name role email');

    if (!justification) {
      return res.status(404).json({ message: 'Justification not found' });
    }

    res.json(justification);

  } catch (err) {
    console.error('Get justification error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get justifications by school_id (using attendance.school_id)
const getJustificationsBySchool = async (req, res) => {
  try {
    const { school_id } = req.params;

    const justifications = await AttendanceJustification.find()
      .populate({
        path: 'attendance_id',
        match: { school_id }, // filter attendance by school_id
        populate: { path: 'student_id', select: 'student_id first_name last_name' }
      })
      .populate('submitted_by', 'name role email');

    // Filter out those where attendance_id is null due to no match on school_id
    const filtered = justifications.filter(j => j.attendance_id);

    res.json(filtered);

  } catch (err) {
    console.error('Get justifications by school error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Get justifications by student_id (using attendance.student_id)
const getJustificationsByStudent = async (req, res) => {
  try {
    const { student_id } = req.params;

    const justifications = await AttendanceJustification.find()
      .populate({
        path: 'attendance_id',
        match: { student_id }, // filter attendance by student_id
        populate: { path: 'school_id', select: 'name' }
      })
      .populate('submitted_by', 'name role email');

    // Filter out those where attendance_id is null due to no match on student_id
    const filtered = justifications.filter(j => j.attendance_id);

    res.json(filtered);

  } catch (err) {
    console.error('Get justifications by student error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

// Review a justification: accept/reject and add comment
const reviewJustification = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, review_comment } = req.body;
    const reviewed_by = req.user._id;

    if (!['Accepted', 'Rejected', 'Pending'].includes(status)) {
      return res.status(400).json({ message: 'Invalid status value' });
    }

    const justification = await AttendanceJustification.findById(id);

    if (!justification) {
      return res.status(404).json({ message: 'Justification not found' });
    }

    justification.status = status;
    justification.review_comment = review_comment || '';
    justification.reviewed_by = reviewed_by;

    await justification.save();

    res.json({ message: 'Justification reviewed successfully', justification });

  } catch (err) {
    console.error('Review justification error:', err);
    res.status(500).json({ message: 'Server error' });
  }
};

module.exports = {
  createJustification,
  getJustificationById,
  getJustificationsBySchool,
  getJustificationsByStudent,
  reviewJustification,
};
