const express = require('express');
const periodController = require('../controllers/periodController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// New routes for school-specific periods
router.get('/school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), periodController.getPeriods);
router.post('/school/:school_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), periodController.createPeriod);
router.put('/school/:school_id/:period_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), periodController.updatePeriod);
router.delete('/school/:school_id/:period_id', authenticate, authorize(['admin', 'super', 'school_admin', 'dean_of_studies']), periodController.deletePeriod);

// Legacy routes (keeping for backward compatibility)
router.get('/test', periodController.testPeriodResponse || ((req, res) => res.json({ message: 'Period routes working' })));

module.exports = router;
