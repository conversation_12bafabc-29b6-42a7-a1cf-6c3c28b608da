"use client";

import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import { ArrowLeft } from "lucide-react";

import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { Coins } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { CreditTransactionSchema } from "@/app/models/CreditTransactionModel";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import {
    createCreditTransaction,
    getCreditTransactionsBySchoolId,
} from "@/app/services/CreditTransactionServices";
import { SchoolSchema } from "@/app/models/SchoolModel";
import DataTableFix from "@/components/utils/TableFix";
import NotificationCard from "@/components/NotificationCard";
import { motion } from "framer-motion";
import CreditTransactionModal from "../components/CreditTransactionModal";
import { getAcademicYears } from "@/app/services/AcademicYearServices";
import { AcademicYearSchema } from "@/app/models/AcademicYear";
import ActionButton from "@/components/ActionButton";
import BackRounded from "@/components/BackRounded";

export default function Page() {
    const BASE_URL = "/super-admin";

    function Credit() {
        const router = useRouter();
        const searchParams = useSearchParams();
        const schoolId = searchParams.get("id");

        const [school, setSchool] = useState<SchoolSchema | null>(null);
        const [transactions, setTransactions] = useState<CreditTransactionSchema[]>([]);
        const [academicYears, setAcademicYears] = useState<AcademicYearSchema[]>([]);
        const [loadingData, setLoadingData] = useState(false);

        const [isSubmitting, setIsSubmitting] = useState(false);
        const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);

        const [isNotificationCard, setIsNotificationCard] = useState(false);
        const [notificationMessage, setNotificationMessage] = useState("");
        const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");

        const [isModalOpen, setIsModalOpen] = useState(false);

        // ──────────────────────────────────────────────────────────────────────────────
        // Fetch data function
        const fetchData = async () => {
            if (!schoolId) return;
            setLoadingData(true);
            try {
                const [fetchedTransactions, fetchedSchool, fetchAcademicYear] = await Promise.all([
                    getCreditTransactionsBySchoolId(schoolId),
                    getSchoolBy_id(schoolId),
                    getAcademicYears(),
                ]);

                setTransactions(fetchedTransactions);
                setSchool(fetchedSchool);
                setAcademicYears(fetchAcademicYear);
            } catch (error: any) {
                const message = error?.message || "Unable to load data";
                console.error("Data fetch error:", message, error);
                setNotificationMessage(`Unable to load data: ${message}`);
                setNotificationType("error");
                setIsNotificationCard(true);
            } finally {
                setLoadingData(false);
            }
        };

        useEffect(() => {
            fetchData();
            // eslint-disable-next-line react-hooks/exhaustive-deps
        }, [schoolId]);

        // ──────────────────────────────────────────────────────────────────────────────
        // Navigation setup (now safely inside, can reference `school`)
        const navigation = {
            icon: Coins,
            baseHref: `${BASE_URL}/credit`,
            title: `Credit Transactions – ${school?.name ?? "Loading..."}`,
        };

        // ──────────────────────────────────────────────────────────────────────────────
        // Table config
        const columns = [
            {
                header: "Transaction ID",
                accessor: (row: CreditTransactionSchema) => row._id,
            },
            {
                header: "School",
                accessor: () => school?.name ?? "Unknown School",
            },
            {
                header: "Date",
                accessor: (row: CreditTransactionSchema) =>
                    row.createdAt ? new Date(row.createdAt).toLocaleString() : "N/A",
            },
            {
                header: "Credit Bought",
                accessor: (row: CreditTransactionSchema) => row.credit,
            },
            {
                header: "Method",
                accessor: (row: CreditTransactionSchema) => row.payment_method,
            },
            {
                header: "Amount Paid (XAF)",
                accessor: (row: CreditTransactionSchema) => row.amountPaid,
            },
        ];

        const actions: Array<{
            label: string;
            onClick: (trans: CreditTransactionSchema) => void;
        }> = [
                // {
                //     label: "View",
                //     onClick: (trans: CreditTransactionSchema) => {
                //         router.push(`${BASE_URL}/credit/manage/view?id=${trans._id}`);
                //     },
                // },
            ];

        // ──────────────────────────────────────────────────────────────────────────────
        // Handler to send credits
        const handleSendCredits = async (transactionData: CreditTransactionSchema) => {
            if (!schoolId) {
                setNotificationMessage("No school selected");
                setNotificationType("error");
                setIsNotificationCard(true);
                return;
            }

            setIsSubmitting(true);
            setSubmitStatus(null);

            const creditTransaction: CreditTransactionSchema = {
                ...transactionData,
                school_id: schoolId,
            };

            try {
                const response = await createCreditTransaction(creditTransaction);
                if (response) {

                    setSubmitStatus("success");
                    setNotificationMessage("Credit transaction sent successfully");
                    setNotificationType("success");
                    setIsNotificationCard(true);
                    fetchData();
                }
            } catch (error: any) {
                setSubmitStatus("failure");
                setNotificationMessage(error.message || "Error sending credit transaction");
                setNotificationType("error");
                setIsNotificationCard(true);
            } finally {
                setIsSubmitting(false);
            }
        };

        // ──────────────────────────────────────────────────────────────────────────────
        return (
            <SuperLayout
                navigation={navigation}
                showGoPro={true}
                onLogout={() => console.log("Logged out")}
            >
                <div>
                    {/* Notification */}
                    {isNotificationCard && (
                        <NotificationCard
                            title="Notification"
                            icon={
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                                    <path
                                        d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z"
                                        stroke="#15803d"
                                        strokeWidth="1.5"
                                    />
                                    <path
                                        d="M7.75 11.9999L10.58 14.8299L16.25 9.16992"
                                        stroke="#15803d"
                                        strokeWidth="1.5"
                                    />
                                </svg>
                            }
                            message={notificationMessage}
                            onClose={() => setIsNotificationCard(false)}
                            type={notificationType}
                            isVisible={isNotificationCard}
                            isFixed={true}
                        />
                    )}

                    {/* Actions */}
                    <div className="flex justify-between mb-4">
                        <BackRounded/>
                        <ActionButton action="sendCredit" onClick={() => setIsModalOpen(true)} />

                    </div>


                    {/* Modal */}
                    {isModalOpen && (
                        <CreditTransactionModal
                            onClose={() => {
                                setIsModalOpen(false);
                                setSubmitStatus(null);
                            }}
                            onSave={handleSendCredits}
                            submitStatus={submitStatus}
                            isSubmitting={isSubmitting}
                            schools={school ? [school] : []}
                            academicYears={academicYears}
                        />
                    )}

                    {/* Total Credit Card */}
                    <div className="mb-6 flex justify-start">
                        {loadingData || school === null ? (
                            <div className="bg-gray-100 rounded-lg shadow-md border border-gray-300 px-6 py-4 max-w-xs w-full animate-pulse">
                                <div className="h-4 w-32 mb-4 bg-gray-200 rounded"></div>
                                <div className="h-10 w-20 bg-gray-200 rounded"></div>
                            </div>
                        ) : (
                            <div className="bg-background shadow-md border border-gray-200 rounded-lg px-6 py-4 max-w-xs w-full">
                                <h3 className="text-foreground text-sm text-gray-500 mb-1">
                                    Total Current Credit
                                </h3>
                                <p className="text-2xl font-bold text-teal">
                                    {typeof school?.credit === "number" ? school.credit : 0}
                                </p>
                            </div>
                        )}
                    </div>


                    {/* Transactions Table */}
                    <DataTableFix
                        columns={columns}
                        data={transactions}
                        actions={actions}
                        defaultItemsPerPage={5}
                        loading={loadingData}
                        onLoadingChange={setLoadingData}
                        showCheckbox={false}
                    />
                </div>
            </SuperLayout>
        );
    }

    return (
        <Suspense
            fallback={
                <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
                    <CircularLoader size={32} color="teal" />
                </div>
            }
        >
            <Credit />
        </Suspense>
    );
}
