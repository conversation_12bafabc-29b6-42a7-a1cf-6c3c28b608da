const mongoose = require('mongoose');

// Define the schema for the Term model
const termSchema = new mongoose.Schema({
  school_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "School",
    required: true,
  },
  name: {
    type: String,
    required: true, // e.g., "Premier Trimestre", "Deuxième Trimestre"
  },
  term_number: {
    type: Number,
    required: true, // 1, 2, 3
    min: 1,
    max: 3
  },
  sequences: [{
    sequence_number: {
      type: Number,
      required: true, // 1, 2, 3, 4, 5, 6
      min: 1,
      max: 6
    },
    sequence_name: {
      type: String,
      required: true, // e.g., "1ère Séquence", "2ème Séquence"
    }
  }],
  academic_year: {
    type: String,
    required: true, // e.g., "2024-2025"
  },
  start_date: {
    type: Date,
    required: true,
  },
  end_date: {
    type: Date,
    required: true,
  },
  is_current: {
    type: Boolean,
    default: false, // Only one term can be current at a time per school
  },
  is_active: {
    type: <PERSON><PERSON><PERSON>,
    default: true,
  }
}, {
  timestamps: true // Automatically adds createdAt and updatedAt fields
});

// Ensure only one current term per school
termSchema.index({ school_id: 1, is_current: 1 }, { 
  unique: true, 
  partialFilterExpression: { is_current: true } 
});

// Ensure unique term numbers per school per academic year
termSchema.index({ school_id: 1, term_number: 1, academic_year: 1 }, { unique: true });

// Use the model if it's already defined, or create a new one
const Term = mongoose.models.Term || mongoose.model('Term', termSchema);

module.exports = Term;
