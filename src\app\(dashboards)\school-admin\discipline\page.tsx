"use client";

import { AlertTriangle } from "lucide-react";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import React, { Suspense, useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import useAuth from "@/app/hooks/useAuth";
import DataTableFix from "@/components/utils/TableFix";
import { motion } from "framer-motion";
import NotificationCard from "@/components/NotificationCard";
import { createSuccessNotification, createErrorNotification, NotificationState } from "@/app/types/notification";
import { DisciplineSchema } from "@/app/models/Discipline";
import { getDisciplines, createDiscipline, updateDiscipline, deleteDiscipline, deleteMultipleDisciplines } from "@/app/services/DisciplineServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import DisciplineModal from "@/components/modals/DisciplineModal";
import PasswordConfirmDeleteModal from "@/components/modals/PasswordConfirmDeleteModal";
import DisciplineSkeleton from "@/components/skeletons/DisciplineSkeleton";
import { verifyPassword } from "@/app/services/UserServices";

const BASE_URL = "/school-admin";

const navigation = {
  icon: AlertTriangle,
  baseHref: `${BASE_URL}/discipline`,
  title: "Discipline Records"
};

interface Student {
  _id: string;
  name: string;
  student_id: string;
}

function DisciplineContent() {
  const [disciplines, setDisciplines] = useState<DisciplineSchema[]>([]);
  const [students, setStudents] = useState<Student[]>([]);
  const [loadingData, setLoadingData] = useState(true);
  const [selectedDisciplines, setSelectedDisciplines] = useState<DisciplineSchema[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [disciplineToEdit, setDisciplineToEdit] = useState<DisciplineSchema | null>(null);
  const [disciplineToDelete, setDisciplineToDelete] = useState<DisciplineSchema | null>(null);
  const [deleteType, setDeleteType] = useState<"single" | "multiple">("single");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<NotificationState | null>(null);
  const [clearSelection, setClearSelection] = useState(false);
  const { user } = useAuth();
  const router = useRouter();

  // Columns for the table
  const columns = [
    {
      header: "Discipline ID",
      accessor: (row: DisciplineSchema) => (
        <span className="font-medium">{row.discipline_id}</span>
      )
    },
    {
      header: "Student",
      accessor: (row: DisciplineSchema) => {
        const student = students.find(s => s._id === row.student_id);
        return student ? `${student.name} (${student.student_id})` : "Unknown Student";
      }
    },
    {
      header: "Comments",
      accessor: (row: DisciplineSchema) => (
        <span className="text-sm">{row.comments || "No comments"}</span>
      )
    },
    {
      header: "Created At",
      accessor: (row: DisciplineSchema) => {
        if (!row.createdAt) return "N/A";
        try {
          return new Date(row.createdAt).toLocaleDateString();
        } catch {
          return "Invalid Date";
        }
      }
    }
  ];

  // Actions for the table
  const actions = [
    {
      label: "Edit",
      onClick: (discipline: DisciplineSchema) => {
        handleEditDiscipline(discipline);
      },
    },
    {
      label: "Delete",
      onClick: (discipline: DisciplineSchema) => {
        handleDeleteDiscipline(discipline);
      },
    },
  ];

  // Load data on page load
  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      setLoadingData(true);
      
      // Fetch disciplines and students in parallel
      const [disciplinesData, studentsData] = await Promise.all([
        getDisciplines(),
        user?.school_ids?.[0] ? getStudentsBySchool(user.school_ids[0]) : []
      ]);

      // Filter disciplines by school if user has school_ids
      if (user && user.school_ids && user.school_ids.length > 0) {
        const schoolId = user.school_ids[0];
        const filteredDisciplines = disciplinesData.filter(discipline => discipline.school_id === schoolId);
        setDisciplines(filteredDisciplines);
      } else {
        setDisciplines(disciplinesData);
      }

      setStudents(studentsData);
    } catch (error) {
      console.error("Error fetching data:", error);
      setSubmitStatus(createErrorNotification("Failed to fetch discipline records"));
    } finally {
      setLoadingData(false);
    }
  };

  // Handle creating new discipline
  const handleCreateDiscipline = () => {
    setDisciplineToEdit(null);
    setIsModalOpen(true);
  };

  // Handle editing discipline
  const handleEditDiscipline = (discipline: DisciplineSchema) => {
    setDisciplineToEdit(discipline);
    setIsModalOpen(true);
  };

  // Handle deleting single discipline
  const handleDeleteDiscipline = (discipline: DisciplineSchema) => {
    setDisciplineToDelete(discipline);
    setDeleteType("single");
    setIsDeleteModalOpen(true);
  };

  // Handle deleting multiple disciplines
  const handleDeleteMultiple = (selectedIds: string[]) => {
    setDeleteType("multiple");
    setDisciplineToDelete(null);
    setIsDeleteModalOpen(true);
  };

  // Handle selection change
  const handleSelectionChange = (selected: DisciplineSchema[]) => {
    setSelectedDisciplines(selected);
  };

  // Handle save (create or update)
  const handleSave = async (disciplineData: any) => {
    setIsSubmitting(true);
    try {
      if (disciplineToEdit) {
        // Update existing discipline
        await updateDiscipline(disciplineToEdit.discipline_id, disciplineData);
        setSubmitStatus(createSuccessNotification("Discipline record updated successfully"));
      } else {
        // Create new discipline
        await createDiscipline(disciplineData);
        setSubmitStatus(createSuccessNotification("Discipline record created successfully"));
      }
      
      setIsModalOpen(false);
      setDisciplineToEdit(null);
      await fetchData(); // Refresh the list
    } catch (error) {
      console.error("Error saving discipline:", error);
      setSubmitStatus(createErrorNotification("Failed to save discipline record"));
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle delete confirmation with password
  const handleDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    try {
      // Verify password first
      if (!user?.email) {
        throw new Error("User email not found");
      }

      await verifyPassword(user.email, password);

      if (disciplineToDelete) {
        // Delete single discipline
        await deleteDiscipline(disciplineToDelete._id);
        setSubmitStatus(createSuccessNotification("Discipline record deleted successfully"));
      } else if (selectedDisciplines.length > 0) {
        // Delete multiple disciplines
        const ids = selectedDisciplines.map(discipline => discipline._id);
        await deleteMultipleDisciplines(ids);
        setSubmitStatus(createSuccessNotification(`${selectedDisciplines.length} discipline records deleted successfully`));
        setSelectedDisciplines([]);
      }

      setIsDeleteModalOpen(false);
      setDisciplineToDelete(null);
      await fetchData(); // Refresh the list
    } catch (error) {
      console.error("Error deleting discipline record(s):", error);
      if (error instanceof Error && error.message.includes("password")) {
        setSubmitStatus(createErrorNotification("Invalid password"));
      } else {
        setSubmitStatus(createErrorNotification("Failed to delete discipline record(s)"));
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  if (loadingData) {
    return <DisciplineSkeleton />;
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-text">Discipline Records Management</h1>

      {submitStatus && (
        <div className="mb-4">
          <NotificationCard
            type={submitStatus.type}
            title={submitStatus.title}
            message={submitStatus.message}
            onClose={() => setSubmitStatus(null)}
            isVisible={true}
          />
        </div>
      )}

      <motion.button
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
        transition={{ type: 'spring', stiffness: 300 }}
        onClick={handleCreateDiscipline}
        className="mb-4 px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600 transition-colors"
      >
        Add New Discipline Record
      </motion.button>

      <DataTableFix<DisciplineSchema>
        data={disciplines}
        columns={columns}
        actions={actions}
        defaultItemsPerPage={10}
        onSelectionChange={handleSelectionChange}
        handleDeleteMultiple={handleDeleteMultiple}
        clearSelection={clearSelection}
        onSelectionCleared={() => setClearSelection(false)}
      />

      {/* Discipline Modal */}
      <DisciplineModal
        isOpen={isModalOpen}
        onClose={() => {
          setIsModalOpen(false);
          setDisciplineToEdit(null);
        }}
        onSave={handleSave}
        discipline={disciplineToEdit}
        isSubmitting={isSubmitting}
        students={students}
      />

      {/* Delete Confirmation Modal with Password */}
      <PasswordConfirmDeleteModal
        isOpen={isDeleteModalOpen}
        onClose={() => {
          setIsDeleteModalOpen(false);
          setDisciplineToDelete(null);
        }}
        onConfirm={handleDeleteConfirm}
        title={disciplineToDelete ? "Delete Discipline Record" : "Delete Selected Discipline Records"}
        message={
          disciplineToDelete
            ? `Are you sure you want to delete the discipline record "${disciplineToDelete.discipline_id}"? This action cannot be undone.`
            : `Are you sure you want to delete ${selectedDisciplines.length} selected discipline records? This action cannot be undone.`
        }
        itemName={disciplineToDelete?.discipline_id}
        itemCount={selectedDisciplines.length}
        type={disciplineToDelete ? "single" : "multiple"}
        loading={isSubmitting}
      />
    </div>
  );
}

export default function Page() {
  const { logout } = useAuth();
  return (
    <Suspense fallback={
      <div>
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50">
          <CircularLoader size={32} color="teal" />
        </div>
      </div>
    }>
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <DisciplineContent />
      </SchoolLayout>
    </Suspense>
  );
}
