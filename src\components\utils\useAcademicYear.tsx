import { useState, useEffect } from 'react';
import { getAcademicYears } from '@/app/services/AcademicYearServices';
import { AcademicYearSchema } from '@/app/models/AcademicYear';

const useAcademicYear = () => {
  const [currentAcademicYear, setCurrentAcademicYear] = useState<string>('');
  const [yearLoading, setYearLoading] = useState(false);
  const [allAcademicYears, setAllAcademicYears] = useState<AcademicYearSchema[]>([]);

  const getCurrentAcademicYear = (academicYears: AcademicYearSchema[]): string => {
    const normalize = (dateStr: string | Date) => {
      const d = new Date(dateStr);
      d.setHours(0, 0, 0, 0);
      return d;
    };

    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const sortedAcademicYears = academicYears.sort(
      (a, b) => new Date(b.start_date).getTime() - new Date(a.start_date).getTime()
    );

    const isWithinGracePeriod = today.getMonth() === 6 || today.getMonth() === 7;

    const currentAcademicYear = academicYears.find((year) => {
      const start = normalize(year.start_date);
      const end = normalize(year.end_date);

      if (isWithinGracePeriod) {
        return today <= end;
      }

      return today >= start && today <= end;
    });

    if (currentAcademicYear) {
      return currentAcademicYear.academic_year;
    }

    if (sortedAcademicYears.length > 0) {
      return sortedAcademicYears[0].academic_year;
    }

    return '';
  };

  const fetchAcademicYear = async () => {
    try {
      setYearLoading(true);
      const years = await getAcademicYears();
      setAllAcademicYears(years);
      console.log('📅 Fetched academic years:', years);

      const current = getCurrentAcademicYear(years);
      if (current !== currentAcademicYear) {
        console.log('✅ New Academic Year:', current);
        setCurrentAcademicYear(current);
      }
    } catch (error) {
      console.error('❌ Error fetching academic years:', error);
      setCurrentAcademicYear('');
    } finally {
      setYearLoading(false);
    }
  };

  useEffect(() => {
    fetchAcademicYear(); // Always fetch on mount
  }, []);

  return {
    currentAcademicYear,
    yearLoading,
    refreshAcademicYear: fetchAcademicYear,
    allAcademicYears,
  };
};

export default useAcademicYear;
