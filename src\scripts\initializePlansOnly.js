/**
 * Script d'initialisation des plans de souscription uniquement
 * 
 * Ce script initialise seulement les plans de souscription mock :
 * - Basic (3000 FCFA/crédit)
 * - Standard (3000 FCFA/crédit + chatbot)
 * - Custom (sur mesure)
 * 
 * Usage: node src/scripts/initializePlansOnly.js
 */

// Simulation des données pour le frontend (pas de vraie connexion MongoDB)
const mockPlans = [
  {
    plan_name: 'basic',
    display_name: 'Plan Basic',
    description: 'Parfait pour débuter avec la gestion scolaire',
    price_per_credit: 3000,
    currency: 'FCFA',
    chatbot_enabled: false,
    features: [
      {
        name: 'student_management',
        display_name: 'Gestion des étudiants',
        description: 'Inscription, profils et suivi des étudiants',
        included: true
      },
      {
        name: 'class_management',
        display_name: 'Gestion des classes',
        description: 'Organisation des classes et groupes',
        included: true
      },
      {
        name: 'attendance_tracking',
        display_name: 'Suivi des présences',
        description: 'Pointage et rapports de présence',
        included: true
      },
      {
        name: 'grade_management',
        display_name: 'Gestion des notes',
        description: 'Saisie et calcul des notes',
        included: true
      },
      {
        name: 'timetable_management',
        display_name: 'Emplois du temps',
        description: 'Création et gestion des emplois du temps',
        included: true
      },
      {
        name: 'basic_reports',
        display_name: 'Rapports de base',
        description: 'Rapports essentiels sur les performances',
        included: true
      },
      {
        name: 'email_support',
        display_name: 'Support par email',
        description: 'Assistance par email en français',
        included: true
      }
    ],
    benefits: [
      'Gestion complète des étudiants',
      'Suivi des présences et notes',
      'Emplois du temps personnalisés',
      'Rapports de base',
      'Support par email'
    ],
    limitations: [
      'Pas de chatbot IA',
      'Rapports limités',
      'Support standard'
    ],
    recommended_for: 'Petites écoles (moins de 100 étudiants)',
    max_students: 100,
    is_active: true,
    sort_order: 1
  },
  {
    plan_name: 'standard',
    display_name: 'Plan Standard',
    description: 'Le choix idéal pour la plupart des écoles',
    price_per_credit: 3000,
    currency: 'FCFA',
    chatbot_enabled: true,
    chatbot_credits_per_message: 1,
    features: [
      {
        name: 'student_management',
        display_name: 'Gestion des étudiants',
        description: 'Inscription, profils et suivi des étudiants',
        included: true
      },
      {
        name: 'class_management',
        display_name: 'Gestion des classes',
        description: 'Organisation des classes et groupes',
        included: true
      },
      {
        name: 'attendance_tracking',
        display_name: 'Suivi des présences',
        description: 'Pointage et rapports de présence',
        included: true
      },
      {
        name: 'grade_management',
        display_name: 'Gestion des notes',
        description: 'Saisie et calcul des notes',
        included: true
      },
      {
        name: 'timetable_management',
        display_name: 'Emplois du temps',
        description: 'Création et gestion des emplois du temps',
        included: true
      },
      {
        name: 'chatbot_access',
        display_name: 'Chatbot IA',
        description: 'Assistant intelligent pour répondre aux questions',
        included: true
      },
      {
        name: 'advanced_reports',
        display_name: 'Rapports avancés',
        description: 'Analytics détaillées et tableaux de bord',
        included: true
      },
      {
        name: 'priority_support',
        display_name: 'Support prioritaire',
        description: 'Assistance prioritaire par email et chat',
        included: true
      },
      {
        name: 'early_access',
        display_name: 'Accès anticipé',
        description: 'Nouvelles fonctionnalités en avant-première',
        included: true
      }
    ],
    benefits: [
      'Toutes les fonctionnalités Basic',
      'Chatbot IA intelligent (1 crédit/message)',
      'Rapports avancés et analytics',
      'Support prioritaire',
      'Accès aux nouvelles fonctionnalités'
    ],
    limitations: [
      'Coût par message chatbot',
      'Support limité aux heures ouvrables'
    ],
    recommended_for: 'Écoles moyennes (100-500 étudiants)',
    max_students: 500,
    is_active: true,
    sort_order: 2,
    is_popular: true
  },
  {
    plan_name: 'custom',
    display_name: 'Plan Custom',
    description: 'Solution sur mesure pour les grandes institutions',
    price_per_credit: null, // Prix négocié
    currency: 'FCFA',
    chatbot_enabled: true,
    chatbot_credits_per_message: 1,
    features: [
      {
        name: 'everything_standard',
        display_name: 'Toutes les fonctionnalités Standard',
        description: 'Accès complet à toutes les fonctionnalités',
        included: true
      },
      {
        name: 'custom_development',
        display_name: 'Développement sur mesure',
        description: 'Fonctionnalités personnalisées selon vos besoins',
        included: true
      },
      {
        name: 'api_access',
        display_name: 'Accès API',
        description: 'Intégration avec vos systèmes existants',
        included: true
      },
      {
        name: 'dedicated_support',
        display_name: 'Support dédié 24/7',
        description: 'Équipe dédiée disponible 24h/24',
        included: true
      },
      {
        name: 'custom_integrations',
        display_name: 'Intégrations personnalisées',
        description: 'Connexion avec vos outils métier',
        included: true
      },
      {
        name: 'advanced_security',
        display_name: 'Sécurité avancée',
        description: 'Chiffrement et conformité renforcés',
        included: true
      },
      {
        name: 'training_sessions',
        display_name: 'Sessions de formation',
        description: 'Formation personnalisée de votre équipe',
        included: true
      }
    ],
    benefits: [
      'Toutes les fonctionnalités Standard',
      'Développement sur mesure',
      'Intégrations personnalisées',
      'Support dédié 24/7',
      'Formation personnalisée',
      'Sécurité renforcée'
    ],
    limitations: [],
    recommended_for: 'Grandes institutions (500+ étudiants)',
    max_students: null, // Illimité
    is_active: true,
    sort_order: 3,
    contact_required: true
  }
];

function initializePlansOnly() {
  console.log('🚀 Initialisation des plans de souscription mock...\n');
  
  // Simuler l'initialisation des plans
  console.log('📋 Plans de souscription initialisés:');
  mockPlans.forEach((plan, index) => {
    console.log(`   ${index + 1}. ${plan.display_name} (${plan.plan_name})`);
    console.log(`      - Prix: ${plan.price_per_credit ? plan.price_per_credit + ' FCFA/crédit' : 'Sur mesure'}`);
    console.log(`      - Chatbot: ${plan.chatbot_enabled ? 'Oui' : 'Non'}`);
    console.log(`      - Fonctionnalités: ${plan.features.length}`);
    if (plan.is_popular) {
      console.log(`      - ⭐ Plan populaire`);
    }
    console.log('');
    // store it in the database
    // Note: In a real application, you would save these plans to a database
    // For this mock, we just log them
    
  });

  console.log('✅ Plans mock initialisés avec succès !');
  console.log('💡 Ces données sont maintenant disponibles pour les tests frontend.');
  
  return mockPlans;
}

// Exporter les données pour utilisation dans le frontend
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { mockPlans, initializePlansOnly };
}

// Exécuter le script si appelé directement
if (require.main === module) {
  initializePlansOnly();
}
