import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";
import {
  CreditTransactionSchema,
  CreditTransactionCreateSchema,
  CreditTransactionUpdateSchema,
} from "../models/CreditTransactionModel";

// Get all credit transactions
export async function getCreditTransactions(): Promise<CreditTransactionSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit-transaction/get-credit-transactions`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credit transactions");
    }

    const data = await response.json();
    return data.map((tx: any) => ({
      _id: tx._id,
      school_id: tx.school_id,
      academicYear_id: tx.academicYear_id,
      payment_method: tx.payment_method,
      amountPaid: tx.amountPaid,
      credit: tx.credit,
      paidAt: tx.paidAt,
      createdAt: tx.createdAt,
      updatedAt: tx.updatedAt,
    })) as CreditTransactionSchema[];
  } catch (error) {
    console.error("Error fetching credit transactions:", error);
    throw new Error("Failed to fetch credit transactions");
  }
}

// Get credit transaction by ID
export async function getCreditTransactionById(id: string): Promise<CreditTransactionSchema> {
  const response = await fetch(`${BASE_API_URL}/credit-transaction/get-credit-transaction/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    throw new Error("Failed to fetch credit transaction");
  }

  return await response.json();
}

// Get credit transactions by School ID
export async function getCreditTransactionsBySchoolId(schoolId: string): Promise<CreditTransactionSchema[]> {
  try {
    const token = getTokenFromCookie("idToken");
    const response = await fetch(`${BASE_API_URL}/credit-transaction/get-credit-transactions-by-school/${schoolId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error("Failed to fetch credit transactions for the school");
    }

    const data = await response.json();
    return data.map((tx: any) => ({
      _id: tx._id,
      school_id: tx.school_id,
      academicYear_id: tx.academicYear_id,
      payment_method: tx.payment_method,
      amountPaid: tx.amountPaid,
      credit: tx.credit,
      paidAt: tx.paidAt,
      createdAt: tx.createdAt,
      updatedAt: tx.updatedAt,
    })) as CreditTransactionSchema[];
  } catch (error) {
    console.error("Error fetching credit transactions by school ID:", error);
    throw new Error("Failed to fetch credit transactions by school ID");
  }
}

// Create a new credit transaction
export async function createCreditTransaction(data: CreditTransactionCreateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit-transaction/create-credit-transaction`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to create credit transaction";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Update credit transaction
export async function updateCreditTransaction(id: string, data: CreditTransactionUpdateSchema) {
  const response = await fetch(`${BASE_API_URL}/credit-transaction/update-credit-transaction/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    let message = "Failed to update credit transaction";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Delete credit transaction
export async function deleteCreditTransaction(id: string) {
  const response = await fetch(`${BASE_API_URL}/credit-transaction/delete-credit-transaction/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
  });

  if (!response.ok) {
    let message = "Failed to delete credit transaction";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}

// Delete multiple credit transactions
export async function deleteMultipleCreditTransactions(ids: string[]) {
  const response = await fetch(`${BASE_API_URL}/credit-transaction/delete-credit-transactions`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
    },
    body: JSON.stringify({ ids }),
  });

  if (!response.ok) {
    let message = "Failed to delete credit transactions";
    try {
      const error = await response.json();
      message = error.message || message;
    } catch {}
    throw new Error(message);
  }

  return await response.json();
}
