"use client";

import { useState } from "react";
import { getAttendanceRecords, getAttendanceStats } from "@/app/services/AttendanceServices";
import { getTeacherPermissions, getTeacherSchedule } from "@/app/services/TeacherPermissionServices";
import { getStudentsBySchool } from "@/app/services/StudentServices";
import { getPeriodsBySchool } from "@/app/services/PeriodServices";

interface APITestResult {
  endpoint: string;
  status: 'success' | 'error' | 'loading';
  data?: any;
  error?: string;
  timestamp: string;
}

export default function AttendanceAPITester() {
  const [schoolId, setSchoolId] = useState("");
  const [results, setResults] = useState<APITestResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (endpoint: string, status: 'success' | 'error', data?: any, error?: string) => {
    const result: APITestResult = {
      endpoint,
      status,
      data,
      error,
      timestamp: new Date().toISOString()
    };
    setResults(prev => [result, ...prev]);
  };

  const testAPI = async (endpoint: string, apiCall: () => Promise<any>) => {
    try {
      addResult(endpoint, 'loading');
      const data = await apiCall();
      addResult(endpoint, 'success', data);
    } catch (error) {
      addResult(endpoint, 'error', undefined, error instanceof Error ? error.message : 'Unknown error');
    }
  };

  const testAllAPIs = async () => {
    if (!schoolId) {
      alert("Please enter a school ID");
      return;
    }

    setIsLoading(true);
    setResults([]);

    // Test all APIs
    await testAPI('getTeacherPermissions', () => getTeacherPermissions(schoolId));
    await testAPI('getTeacherSchedule', () => getTeacherSchedule(schoolId));
    await testAPI('getStudentsBySchool', () => getStudentsBySchool(schoolId));
    await testAPI('getPeriodsBySchool', () => getPeriodsBySchool(schoolId));
    await testAPI('getAttendanceRecords', () => getAttendanceRecords(schoolId));
    await testAPI('getAttendanceStats', () => getAttendanceStats(schoolId));

    setIsLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  const exportResults = () => {
    const dataStr = JSON.stringify(results, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `attendance-api-test-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
          Attendance API Tester
        </h2>
        
        <div className="mb-6">
          <div className="flex gap-4 items-end">
            <div className="flex-1">
              <label htmlFor="schoolId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                School ID
              </label>
              <input
                type="text"
                id="schoolId"
                value={schoolId}
                onChange={(e) => setSchoolId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                placeholder="Enter school ID to test APIs"
              />
            </div>
            <button
              onClick={testAllAPIs}
              disabled={isLoading || !schoolId}
              className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? 'Testing...' : 'Test All APIs'}
            </button>
            <button
              onClick={clearResults}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
            >
              Clear
            </button>
            {results.length > 0 && (
              <button
                onClick={exportResults}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Export
              </button>
            )}
          </div>
        </div>

        {results.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              Test Results ({results.length})
            </h3>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {results.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border ${
                    result.status === 'success'
                      ? 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800'
                      : result.status === 'error'
                      ? 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800'
                      : 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="font-medium text-gray-900 dark:text-white">
                      {result.endpoint}
                    </h4>
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-2 py-1 text-xs rounded-full ${
                          result.status === 'success'
                            ? 'bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100'
                            : result.status === 'error'
                            ? 'bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100'
                            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100'
                        }`}
                      >
                        {result.status}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(result.timestamp).toLocaleTimeString()}
                      </span>
                    </div>
                  </div>
                  
                  {result.error && (
                    <div className="mb-2">
                      <p className="text-sm text-red-600 dark:text-red-400">
                        <strong>Error:</strong> {result.error}
                      </p>
                    </div>
                  )}
                  
                  {result.data && (
                    <div>
                      <details className="cursor-pointer">
                        <summary className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Response Data {result.data && typeof result.data === 'object' && (
                            <span className="text-xs text-gray-500">
                              ({Array.isArray(result.data) ? `${result.data.length} items` : Object.keys(result.data).length + ' keys'})
                            </span>
                          )}
                        </summary>
                        <pre className="text-xs bg-gray-100 dark:bg-gray-700 p-3 rounded overflow-x-auto">
                          {JSON.stringify(result.data, null, 2)}
                        </pre>
                      </details>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {results.length === 0 && !isLoading && (
          <div className="text-center py-8 text-gray-500 dark:text-gray-400">
            Enter a school ID and click "Test All APIs" to start testing
          </div>
        )}
      </div>
    </div>
  );
}
