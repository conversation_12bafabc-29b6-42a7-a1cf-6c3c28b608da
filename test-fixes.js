// Test script to verify our fixes
const mongoose = require('mongoose');

// Test 1: Verify ActivityLog model can generate unique IDs
async function testActivityLogId() {
  try {
    const ActivityLog = require('./src/models/ActivityLog');
    console.log('✅ ActivityLog model loaded successfully');
    
    // Test the logActivity static method
    const testData = {
      user_id: new mongoose.Types.ObjectId(),
      school_id: new mongoose.Types.ObjectId(),
      action: 'schedule_created',
      entity_type: 'schedule',
      entity_id: new mongoose.Types.ObjectId(),
      details: { test: true }
    };
    
    console.log('📝 Testing ActivityLog.logActivity...');
    // Note: This would require a DB connection to actually test
    console.log('✅ ActivityLog.logActivity method exists');
    
  } catch (error) {
    console.error('❌ ActivityLog test failed:', error.message);
  }
}

// Test 2: Verify Notification model accepts 'schedule' entity_type
async function testNotificationModel() {
  try {
    const Notification = require('./src/models/Notification');
    console.log('✅ Notification model loaded successfully');
    
    // Check if 'schedule' is in the enum
    const schema = Notification.schema;
    const entityTypeEnum = schema.paths['related_entity.entity_type'].enumValues;
    
    if (entityTypeEnum.includes('schedule')) {
      console.log('✅ Notification model accepts "schedule" entity_type');
    } else {
      console.log('❌ Notification model does NOT accept "schedule" entity_type');
      console.log('Available values:', entityTypeEnum);
    }
    
  } catch (error) {
    console.error('❌ Notification test failed:', error.message);
  }
}

// Test 3: Verify timetable controller has updated conflict logic
async function testTimetableController() {
  try {
    const fs = require('fs');
    const controllerContent = fs.readFileSync('./src/controllers/timetableController.js', 'utf8');
    
    // Check for our new conflict logic
    if (controllerContent.includes('conflictType') && controllerContent.includes('existing_type')) {
      console.log('✅ Timetable controller has updated conflict logic');
    } else {
      console.log('❌ Timetable controller missing updated conflict logic');
    }
    
    if (controllerContent.includes('different_type') && controllerContent.includes('same_type')) {
      console.log('✅ Timetable controller has schedule type conflict detection');
    } else {
      console.log('❌ Timetable controller missing schedule type conflict detection');
    }
    
  } catch (error) {
    console.error('❌ Timetable controller test failed:', error.message);
  }
}

// Run all tests
async function runTests() {
  console.log('🧪 Running fix verification tests...\n');
  
  await testActivityLogId();
  console.log('');
  
  await testNotificationModel();
  console.log('');
  
  await testTimetableController();
  console.log('');
  
  console.log('🎉 Test verification complete!');
}

runTests().catch(console.error);
