import { SubscriptionSchema } from "../models/SubscriptionModel";
import { BASE_API_URL } from "./AuthContext";
import { getTokenFromCookie } from "./UserServices";


export async function getSubscriptions() {
    const response = await fetch(`${BASE_API_URL}/subscription/get-subscriptions`, {
        method: "GET",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error  fetching subscriptions:", response.statusText);
        throw new Error("Failed to fetch subscriptions data");
    }
    const data = await response.json();
    const subscriptions = data.map((subscription: any) =>{
        return {
            _id: subscription._id,
            subscription_id: subscription.subscription_id,
            transaction_id: subscription.transaction_id,
            guardian_id: subscription.guardian_id,
            student_id: subscription.student_id,
            amount: subscription.amount,
            email: subscription.email,
            status: subscription.status,
            expiryDate: subscription.expiryDate,
            createdAt: subscription.createdAt,
            updatedAt: subscription.updatedAt

        } as SubscriptionSchema
    })
    return subscriptions
}

export async function deleteMultipleSubscriptions(subscriptionIds: string[]) {
    const response = await fetch(`${BASE_API_URL}/subscription/delete-subscriptions`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
        body: JSON.stringify({ ids: subscriptionIds }),
    });
    if (!response.ok) {
        console.error("Error deleting multiple subscriptions:", response.statusText);
        throw new Error("Failed to delete multiple subscriptions");
    }
    const data = await response.json();
    return data;
}

export async function deleteAllSubscriptions() {
    const response = await fetch(`${BASE_API_URL}/subscription/delete-all-subscriptions`, {
        method: "DELETE",
        headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${getTokenFromCookie("idToken")}`,
        },
    });
    if (!response.ok) {
        console.error("Error deleting all subscriptions:", response.statusText);
        throw new Error("Failed to delete all subscriptions");
    }
    const data = await response.json();
    return data;
}
