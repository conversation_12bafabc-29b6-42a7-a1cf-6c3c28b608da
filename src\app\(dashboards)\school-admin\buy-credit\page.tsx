"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  CreditCard,
  TrendingUp,
  AlertTriangle,
  Plus,
  Eye,
  Calendar,
  Users,
  MessageCircle,
  BarChart3,
  ExternalLink,
  Zap
} from 'lucide-react';
import useAuth from '@/app/hooks/useAuth';
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import {
  getSchoolSubscription,
  getSubscriptionStats,
  formatCurrency,
  formatCredits
} from '@/app/services/SubscriptionServices';
import { SchoolSubscriptionSchema } from '@/app/models/SchoolSubscriptionModel';
import LoadingSpinner from '@/components/widgets/LoadingSpinner';
import CreditPurchaseModal from '@/components/school-admin/CreditPurchaseModal';
// import SubscriptionOverview from '@/components/school-admin/SubscriptionOverview';
// import UsageChart from '@/components/school-admin/UsageChart';
// import RecentTransactions from '@/components/school-admin/RecentTransactions';
import Link from 'next/link';

const BASE_URL = "/school-admin";

export default function BuyCreditPage() {
  const { user, logout } = useAuth();
  const [subscription, setSubscription] = useState<SchoolSubscriptionSchema | null>(null);
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showPurchaseModal, setShowPurchaseModal] = useState(false);

  // Navigation avec titre dynamique
  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/buy-credit`,
    title: "Gestion des Crédits"
  };

  useEffect(() => {
    if (user?.school_ids && user.school_ids.length > 0) {
      fetchSubscriptionData();
    }
  }, [user]);

  const fetchSubscriptionData = async () => {
    try {
      setLoading(true);
      setError(null);

      if (!user?.school_ids || user.school_ids.length === 0) {
        throw new Error('Aucune école associée à cet utilisateur');
      }

      const schoolId = user.school_ids[0];

      const [subscriptionResponse, statsResponse] = await Promise.all([
        getSchoolSubscription(schoolId),
        getSubscriptionStats(schoolId, 'month')
      ]);

      setSubscription(subscriptionResponse.subscription);
      setStats(statsResponse);
    } catch (err) {
      setError('Erreur lors du chargement des données');
      console.error('Error fetching subscription data:', err);
    } finally {
      setLoading(false);
    }
  };

  const handlePurchaseSuccess = () => {
    setShowPurchaseModal(false);
    fetchSubscriptionData(); // Refresh data after purchase
  };

  if (loading) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-96">
          <LoadingSpinner size="large" />
        </div>
      </SchoolLayout>
    );
  }

  if (error || !subscription) {
    return (
      <SchoolLayout
        navigation={navigation}
        showGoPro={true}
        onLogout={() => logout()}
      >
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">Erreur</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error || 'Données de souscription non trouvées'}</p>
            <button
              onClick={fetchSubscriptionData}
              className="px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg transition-colors"
            >
              Réessayer
            </button>
          </div>
        </div>
      </SchoolLayout>
    );
  }

  const isLowBalance = subscription.credits_balance <= subscription.low_credit_threshold;
  const efficiencyScore = subscription.credits_purchased > 0 
    ? Math.round((subscription.credits_used / subscription.credits_purchased) * 100) 
    : 0;

  return (
    <SchoolLayout
      navigation={navigation}
      showGoPro={true}
      onLogout={() => logout()}
    >
      <div className="p-6">
        <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                Gestion des Crédits
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Gérez votre souscription et achetez des crédits pour votre école
              </p>
            </div>
            
            <div className="flex gap-3">
              <Link
                href="/pricing"
                className="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 flex items-center transition-colors"
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                Voir les plans
              </Link>
              <button
                onClick={() => setShowPurchaseModal(true)}
                className="px-6 py-2 bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white rounded-lg flex items-center transition-colors"
              >
                <Plus className="h-4 w-4 mr-2" />
                Acheter des crédits
              </button>
            </div>
          </div>
        </div>

        {/* Alert for Low Balance */}
        {isLowBalance && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-6 p-4 bg-orange-50 border border-orange-200 rounded-lg"
          >
            <div className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-orange-500 mr-3" />
              <div>
                <h3 className="font-medium text-orange-800">Solde de crédits faible</h3>
                <p className="text-orange-700 text-sm">
                  Il vous reste seulement {formatCredits(subscription.credits_balance)}. 
                  Pensez à recharger pour continuer à utiliser toutes les fonctionnalités.
                </p>
              </div>
              <button
                onClick={() => setShowPurchaseModal(true)}
                className="ml-auto px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 text-sm"
              >
                Recharger maintenant
              </button>
            </div>
          </motion.div>
        )}

        {/* Main Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {/* Credits Balance */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6 border-l-4 border-blue-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Crédits disponibles</p>
                <p className="text-3xl font-bold text-blue-600">
                  {subscription.credits_balance.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-blue-100 rounded-full">
                <CreditCard className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm">
              <span className={`px-2 py-1 rounded-full text-xs ${
                isLowBalance 
                  ? 'bg-red-100 text-red-800' 
                  : 'bg-green-100 text-green-800'
              }`}>
                {isLowBalance ? 'Solde faible' : 'Solde normal'}
              </span>
            </div>
          </motion.div>

          {/* Credits Used */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2 }}
            className="bg-white rounded-xl shadow-sm p-6 border-l-4 border-green-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Crédits utilisés</p>
                <p className="text-3xl font-bold text-green-600">
                  {subscription.credits_used.toLocaleString()}
                </p>
              </div>
              <div className="p-3 bg-green-100 rounded-full">
                <TrendingUp className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-600">
              <span>Efficacité: {efficiencyScore}%</span>
            </div>
          </motion.div>

          {/* Plan Type */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-white rounded-xl shadow-sm p-6 border-l-4 border-purple-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Plan actuel</p>
                <p className="text-2xl font-bold text-purple-600 capitalize">
                  {subscription.plan_type}
                </p>
              </div>
              <div className="p-3 bg-purple-100 rounded-full">
                <Zap className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <div className="mt-4">
              <span className={`px-2 py-1 rounded-full text-xs ${
                subscription.status === 'active' 
                  ? 'bg-green-100 text-green-800' 
                  : 'bg-yellow-100 text-yellow-800'
              }`}>
                {subscription.status === 'active' ? 'Actif' : subscription.status}
              </span>
            </div>
          </motion.div>

          {/* Monthly Usage */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="bg-white rounded-xl shadow-sm p-6 border-l-4 border-orange-500"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Usage ce mois</p>
                <p className="text-3xl font-bold text-orange-600">
                  {stats?.usage_stats?.reduce((sum: number, stat: any) => sum + stat.total_credits, 0) || 0}
                </p>
              </div>
              <div className="p-3 bg-orange-100 rounded-full">
                <BarChart3 className="h-6 w-6 text-orange-600" />
              </div>
            </div>
            <div className="mt-4 flex items-center text-sm text-gray-600">
              <Calendar className="h-4 w-4 mr-1" />
              <span>Ce mois</span>
            </div>
          </motion.div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Subscription Overview & Usage Chart */}
          <div className="lg:col-span-2 space-y-8">
            {/* <SubscriptionOverview subscription={subscription} stats={stats} /> */}
            {/* <UsageChart stats={stats} /> */}

            {/* Temporary placeholder content */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Aperçu de la souscription</h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Plan actuel</p>
                  <p className="text-lg font-semibold capitalize">{subscription.plan_type}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Statut</p>
                  <p className="text-lg font-semibold capitalize">{subscription.status}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Crédits achetés</p>
                  <p className="text-lg font-semibold">{subscription.credits_purchased.toLocaleString()}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Fonctionnalités</p>
                  <p className="text-sm text-gray-700">{subscription.features.length} activées</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Graphique d'utilisation</h3>
              <div className="h-64 flex items-center justify-center bg-gray-50 rounded-lg">
                <p className="text-gray-500">Graphique d'utilisation des crédits (à implémenter)</p>
              </div>
            </div>
          </div>

          {/* Right Column - Recent Transactions & Quick Actions */}
          <div className="space-y-8">
            {/* <RecentTransactions
              schoolId={user!.school_id}
              onPurchaseClick={() => setShowPurchaseModal(true)}
            /> */}

            {/* Temporary placeholder for recent transactions */}
            <div className="bg-white rounded-xl shadow-sm p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Transactions récentes</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <p className="font-medium text-gray-900">Achat de crédits</p>
                    <p className="text-sm text-gray-600">Il y a 2 jours</p>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-green-600">+50 crédits</p>
                    <p className="text-sm text-gray-600">150,000 FCFA</p>
                  </div>
                </div>
                <div className="text-center py-4">
                  <p className="text-gray-500 text-sm">Historique complet disponible bientôt</p>
                </div>
              </div>
            </div>
            
            {/* Quick Actions */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Actions rapides</h3>
              <div className="space-y-3">
                <button
                  onClick={() => setShowPurchaseModal(true)}
                  className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                >
                  <Plus className="h-5 w-5 text-blue-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">Acheter des crédits</div>
                    <div className="text-sm text-gray-600">Recharger votre solde</div>
                  </div>
                </button>
                
                <Link
                  href="/school-admin/buy-credit/history"
                  className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                >
                  <Eye className="h-5 w-5 text-green-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">Historique détaillé</div>
                    <div className="text-sm text-gray-600">Voir toutes les transactions</div>
                  </div>
                </Link>
                
                <Link
                  href="/pricing"
                  className="w-full p-3 text-left border border-gray-200 rounded-lg hover:bg-gray-50 flex items-center"
                >
                  <ExternalLink className="h-5 w-5 text-purple-600 mr-3" />
                  <div>
                    <div className="font-medium text-gray-900">Changer de plan</div>
                    <div className="text-sm text-gray-600">Découvrir nos offres</div>
                  </div>
                </Link>
              </div>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Purchase Modal */}
      {showPurchaseModal && (
        <CreditPurchaseModal
          isOpen={showPurchaseModal}
          onClose={() => setShowPurchaseModal(false)}
          onSuccess={handlePurchaseSuccess}
          schoolId={user?.school_ids?.[0] || ''}
          currentPlan={subscription.plan_type}
        />
      )}
    </div>
    </SchoolLayout>
  );
}
