/**
 * Test script for teacher assignments debugging
 */

const mongoose = require('mongoose');
const ClassSchedule = require('../models/ClassSchedule');
const { getTeacherAssignmentsFromSchedule } = require('./teacherAssignmentSync');

// Test function to debug teacher assignments
async function testTeacherAssignments(teacherId, schoolId) {
  try {
    console.log('🔍 Testing teacher assignments...');
    console.log(`Teacher ID: ${teacherId}`);
    console.log(`School ID: ${schoolId}`);
    console.log('=' * 50);

    // Get raw schedule data
    console.log('\n📅 Raw Schedule Data:');
    const scheduleEntries = await ClassSchedule.find({
      school_id: schoolId,
      teacher_id: teacherId
    })
    .populate('class_id', 'name class_level')
    .populate('subject_id', 'name')
    .populate('period_id', 'period_number start_time end_time');

    console.log(`Found ${scheduleEntries.length} schedule entries:`);
    scheduleEntries.forEach((entry, index) => {
      console.log(`  ${index + 1}. Class: ${entry.class_id?.name || 'Unknown'}`);
      console.log(`     Subject: ${entry.subject_id?.name || 'Unknown'}`);
      console.log(`     Day: ${entry.day_of_week}`);
      console.log(`     Period: ${entry.period_id?.period_number || 'Unknown'} (${entry.period_id?.start_time || 'Unknown'}-${entry.period_id?.end_time || 'Unknown'})`);
      console.log(`     IDs: class=${entry.class_id?._id}, subject=${entry.subject_id?._id}, period=${entry.period_id?._id}`);
      console.log('');
    });

    // Test the utility function
    console.log('\n🔧 Testing utility function:');
    const assignments = await getTeacherAssignmentsFromSchedule(teacherId, schoolId);

    console.log('\n📊 Results:');
    console.log(`Assigned Classes (${assignments.assigned_classes?.length || 0}):`);
    assignments.assigned_classes?.forEach((cls, index) => {
      console.log(`  ${index + 1}. ${cls.name} (ID: ${cls._id})`);
    });

    console.log(`\nAssigned Subjects (${assignments.assigned_subjects?.length || 0}):`);
    assignments.assigned_subjects?.forEach((subject, index) => {
      console.log(`  ${index + 1}. ${subject.name} in ${subject.class_name} (Class ID: ${subject.class_id})`);
    });

    // Analyze potential issues
    console.log('\n🔍 Analysis:');
    
    // Check for missing subjects
    const uniqueSubjects = new Set();
    const uniqueClasses = new Set();
    
    scheduleEntries.forEach(entry => {
      if (entry.subject_id && entry.class_id) {
        uniqueSubjects.add(`${entry.subject_id._id}_${entry.class_id._id}`);
        uniqueClasses.add(entry.class_id._id.toString());
      }
    });

    console.log(`Expected unique subject-class combinations: ${uniqueSubjects.size}`);
    console.log(`Expected unique classes: ${uniqueClasses.size}`);
    console.log(`Actual assigned subjects: ${assignments.assigned_subjects?.length || 0}`);
    console.log(`Actual assigned classes: ${assignments.assigned_classes?.length || 0}`);

    if (uniqueSubjects.size !== (assignments.assigned_subjects?.length || 0)) {
      console.log('⚠️  Mismatch in subject assignments!');
    }

    if (uniqueClasses.size !== (assignments.assigned_classes?.length || 0)) {
      console.log('⚠️  Mismatch in class assignments!');
    }

    // Check for specific class subjects
    console.log('\n📚 Subject distribution by class:');
    const subjectsByClass = {};
    
    scheduleEntries.forEach(entry => {
      if (entry.class_id && entry.subject_id) {
        const className = entry.class_id.name;
        const classId = entry.class_id._id.toString();
        
        if (!subjectsByClass[className]) {
          subjectsByClass[className] = {
            classId,
            subjects: new Set()
          };
        }
        
        subjectsByClass[className].subjects.add(entry.subject_id.name);
      }
    });

    Object.entries(subjectsByClass).forEach(([className, data]) => {
      console.log(`  ${className} (${data.classId}):`);
      console.log(`    Subjects: ${Array.from(data.subjects).join(', ')}`);
      
      // Check if all subjects are in assigned_subjects
      const assignedForThisClass = assignments.assigned_subjects?.filter(s => s.class_id === data.classId) || [];
      console.log(`    Assigned subjects: ${assignedForThisClass.map(s => s.name).join(', ')}`);
      
      if (data.subjects.size !== assignedForThisClass.length) {
        console.log(`    ⚠️  Expected ${data.subjects.size} subjects, got ${assignedForThisClass.length}`);
      }
    });

    return assignments;

  } catch (error) {
    console.error('❌ Error testing teacher assignments:', error);
    throw error;
  }
}

// Export for use in other files
module.exports = {
  testTeacherAssignments
};

// If run directly
if (require.main === module) {
  // You can test with specific IDs here
  const TEACHER_ID = process.argv[2];
  const SCHOOL_ID = process.argv[3];
  
  if (!TEACHER_ID || !SCHOOL_ID) {
    console.log('Usage: node testTeacherAssignments.js <teacherId> <schoolId>');
    process.exit(1);
  }

  // Connect to MongoDB (adjust connection string as needed)
  mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify')
    .then(() => {
      console.log('Connected to MongoDB');
      return testTeacherAssignments(TEACHER_ID, SCHOOL_ID);
    })
    .then((result) => {
      console.log('\n✅ Test completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Test failed:', error);
      process.exit(1);
    });
}
