const mongoose = require("mongoose");

// Define the schema for the User model
const userSchema = new mongoose.Schema(
  {
    user_id: {
      type: String,
      required: true,
      unique: true,
    },
    firebaseUid: {
      type: String,
      unique: true,
      required: function () {
        return this.role !== "parent"; // Only required if the role is not "parent"
      },
    },
    name: {
      type: String,
      required: false,
    },
    role: {
      type: String,
      required: true,
      enum: ["admin", "teacher", "parent", "super", "school_admin", "bursar", "dean_of_studies"], // Extended roles
    },
    email: {
      type: String,
      unique: true,
      sparse: true, // Allows multiple documents to have `null` email but ensures uniqueness if provided
    },
    phone: {
      type: String,
      unique: true,
      sparse: true, // Allows multiple documents to have `null` phone but ensures uniqueness if provided
    },
    password: {
      type: String,
      required: function () {
        return this.role !== "parent"; // Only required if the role is not "parent"
      },
    },
    avatar: {
      type: String,
      required: false,
    },
    address: {
      type: String,
      required: false,
    },
    school_ids: [
      {
        type: mongoose.Schema.Types.ObjectId, // Reference to the School model
        ref: "School",
      },
    ],
    student_ids: [
      {
        type: mongoose.Schema.Types.ObjectId, // Reference to the School model
        ref: "Student",
      },
    ],
    isVerified: {
      type: Boolean,
      default: false,
    },
    verificationCode: {
      type: String,
      required: false,
    },
    verificationCodeExpires: {
      type: Date,
      required: false,
    },
    lastLogin: {
      type: Date,
      default: null, // Initially null, will be updated on user login
    },
    temp_password: {
      type: String,
      required: false,
    },
    temp_password_expires: {
      type: Date,
      required: false,
    },

    // Access codes for teachers to access multiple schools
    access_codes: [{
      school_id: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'School',
        required: true
      },
      access_code: {
        type: String,
        required: true
      },
      granted_at: {
        type: Date,
        default: Date.now
      },
      granted_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      is_active: {
        type: Boolean,
        default: true
      }
    }],

    // Additional staff information
    first_name: {
      type: String,
      required: false
    },

    last_name: {
      type: String,
      required: false
    },

    staff_id: {
      type: String,
      required: false,
      unique: true,
      sparse: true
    },

    // Password reset tokens for staff
    password_reset_token: {
      type: String,
      required: false
    },

    password_reset_expires: {
      type: Date,
      required: false
    },

    // Staff status
    is_staff_active: {
      type: Boolean,
      default: true
    },
  },
  {
    timestamps: true, // Automatically adds createdAt and updatedAt fields
  }
);

// Custom validation to ensure either email or phone is provided
userSchema.path("email").validate(function (value) {
  return this.phone || value; // If email is not provided, phone must be present
}, "Either email or phone is required.");

userSchema.path("phone").validate(function (value) {
  return this.email || value; // If phone is not provided, email must be present
}, "Either email or phone is required.");

// Validation: Only 'teacher' and 'parent' can have more than one school
userSchema.path("school_ids").validate(function (value) {
  if (!value) return true; // allow null/undefined
  if (value.length <= 1) return true; // 0 or 1 is fine for any role
  return this.role === "teacher" || this.role === "parent"; // only allow >1 for teacher or parent
}, "Only users with the role 'teacher' or 'parent' can be assigned to more than one school.");

// Use the model if it's already defined, or create a new one
const User = mongoose.models.User || mongoose.model("User", userSchema);

module.exports = User;
