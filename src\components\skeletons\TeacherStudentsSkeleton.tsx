import React from 'react';

// Skeleton for individual student card
export const StudentCardSkeleton = () => (
  <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
    <div className="flex items-center space-x-3">
      <div className="w-10 h-10 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
      <div className="flex-1">
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-3/4 mb-2"></div>
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-1"></div>
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-2/3"></div>
      </div>
    </div>
  </div>
);

// Skeleton for class section
export const ClassSectionSkeleton = () => (
  <div className="space-y-4 animate-pulse">
    {/* Class header skeleton */}
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
      <div>
        <div className="h-5 bg-gray-300 dark:bg-gray-600 rounded w-32 mb-1"></div>
        <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-20"></div>
      </div>
    </div>
    
    {/* Students grid skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(6)].map((_, index) => (
        <StudentCardSkeleton key={index} />
      ))}
    </div>
  </div>
);

// Skeleton for header stats
export const HeaderStatsSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 animate-pulse">
    {[...Array(3)].map((_, index) => (
      <div key={index} className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4">
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <div className="h-3 bg-gray-300 dark:bg-gray-600 rounded w-1/2 mb-2"></div>
            <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/4"></div>
          </div>
          <div className="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
        </div>
      </div>
    ))}
  </div>
);

// Skeleton for page header
export const PageHeaderSkeleton = () => (
  <div className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
    <div className="flex items-center space-x-3 mb-4">
      <div className="w-12 h-12 bg-gray-300 dark:bg-gray-600 rounded-full"></div>
      <div className="flex-1">
        <div className="h-6 bg-gray-300 dark:bg-gray-600 rounded w-1/3 mb-2"></div>
        <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
      </div>
    </div>
    
    <HeaderStatsSkeleton />
  </div>
);

// Main skeleton component for the entire students page
export const TeacherStudentsSkeleton = () => (
  <div className="space-y-6">
    {/* Header skeleton */}
    <PageHeaderSkeleton />
    
    {/* Content skeleton */}
    <div className="bg-widget rounded-lg border border-stroke p-6">
      <div className="space-y-8">
        {[...Array(3)].map((_, index) => (
          <ClassSectionSkeleton key={index} />
        ))}
      </div>
    </div>
  </div>
);

export default TeacherStudentsSkeleton;
