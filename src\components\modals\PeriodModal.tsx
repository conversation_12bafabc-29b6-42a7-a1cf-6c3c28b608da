"use client";

import React, { useState, useEffect } from "react";
import { X, Clock, Save } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { CreatePeriodData, UpdatePeriodData } from "@/app/services/PeriodServices";
import { PeriodSchema } from "@/app/models/PeriodModel";

interface PeriodModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: CreatePeriodData | UpdatePeriodData) => Promise<void>;
  period?: PeriodSchema | null;
  existingPeriods: PeriodSchema[];
  loading?: boolean;
}

export default function PeriodModal({
  isOpen,
  onClose,
  onSubmit,
  period,
  existingPeriods,
  loading = false
}: PeriodModalProps) {
  const [formData, setFormData] = useState({
    period_number: "",
    start_time: "",
    end_time: ""
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const isEditing = !!period;

  useEffect(() => {
    if (isOpen) {
      if (period) {
        setFormData({
          period_number: period.period_number.toString(),
          start_time: period.start_time.slice(0, 5), // Remove seconds
          end_time: period.end_time.slice(0, 5)
        });
      } else {
        // Auto-suggest next period number
        const maxPeriod = Math.max(...existingPeriods.map(p => p.period_number), 0);
        setFormData({
          period_number: (maxPeriod + 1).toString(),
          start_time: "",
          end_time: ""
        });
      }
      setErrors({});
    }
  }, [isOpen, period, existingPeriods]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // Validate period number
    const periodNum = parseInt(formData.period_number);
    if (!periodNum || periodNum < 1) {
      newErrors.period_number = "Period number must be a positive number";
    } else {
      // Check for duplicate period number (only when creating or changing number)
      const isDuplicate = existingPeriods.some(p => 
        p.period_number === periodNum && (!period || p._id !== period._id)
      );
      if (isDuplicate) {
        newErrors.period_number = "This period number already exists";
      }
    }

    // Validate times
    if (!formData.start_time) {
      newErrors.start_time = "Start time is required";
    }
    if (!formData.end_time) {
      newErrors.end_time = "End time is required";
    }

    // Validate time order
    if (formData.start_time && formData.end_time) {
      const startTime = new Date(`2000-01-01T${formData.start_time}:00`);
      const endTime = new Date(`2000-01-01T${formData.end_time}:00`);
      
      if (startTime >= endTime) {
        newErrors.end_time = "End time must be after start time";
      }

      // Check for time conflicts with other periods
      const hasConflict = existingPeriods.some(p => {
        if (period && p._id === period._id) return false; // Skip self when editing
        
        const pStart = new Date(`2000-01-01T${p.start_time}`);
        const pEnd = new Date(`2000-01-01T${p.end_time}`);
        
        return (
          (startTime >= pStart && startTime < pEnd) ||
          (endTime > pStart && endTime <= pEnd) ||
          (startTime <= pStart && endTime >= pEnd)
        );
      });

      if (hasConflict) {
        newErrors.start_time = "Time conflicts with existing period";
        newErrors.end_time = "Time conflicts with existing period";
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsSubmitting(true);
    try {
      const submitData = {
        period_number: parseInt(formData.period_number),
        start_time: `${formData.start_time}:00`,
        end_time: `${formData.end_time}:00`
      };

      await onSubmit(submitData);
      onClose();
    } catch (error) {
      console.error("Error submitting period:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Convert French time format (17h) to 24-hour format (17:00)
  const convertFrenchTime = (value: string) => {
    // Check if it's French format (e.g., "17h", "17h30", "9h", "9h15")
    const frenchTimeRegex = /^(\d{1,2})h(\d{0,2})$/;
    const match = value.match(frenchTimeRegex);

    if (match) {
      const hours = match[1].padStart(2, '0');
      const minutes = match[2] ? match[2].padStart(2, '0') : '00';
      return `${hours}:${minutes}`;
    }

    return value; // Return as-is if not French format
  };

  const handleInputChange = (field: string, value: string) => {
    // Convert French time format if applicable
    if ((field === 'start_time' || field === 'end_time') && value.includes('h')) {
      value = convertFrenchTime(value);
    }

    setFormData(prev => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />
          
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md mx-4"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center">
                  <Clock className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    {isEditing ? "Edit Period" : "Add New Period"}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isEditing ? "Update period details" : "Create a new class period"}
                  </p>
                </div>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Form */}
            <form onSubmit={handleSubmit} className="p-6 space-y-4">
              {/* Period Number */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Period Number
                </label>
                <input
                  type="number"
                  min="1"
                  value={formData.period_number}
                  onChange={(e) => handleInputChange("period_number", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white ${
                    errors.period_number 
                      ? "border-red-500 dark:border-red-500" 
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="Enter period number"
                />
                {errors.period_number && (
                  <p className="mt-1 text-sm text-red-500">{errors.period_number}</p>
                )}
              </div>

              {/* Start Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Start Time
                </label>
                <input
                  type="time"
                  value={formData.start_time}
                  onChange={(e) => handleInputChange("start_time", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white ${
                    errors.start_time
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="08:00 or 8h"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Supports French format: 8h, 8h30, 17h, etc.
                </p>
                {errors.start_time && (
                  <p className="mt-1 text-sm text-red-500">{errors.start_time}</p>
                )}
              </div>

              {/* End Time */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  End Time
                </label>
                <input
                  type="time"
                  value={formData.end_time}
                  onChange={(e) => handleInputChange("end_time", e.target.value)}
                  className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-700 dark:text-white ${
                    errors.end_time
                      ? "border-red-500 dark:border-red-500"
                      : "border-gray-300 dark:border-gray-600"
                  }`}
                  placeholder="09:00 or 9h"
                />
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  Supports French format: 9h, 9h30, 18h, etc.
                </p>
                {errors.end_time && (
                  <p className="mt-1 text-sm text-red-500">{errors.end_time}</p>
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-end space-x-3 pt-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700"
                  disabled={isSubmitting}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="px-4 py-2 bg-teal-500 text-white rounded-md hover:bg-teal-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span>{isSubmitting ? "Saving..." : (isEditing ? "Update" : "Create")}</span>
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
