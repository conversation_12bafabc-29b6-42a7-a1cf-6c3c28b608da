import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import useAuth from './useAuth';
import { isTokenExpired } from '../utils/httpInterceptor';
import Cookies from 'js-cookie';

/**
 * Hook pour surveiller l'authentification et rediriger automatiquement
 * vers la page de login si l'utilisateur n'est plus authentifié
 */
export const useAuthGuard = () => {
  const { user, isAuthenticated, loading, forceLogout, checkAuthStatus } = useAuth();
  const router = useRouter();

  // Fonction pour vérifier l'authentification
  const verifyAuth = useCallback(async () => {
    // Si on est encore en train de charger, attendre
    if (loading) return;

    // Vérifier si le token existe
    const token = Cookies.get("idToken");
    if (!token) {
      console.warn("No token found, redirecting to login");
      router.push('/login');
      return;
    }

    // Vérifier si le token est expiré
    if (isTokenExpired()) {
      console.warn("Token expired, redirecting to login");
      forceLogout();
      return;
    }

    // Si pas d'utilisateur mais token présent, vérifier avec le serveur
    if (!user && token && !isTokenExpired()) {
      try {
        await checkAuthStatus();
      } catch (error) {
        console.error("Auth verification failed:", error);
        router.push('/login');
      }
    }

    // Si pas authentifié après toutes les vérifications
    if (!isAuthenticated && !loading) {
      console.warn("User not authenticated, redirecting to login");
      router.push('/login');
    }
  }, [user, isAuthenticated, loading, router, forceLogout, checkAuthStatus]);

  // Vérifier l'authentification au montage et périodiquement
  useEffect(() => {
    verifyAuth();

    // Vérifier toutes les 5 minutes (réduit de 30 secondes)
    const interval = setInterval(verifyAuth, 300000);

    return () => clearInterval(interval);
  }, [verifyAuth]);

  // Écouter les changements de visibilité de la page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page redevient visible, vérifier l'auth
        verifyAuth();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [verifyAuth]);

  // Écouter les changements de focus de la fenêtre
  useEffect(() => {
    const handleFocus = () => {
      verifyAuth();
    };

    window.addEventListener('focus', handleFocus);
    return () => window.removeEventListener('focus', handleFocus);
  }, [verifyAuth]);

  return {
    isAuthenticated,
    loading,
    user,
    verifyAuth
  };
};

export default useAuthGuard;
