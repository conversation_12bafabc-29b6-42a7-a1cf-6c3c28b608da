const Settings = require('../models/Settings');

// Test response
const testSettingsResponse = (req, res) => {
  res.status(200).json({ message: 'Hi, this is settings' });
};

// Get settings (assuming there's only one settings document)
const getSettings = async (req, res) => {
  try {
    const settings = await Settings.findOne();
    if (!settings) {
      return res.status(404).json({ message: 'Settings not found' });
    }
    res.json(settings);
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

// Create default settings (useful on initial setup)
const createSettings = async (req, res) => {
  try {
    const existing = await Settings.findOne();
    if (existing) {
      return res.status(400).json({ message: 'Settings already exist' });
    }

    const newSettings = new Settings(req.body);
    await newSettings.save();

    res.status(201).json(newSettings);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// Update entire settings object
const updateSettings = async (req, res) => {
  try {
    const updated = await Settings.findOneAndUpdate({}, req.body, {
      new: true,
      runValidators: true,
    });

    if (!updated) {
      return res.status(404).json({ message: 'Settings not found' });
    }

    res.json(updated);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// Update a specific section of the settings (e.g., general, financials, etc.)
const updateSettingsSection = async (req, res) => {
  const { section } = req.params;
  const updateData = req.body;

  if (!['general', 'financials', 'notifications', 'security'].includes(section)) {
    return res.status(400).json({ message: 'Invalid settings section' });
  }

  try {
    const settings = await Settings.findOne();
    if (!settings) {
      return res.status(404).json({ message: 'Settings not found' });
    }

    settings[section] = {
      ...settings[section],
      ...updateData,
    };

    await settings.save();
    res.json(settings);
  } catch (err) {
    res.status(400).json({ message: err.message });
  }
};

// Delete all settings (use with caution, mostly for admin tools or resets)
const deleteSettings = async (req, res) => {
  try {
    const result = await Settings.deleteMany({});
    res.json({ message: `${result.deletedCount} settings record(s) deleted` });
  } catch (err) {
    res.status(500).json({ message: err.message });
  }
};

module.exports = {
  testSettingsResponse,
  getSettings,
  createSettings,
  updateSettings,
  updateSettingsSection,
  deleteSettings,
};
