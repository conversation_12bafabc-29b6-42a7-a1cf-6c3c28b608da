const express = require('express');
const router = express.Router();
const activityLogController = require('../controllers/activityLogController');
const { authenticate, authorize } = require('../middleware/middleware');


// Get recent activities
router.get('/recent', authenticate, activityLogController.getRecentActivities);

// Get activities for a specific user
router.get('/user/:user_id', authenticate, authorize(['admin', 'super', 'school_admin', 'teacher']), activityLogController.getUserActivities);

// Get activities for a specific school
router.get('/school/:school_id', authenticate, authorize(['admin', 'super', 'school_admin']), activityLogController.getSchoolActivities);

module.exports = router;
