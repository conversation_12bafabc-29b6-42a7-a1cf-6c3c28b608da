"use client";

import React, { useEffect, useState } from "react";
import {
    getParents,
    resetParentPasswordService,
    updateUser,
} from "@/app/services/UserServices";
import { getSchools } from "@/app/services/SchoolServices";
import { getStudents } from "@/app/services/StudentServices";
import { UserSchema, UserUpdateSchema } from "@/app/models/UserModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { StudentSchema } from "@/app/models/StudentModel";
import NotificationCard from "@/components/NotificationCard";
import UpdateInvitationModal from "@/app/(dashboards)/super-admin/parents/components/UpdateInviteModal";
import ActionButton from "@/components/ActionButton";
import BackButton from "@/components/BackButton";

interface ParentDetailsProps {
    userId: string;
}

export default function ParentDetailsView({ userId }: ParentDetailsProps) {
    const [parent, setParent] = useState<UserSchema | null>(null);
    const [schools, setSchools] = useState<SchoolSchema[]>([]);
    const [students, setStudents] = useState<StudentSchema[]>([]);
    const [loading, setLoading] = useState(true);
    const [loadingData, setLoadingData] = useState(false);
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<
        "success" | "error" | "info" | "warning"
    >("success");
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);

    const fetchData = async () => {
        setLoading(true);
        try {
            const [parents, schoolData, studentData] = await Promise.all([
                getParents(),
                getSchools(),
                getStudents(),
            ]);

            const found = parents.find((user: UserSchema) => user._id === userId);
            if (found) setParent(found);
            setSchools(schoolData);
            setStudents(studentData);
        } catch (error) {
            console.error("Error loading Parent data", error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (userId) fetchData();
    }, [userId]);

    const getSchoolNames = (ids?: string[]) =>
        ids?.map((id) => schools.find((s) => s._id === id)?.name || "Unknown").join(", ") ||
        "No Schools";

    const getStudentNames = (ids?: string[]) =>
        ids?.map((id) => students.find((s) => s._id === id)?.name || "Unknown").join(", ") ||
        "No Children";

    const handlePasswordReset = async () => {
        const contact = parent?.email
            ? { email: parent.email }
            : parent?.phone
                ? { phone: parent.phone }
                : null;
        if (!contact) return;

        setLoadingData(true);
        try {
            await resetParentPasswordService(contact);
            setIsNotificationCard(true);
            setNotificationMessage("Password reset link has been sent.");
            setNotificationType("success");
        } catch (error) {
            const message =
                error instanceof Error ? error.message : "Failed to send reset link.";
            setIsNotificationCard(true);
            setNotificationMessage(message);
            setNotificationType("error");
        } finally {
            setLoadingData(false);
        }
    };

    const handleUpdate = async (invitationData: UserSchema) => {
        if (parent && userId) {
            setIsSubmitting(true);
            setSubmitStatus(null);
            setLoadingData(true);
            try {
                const updatedInvitation: UserUpdateSchema = {
                    _id: invitationData._id,
                    user_id: invitationData.user_id,
                    student_ids: invitationData.childrenIds,
                    school_ids: invitationData.school_ids,
                    name: invitationData.name,
                    createdAt: invitationData.createdAt,
                    role: "parent",
                };
                await updateUser(invitationData.user_id, updatedInvitation);
                setSubmitStatus("success");
                fetchData();
                setIsNotificationCard(true);
                setNotificationMessage("Parent updated successfully.");
                setNotificationType("success");
            } catch (error) {
                const errorMessage =
                    error instanceof Error ? error.message : "Error updating Parent.";
                setSubmitStatus("failure");
                setNotificationMessage(errorMessage);
                setIsNotificationCard(true);
                setNotificationType("error");
            } finally {
                setIsSubmitting(false);
                setLoadingData(false);
            }
        }
    };

    if (loading) {
        return (
            <div className="md:p-6 animate-pulse">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                    <div className="h-6 w-1/3 bg-gray-300 dark:bg-gray-600 rounded mb-6" />
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                        {Array.from({ length: 6 }).map((_, i) => (
                            <div key={i} className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md space-y-2">
                                <div className="h-4 w-1/2 bg-gray-300 dark:bg-gray-600 rounded" />
                                <div className="h-5 w-full bg-gray-300 dark:bg-gray-500 rounded" />
                            </div>
                        ))}
                    </div>
                    <div className="flex justify-end space-x-2">
                        <div className="h-10 w-24 bg-gray-300 dark:bg-gray-600 rounded" />
                        <div className="h-10 w-32 bg-gray-300 dark:bg-gray-600 rounded" />
                        <div className="h-10 w-36 bg-gray-300 dark:bg-gray-600 rounded" />
                    </div>
                </div>
            </div>
        );
    }

    if (!parent) {
        return <div className="text-center text-red-600 font-semibold">Parent not found.</div>;
    }

    return (
        <div className="md:p-6">
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                            <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
                        </svg>
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            {isUpdateModalOpen && (
                <UpdateInvitationModal
                    onClose={() => {
                        setIsUpdateModalOpen(false);
                        setSubmitStatus(null);
                    }}
                    initialData={parent}
                    onSave={handleUpdate}
                    isSubmitting={isSubmitting}
                    submitStatus={submitStatus}
                />
            )}

            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h1 className="text-2xl font-bold text-foreground mb-4">Parent Details</h1>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
                    <InfoItem label="Name" value={parent.name || "N/A"} />
                    <InfoItem label="Email" value={parent.email} />
                    <InfoItem label="Phone" value={parent.phone || "N/A"} />
                    <InfoItem label="Schools" value={getSchoolNames(parent.school_ids)} />
                    <InfoItem label="Children" value={getStudentNames(parent.student_ids)} />
                    <InfoItem
                        label="Invited At"
                        value={parent.createdAt ? new Date(parent.createdAt).toLocaleString() : "N/A"}
                    />
                </div>

                <div className="flex justify-end space-x-2">
                    <BackButton />
                    <ActionButton action="edit" label="Edit Parent" onClick={() => setIsUpdateModalOpen(true)} />
                    <ActionButton
                        action="resetPassword"
                        label={loadingData ? "Resetting..." : "Reset Password"}
                        onClick={handlePasswordReset}
                        disabled={loadingData}
                        isLoading={loadingData}
                    />
                </div>
            </div>
        </div>
    );
}

const InfoItem = ({ label, value }: { label: string; value: string }) => (
    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">{label}</p>
        <p className="text-sm text-foreground">{value}</p>
    </div>
);
