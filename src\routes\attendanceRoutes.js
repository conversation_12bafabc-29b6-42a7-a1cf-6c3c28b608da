const express = require('express');
const attendanceController = require('../controllers/attendanceController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// CRUD routes for attendance

router.post('/create-attendance', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.createAttendance);
router.put('/update-attendance/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.updateAttendance);
router.delete('/delete-attendance/:id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.deleteAttendance);
router.delete('/delete-multiple-attendances', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.deleteMultipleAttendances);

// Get routes for school-specific attendance
router.get('/school/:school_id', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.getAttendanceRecords);
router.get('/school/:school_id/stats', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.getAttendanceStats);

// Export routes
router.get('/school/:school_id/export/pdf', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.exportAttendancePDF);
router.get('/school/:school_id/export/excel', authenticate, checkSubscription, authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']), attendanceController.exportAttendanceExcel);

//Jordan's addition for getting attendance by schedule IDs and school
router.post(
  '/get-by-schedules',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']),
  attendanceController.getAttendanceByScheduleIdsAndSchool
);

// --- NEW ROUTE FOR CREATE/UPDATE BATCH ATTENDANCE ---
router.post(
  '/create-or-update-attendance', // This is the recommended endpoint for batch create/update
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'teacher', 'dean_of_studies']),
  attendanceController.createOrUpdateAttendance // Your new controller function
);

module.exports = router;
