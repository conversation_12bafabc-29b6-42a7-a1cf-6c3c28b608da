"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { Megaphone, Plus, Calendar, Users, AlertCircle, CheckCircle, Clock, Eye } from "lucide-react";
import { motion } from "framer-motion";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import CircularLoader from "@/components/widgets/CircularLoader";
import { useToast, ToastContainer } from "@/components/ui/Toast";
import { getAnnouncementsBySchool, AnnouncementSchema } from "@/app/services/AnnouncementServices";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

// Using AnnouncementSchema from service

const navigation = {
  icon: Megaphone,
  baseHref: "/teacher-dashboard/announcements",
  title: "Announcements"
};

export default function TeacherAnnouncementsPage() {
  const { logout, user } = useAuth();
  const router = useRouter();
  const { toasts, removeToast, showSuccess, showError } = useToast();

  // State management
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [loadingSchool, setLoadingSchool] = useState(true);
  const [loadingData, setLoadingData] = useState(false);
  const [announcements, setAnnouncements] = useState<AnnouncementSchema[]>([]);

  // Filter states
  const [selectedPriority, setSelectedPriority] = useState('all');
  const [selectedAudience, setSelectedAudience] = useState('all');

  // Get school ID from user context
  const schoolId = selectedSchool?.school_id;

  // Load selected school from localStorage
  useEffect(() => {
    const savedSchool = localStorage.getItem('teacher_selected_school');
    if (savedSchool) {
      const parsedSchool = JSON.parse(savedSchool);
      console.log('Loaded school from localStorage:', parsedSchool);
      setSelectedSchool(parsedSchool);
    }
    setLoadingSchool(false);
  }, []);

  // Fetch announcements from API
  useEffect(() => {
    const fetchAnnouncements = async () => {
      if (!schoolId) return;

      setLoadingData(true);
      try {
        const data = await getAnnouncementsBySchool(schoolId);
        // Filter to show only published announcements for teachers
        const publishedAnnouncements = data.filter(announcement =>
          announcement.is_published &&
          (announcement.target_audience === 'teachers' || announcement.target_audience === 'all')
        );
        setAnnouncements(publishedAnnouncements);
      } catch (error) {
        console.error("Error fetching announcements:", error);
        showError("Error", "Failed to load announcements");
        setAnnouncements([]);
      } finally {
        setLoadingData(false);
      }
    };

    fetchAnnouncements();
  }, [schoolId]); // Removed showError from dependencies

  // Handle school change
  const handleSchoolChange = () => {
    router.push('/teacher-dashboard');
  };

  // Get priority color and icon
  const getPriorityDisplay = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return {
          color: 'bg-red-200 text-red-900 border-red-300',
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'Urgent'
        };
      case 'high':
        return {
          color: 'bg-red-100 text-red-800 border-red-200',
          icon: <AlertCircle className="h-4 w-4" />,
          label: 'High Priority'
        };
      case 'medium':
        return {
          color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
          icon: <Clock className="h-4 w-4" />,
          label: 'Medium Priority'
        };
      case 'low':
        return {
          color: 'bg-green-100 text-green-800 border-green-200',
          icon: <CheckCircle className="h-4 w-4" />,
          label: 'Low Priority'
        };
      default:
        return {
          color: 'bg-gray-100 text-gray-800 border-gray-200',
          icon: <Clock className="h-4 w-4" />,
          label: 'Normal'
        };
    }
  };

  // Format date
  const formatDate = (dateInput: string | Date) => {
    const date = new Date(dateInput);
    if (isNaN(date.getTime())) {
      return 'Invalid Date';
    }
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Filter announcements
  const filteredAnnouncements = announcements.filter(announcement => {
    const matchesPriority = selectedPriority === 'all' || announcement.priority === selectedPriority;
    const matchesAudience = selectedAudience === 'all' || 
                           announcement.target_audience === selectedAudience ||
                           announcement.target_audience === 'all';
    
    return matchesPriority && matchesAudience;
  });

  if (loadingSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <CircularLoader />
      </div>
    );
  }

  if (!selectedSchool) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">No School Selected</h2>
          <button
            onClick={() => router.push('/teacher-dashboard')}
            className="px-4 py-2 bg-teal text-white rounded-md hover:bg-teal-600"
          >
            Select School
          </button>
        </div>
      </div>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={{
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        }}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                  <Megaphone className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-foreground">School Announcements</h1>
                  <p className="text-foreground/60">
                    Stay updated with important announcements from {selectedSchool.school_name}
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Filters */}
          <div className="bg-widget rounded-lg border border-stroke p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">Priority:</label>
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md bg-widget"
                >
                  <option value="all">All Priorities</option>
                  <option value="urgent">Urgent</option>
                  <option value="high">High Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="low">Low Priority</option>
                </select>
              </div>
              
              <div className="flex items-center space-x-2">
                <label className="text-sm font-medium text-foreground">Audience:</label>
                <select
                  value={selectedAudience}
                  onChange={(e) => setSelectedAudience(e.target.value)}
                  className="px-3 py-2 border border-stroke rounded-md bg-widget"
                >
                  <option value="all">All Announcements</option>
                  <option value="teachers">For Teachers</option>
                  <option value="students">For Students</option>
                  <option value="parents">For Parents</option>
                </select>
              </div>
            </div>
          </div>

          {/* Announcements List */}
          <div className="space-y-4">
            {loadingData ? (
              Array.from({ length: 4 }).map((_, index) => (
                <div key={index} className="bg-widget rounded-lg border border-stroke p-6 animate-pulse">
                  <div className="flex justify-between items-start mb-4">
                    <div className="flex-1">
                      <div className="h-6 bg-gray-200 rounded mb-2 w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                    <div className="h-6 bg-gray-200 rounded w-20"></div>
                  </div>
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                  </div>
                </div>
              ))
            ) : filteredAnnouncements.length === 0 ? (
              <div className="bg-widget rounded-lg border border-stroke p-12 text-center">
                <Megaphone className="h-16 w-16 text-foreground/30 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-foreground mb-2">No Announcements Found</h3>
                <p className="text-foreground/60">
                  {selectedPriority !== 'all' || selectedAudience !== 'all'
                    ? "Try adjusting your filters to see more announcements."
                    : "There are no announcements available at the moment."}
                </p>
              </div>
            ) : (
              filteredAnnouncements.map((announcement) => {
                const priorityDisplay = getPriorityDisplay(announcement.priority);
                
                return (
                  <motion.div
                    key={announcement._id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="bg-widget rounded-lg border border-stroke p-6 hover:shadow-lg transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-foreground mb-2">
                          {announcement.title}
                        </h3>
                        <div className="flex items-center space-x-4 text-sm text-foreground/60">
                          <div className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>By {
                              typeof announcement.author_id === 'string'
                                ? announcement.author_id
                                : announcement.author_id?.email || announcement.author_id?.name || 'Unknown Author'
                            }</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>{formatDate(announcement.created_at)}</span>
                          </div>
                          <div className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Published</span>
                          </div>
                        </div>
                      </div>
                      
                      <div className={`flex items-center space-x-2 px-3 py-1 rounded-full border ${priorityDisplay.color}`}>
                        {priorityDisplay.icon}
                        <span className="text-sm font-medium">{priorityDisplay.label}</span>
                      </div>
                    </div>
                    
                    <div className="mb-4">
                      <p className="text-foreground/80 leading-relaxed">
                        {announcement.content}
                      </p>
                    </div>
                    
                    <div className="flex justify-between items-center pt-4 border-t border-stroke">
                      <div className="flex items-center space-x-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          announcement.target_audience === 'teachers' ? 'bg-blue-100 text-blue-800' :
                          announcement.target_audience === 'students' ? 'bg-green-100 text-green-800' :
                          announcement.target_audience === 'parents' ? 'bg-purple-100 text-purple-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {announcement.target_audience === 'all' ? 'Everyone' : 
                           announcement.target_audience.charAt(0).toUpperCase() + announcement.target_audience.slice(1)}
                        </span>
                        
                        {announcement.is_published && (
                          <span className="flex items-center space-x-1 text-green-600 text-sm">
                            <CheckCircle className="h-4 w-4" />
                            <span>Published</span>
                          </span>
                        )}
                      </div>
                      
                      <button className="text-teal hover:text-teal-600 text-sm font-medium">
                        Read More
                      </button>
                    </div>
                  </motion.div>
                );
              })
            )}
          </div>
        </div>

        {/* Toast Notifications */}
        <ToastContainer toasts={toasts} onClose={removeToast} />
      </TeacherLayout>
    </ProtectedRoute>
  );
}
