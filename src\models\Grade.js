const mongoose = require('mongoose');

// Define the schema for the Grade model
const gradeSchema = new mongoose.Schema({
  school_id :{
      type: mongoose.Schema.Types.ObjectId, // Reference to the Class model
      ref: 'School',
    },
  subject_id: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the Subject model
    ref: 'Subject',
    required: true, // Ensures that subject field is required
  },
  student_id: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the Subject model
    ref: 'Student',
    required: true, // Ensures that subject field is required
  },
  exam_type: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the ExamType model
    ref: 'ExamType',
    required: false, // Made optional since we have sequences now
  },
  term_id: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the Term model
    ref: 'Term',
    required: true, // Ensures that term_id field is required
  },
  sequence_number: {
    type: Number,
    required: true, // Which sequence within the term (1, 2, etc.)
    min: 1,
    max: 6
  },
  // Keep legacy fields for backward compatibility (will be deprecated)
  term: {
    type: String,
    required: false // Made optional for migration, no enum restriction
  },
  academic_year: {
    type: String,
    required: false, // Made optional since it's now derived from term_id
  },
  grade:{
    type:String
  },
  score: {
    type: Number, // Using Number type for scores (can handle decimals)
    required: true, // Ensures that score field is required
  },
  comments: {
    type: String,
  }
}, {
  timestamps: true // Automatically adds createdAt and updatedAt fields
});

// Add indexes for better query performance
gradeSchema.index({ school_id: 1, term_id: 1 });
gradeSchema.index({ school_id: 1, student_id: 1, term_id: 1 });
gradeSchema.index({ school_id: 1, subject_id: 1, term_id: 1 });
gradeSchema.index({ term_id: 1, sequence_number: 1 });

// Ensure unique grade per student, subject, term, and sequence (exam_type is now optional)
gradeSchema.index({
  student_id: 1,
  subject_id: 1,
  term_id: 1,
  sequence_number: 1,
  exam_type: 1
}, {
  unique: true,
  partialFilterExpression: { exam_type: { $exists: true } } // Only enforce uniqueness when exam_type exists
});

// Use the model if it's already defined, or create a new one
const Grade = mongoose.models.Grade || mongoose.model('Grade', gradeSchema);

module.exports = Grade;
