const express = require('express');
const termController = require('../controllers/termController');
const { authenticate, authorize, checkSubscription } = require('../middleware/middleware');

const router = express.Router();

// Test route
router.get('/test', termController.testTermResponse);

// GET all terms for a school
router.get(
  '/school/:school_id',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'teacher']),
  termController.getTermsBySchool
);

// GET current term for a school
router.get(
  '/school/:school_id/current',
  authenticate,
  checkSubscription,
  authorize(['admin', 'super', 'school_admin', 'teacher']),
  termController.getCurrentTerm
);

// CREATE a new term
router.post(
  '/school/:school_id',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  termController.createTerm
);

// UPDATE term by ID
router.put(
  '/school/:school_id/:term_id',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  termController.updateTerm
);

// DELETE term by ID (soft delete)
router.delete(
  '/school/:school_id/:term_id',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  termController.deleteTerm
);

// SET current term
router.patch(
  '/school/:school_id/:term_id/set-current',
  authenticate,
  authorize(['admin', 'super', 'school_admin']),
  termController.setCurrentTerm
);

module.exports = router;
