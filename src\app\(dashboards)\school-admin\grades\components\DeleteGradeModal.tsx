// components/modals/DeleteGradeModal.tsx
"use client";

import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import CircularLoader from "@/components/widgets/CircularLoader";

interface DeleteGradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (password: string) => void;
  loading: boolean;
  deleteType: "single" | "multiple";
  itemToDelete?: string; // For single delete: grade name/score
  numberOfItems?: number; // For multiple delete
}

const DeleteGradeModal: React.FC<DeleteGradeModalProps> = ({
  isOpen,
  onClose,
  onConfirm,
  loading,
  deleteType,
  itemToDelete,
  numberOfItems,
}) => {
  const [password, setPassword] = useState("");
  const [passwordError, setPasswordError] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!password) {
      setPasswordError("Password is required to confirm deletion.");
      return;
    }
    setPasswordError("");
    onConfirm(password);
  };

  const handleCloseModal = () => {
    setPassword("");
    setPasswordError("");
    onClose();
  };

  const isMultiple = deleteType === "multiple";
  const title = isMultiple ? `Delete ${numberOfItems} Grades` : "Delete Grade";
  const message = isMultiple
    ? `Are you sure you want to delete these ${numberOfItems} grades? This action cannot be undone.`
    : `Are you sure you want to delete the grade "${itemToDelete}"? This action cannot be undone.`;

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-center items-center p-4"
          onClick={handleCloseModal}
        >
          <motion.div
            initial={{ y: -50, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -50, opacity: 0 }}
            className="bg-widget p-6 rounded-lg shadow-xl w-full max-w-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-2xl font-bold text-foreground mb-4">{title}</h2>
            <p className="text-foreground/80 mb-6">{message}</p>

            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-foreground mb-2"
                >
                  Confirm with your password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-background text-foreground focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-transparent"
                  placeholder="Enter your password"
                  disabled={loading}
                />
                {passwordError && (
                  <p className="text-red-500 text-sm mt-1">{passwordError}</p>
                )}
              </div>

              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={handleCloseModal}
                  className="px-4 py-2 rounded-lg text-foreground border border-gray-300 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="bg-red-600 text-white px-4 py-2 rounded-lg flex items-center justify-center gap-2 hover:bg-red-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  disabled={loading}
                >
                  {loading ? (
                    <CircularLoader size={20} color="white" />
                  ) : (
                    "Delete"
                  )}
                </button>
              </div>
            </form>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default DeleteGradeModal;