"use client";

import React, { Suspense, useEffect, useState } from "react";
import { Percent, Search, Users } from "lucide-react";
import TeacherLayout from "@/components/Dashboard/Layouts/TeacherLayout";
import CircularLoader from "@/components/widgets/CircularLoader";
import useAuth from "@/app/hooks/useAuth";
import ProtectedRoute from "@/components/utils/ProtectedRoute";
import { motion } from "framer-motion";
import { ClassGradesSkeleton } from "@/components/skeletons";
import { getTeacherPermissions, getTeacherNavigationItems } from "@/app/services/TeacherPermissionServices";
import { getStudentsByClassAndSchool } from "@/app/services/StudentServices";
import { getCurrentTerm } from "@/app/services/TermServices";
import ClassGradeCard from "@/components/grades/ClassGradeCard";
import { useRouter } from "next/navigation";

interface SelectedSchool {
  school_id: string;
  school_name: string;
  access_granted_at: string;
}

interface ClassWithStudentCount {
  _id: string;
  name: string;
  class_code: string;
  level: string;
  studentCount: number;
  class_id: string;
}

export default function TeacherGradesPage() {
  const { user, logout } = useAuth();
  const router = useRouter();
  const [selectedSchool, setSelectedSchool] = useState<SelectedSchool | null>(null);
  const [navigation, setNavigation] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [classes, setClasses] = useState<ClassWithStudentCount[]>([]);
  const [currentTerm, setCurrentTerm] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    if (user) {
      // Get selected school from localStorage
      const storedSchool = localStorage.getItem("teacher_selected_school");
      if (storedSchool) {
        const school = JSON.parse(storedSchool);
        setSelectedSchool(school);
      } else {
        router.push("/teacher-dashboard");
      }
    } else {
      router.push("/teacher-dashboard");
    }
  }, [user, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!selectedSchool || !user) return;

      try {
        setLoading(true);

        // Get navigation items
        const navItems = await getTeacherNavigationItems(selectedSchool.school_id);
        setNavigation(navItems);

        // Get teacher's assignments
        const teacherData = await getTeacherPermissions(selectedSchool.school_id);
        
        // Get current term
        try {
          const termData = await getCurrentTerm(selectedSchool.school_id);
          setCurrentTerm(termData.term); // Extract the term object
        } catch (error) {
          console.warn("Could not fetch current term:", error);
          setCurrentTerm(null);
        }

        // Process teacher's assigned classes and get student counts
        const classesWithCounts: ClassWithStudentCount[] = [];
        
        for (const assignedClass of teacherData.assigned_classes) {
          try {
            // Get students for this class
            const studentsData = await getStudentsByClassAndSchool(assignedClass._id, selectedSchool.school_id);
            
            classesWithCounts.push({
              _id: assignedClass._id,
              name: assignedClass.name,
              class_code: assignedClass.class_code || assignedClass.name,
              level: assignedClass.level || "",
              studentCount: studentsData.length,
              class_id: assignedClass._id
            });
          } catch (error) {
            console.error(`Error fetching students for class ${assignedClass.name}:`, error);
            // Add class with 0 students if there's an error
            classesWithCounts.push({
              _id: assignedClass._id,
              name: assignedClass.name,
              class_code: assignedClass.class_code || assignedClass.name,
              level: assignedClass.level || "",
              studentCount: 0,
              class_id: assignedClass._id
            });
          }
        }

        setClasses(classesWithCounts);
        
      } catch (error) {
        console.error("Error fetching teacher data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [selectedSchool, user]);

  const handleSchoolChange = () => {
    localStorage.removeItem("teacher_selected_school");
    router.push("/teacher-dashboard");
  };

  const handleClassClick = (classId: string) => {
    router.push(`/teacher-dashboard/grades/class/${classId}`);
  };

  // Filter classes based on search term
  const filteredClasses = classes.filter(cls =>
    cls.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cls.class_code.toLowerCase().includes(searchTerm.toLowerCase()) ||
    cls.level.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (loading) {
    return (
      <ProtectedRoute allowedRoles={["teacher"]}>
        <TeacherLayout
          navigation={navigation}
          selectedSchool={selectedSchool ? {
            _id: selectedSchool.school_id,
            name: selectedSchool.school_name
          } : null}
          onSchoolChange={handleSchoolChange}
          onLogout={logout}
        >
          <ClassGradesSkeleton />
        </TeacherLayout>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute allowedRoles={["teacher"]}>
      <TeacherLayout
        navigation={navigation}
        selectedSchool={selectedSchool ? {
          _id: selectedSchool.school_id,
          name: selectedSchool.school_name
        } : null}
        onSchoolChange={handleSchoolChange}
        onLogout={logout}
      >
        <div className="space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-foreground">Manage Grades</h1>
              <p className="text-foreground/60">
                Select a class to view or enter grades.
                {currentTerm && (
                  <span className="ml-2 px-2 py-1 bg-teal/10 text-teal rounded-full text-xs">
                    {currentTerm.name} - {currentTerm.academic_year}
                  </span>
                )}
              </p>
            </div>
          </div>

          {/* Search */}
          <div className="bg-widget rounded-lg border border-stroke p-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-foreground/40 h-4 w-4" />
              <input
                type="text"
                placeholder="Search classes..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal focus:border-transparent bg-widget text-foreground"
              />
            </div>
          </div>

          {/* Classes Grid */}
          {filteredClasses.length === 0 ? (
            <div className="text-center py-12">
              <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {searchTerm ? "No classes found" : "No classes available"}
              </h3>
              <p className="text-gray-500 dark:text-gray-400">
                {searchTerm
                  ? "Try adjusting your search terms"
                  : "You don't have any classes assigned yet"
                }
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredClasses.map((cls) => (
                <motion.div
                  key={cls._id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.1 }}
                >
                  <ClassGradeCard
                    classId={cls._id}
                    className={cls.name}
                    classCode={cls.class_code}
                    studentCount={cls.studentCount}
                    lastTerm={currentTerm?.name || "No active term"}
                    onClick={() => handleClassClick(cls.class_id)}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </TeacherLayout>
    </ProtectedRoute>
  );
}
