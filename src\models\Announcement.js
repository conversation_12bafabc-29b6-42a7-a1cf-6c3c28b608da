const mongoose = require('mongoose');
// Define the schema for the Announcement model
const announcementSchema = new mongoose.Schema({
  announcement_id: {
    type: String,
    required: true, // Ensures that the announcement_id field is required
  },

  school_id:{
    type: mongoose.Schema.Types.ObjectId, // Reference to the School model
    ref: 'School',
    required: true,
  },

  title: {
    type: String,
    required: true,
  },

  content: {
    type: String,
    required: true,
  },

  // Keep the old field for backward compatibility
  announcement: {
    type: String,
  },

  author_id: {
    type: mongoose.Schema.Types.ObjectId, // Reference to the User model
    ref: 'User',
    required: true,
  },

  target_audience: {
    type: String,
    required: true,
    enum: ['all', 'teachers', 'parents', 'students'],
    default: 'all',
  },

  priority: {
    type: String,
    required: true,
    enum: ['low', 'medium', 'high', 'urgent'],
    default: 'medium',
  },

  is_published: {
    type: Boolean,
    default: false,
  },

  published_at: {
    type: Date,
  },

  expires_at: {
    type: Date,
  },
}, {
  timestamps: true
});

// Use the model if it's already defined, or create a new one
const Announcement = mongoose.models.Announcement || mongoose.model('Announcement', announcementSchema);

module.exports = Announcement;
